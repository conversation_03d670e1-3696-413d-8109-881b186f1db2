const { ccclass, property } = cc._decorator;

@ccclass
export default class AnimationTest extends cc.Component {

    @property(cc.Button)
    testGameStartBtn: cc.Button = null;

    @property(cc.Button)
    testRoundStartBtn: cc.Button = null;

    @property(cc.Label)
    statusLabel: cc.Label = null;

    onLoad() {
        // 设置按钮点击事件
        if (this.testGameStartBtn) {
            this.testGameStartBtn.node.on('click', this.testGameStartAnimation, this);
        }

        if (this.testRoundStartBtn) {
            this.testRoundStartBtn.node.on('click', this.testRoundStartAnimation, this);
        }
    }

    /**
     * 测试游戏开始动画
     */
    testGameStartAnimation() {
        this.updateStatus("测试游戏开始动画...");
        
        // 获取GamePageController实例
        const gamePageController = (window as any).gamePageController;
        if (gamePageController) {
            // 调用游戏开始动画
            if (gamePageController.showGameStartAnimation) {
                gamePageController.showGameStartAnimation();
                this.updateStatus("游戏开始动画已触发");
                
                // 3秒后隐藏
                this.scheduleOnce(() => {
                    if (gamePageController.hideGameStartAnimation) {
                        gamePageController.hideGameStartAnimation();
                        this.updateStatus("游戏开始动画已隐藏");
                    }
                }, 3);
            } else {
                this.updateStatus("GamePageController中没有找到showGameStartAnimation方法");
            }
        } else {
            this.updateStatus("未找到GamePageController实例");
        }
    }

    /**
     * 测试回合开始动画
     */
    testRoundStartAnimation() {
        this.updateStatus("测试回合开始动画...");
        
        // 获取GamePageController实例
        const gamePageController = (window as any).gamePageController;
        if (gamePageController) {
            // 调用回合开始动画
            if (gamePageController.showRoundStartAnimation) {
                gamePageController.showRoundStartAnimation();
                this.updateStatus("回合开始动画已触发");
            } else {
                this.updateStatus("GamePageController中没有找到showRoundStartAnimation方法");
            }
        } else {
            this.updateStatus("未找到GamePageController实例");
        }
    }

    /**
     * 更新状态显示
     */
    private updateStatus(message: string) {
        if (this.statusLabel) {
            this.statusLabel.string = message;
        }
        console.log(`[AnimationTest] ${message}`);
    }

    onDestroy() {
        if (this.testGameStartBtn) {
            this.testGameStartBtn.node.off('click', this.testGameStartAnimation, this);
        }
        if (this.testRoundStartBtn) {
            this.testRoundStartBtn.node.off('click', this.testRoundStartAnimation, this);
        }
    }
}
