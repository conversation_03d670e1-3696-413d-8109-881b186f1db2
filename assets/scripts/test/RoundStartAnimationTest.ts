// 回合开始动画测试脚本
// 这个脚本可以附加到任何节点上，用于测试回合开始动画

const { ccclass, property } = cc._decorator;

@ccclass
export default class RoundStartAnimationTest extends cc.Component {

    @property(cc.Button)
    testButton: cc.Button = null;

    private roundStartNode: cc.Node = null;

    onLoad() {
        console.log("=== RoundStartAnimationTest onLoad ===");
        
        // 创建测试按钮（如果没有在编辑器中设置）
        if (!this.testButton) {
            this.createTestButton();
        }

        // 创建回合开始节点
        this.createRoundStartNode();
        
        // 3秒后自动测试
        this.scheduleOnce(() => {
            console.log("3秒后自动测试动画");
            this.testAnimation();
        }, 3);
    }

    private createTestButton() {
        const buttonNode = new cc.Node("TestButton");
        buttonNode.addComponent(cc.Button);
        const sprite = buttonNode.addComponent(cc.Sprite);
        const label = new cc.Node("Label");
        label.addComponent(cc.Label).string = "测试动画";
        buttonNode.addChild(label);
        
        buttonNode.setPosition(0, 200);
        buttonNode.width = 200;
        buttonNode.height = 80;
        
        const canvas = cc.find("Canvas");
        if (canvas) {
            canvas.addChild(buttonNode);
            buttonNode.on('click', this.testAnimation, this);
        }
    }

    private createRoundStartNode() {
        console.log("创建回合开始测试节点");
        
        this.roundStartNode = new cc.Node('round_start_test_node');
        const canvas = cc.find('Canvas');
        if (canvas) {
            canvas.addChild(this.roundStartNode);
        }

        // 设置节点属性
        this.roundStartNode.setPosition(-750, -1);
        this.roundStartNode.zIndex = 1000;
        this.roundStartNode.width = 200;
        this.roundStartNode.height = 100;

        // 添加文本标签
        const label = this.roundStartNode.addComponent(cc.Label);
        label.string = "回合开始";
        label.fontSize = 48;
        label.node.color = cc.Color.WHITE;
        
        // 添加背景色
        const graphics = this.roundStartNode.addComponent(cc.Graphics);
        graphics.fillColor = cc.Color.RED;
        graphics.rect(-100, -50, 200, 100);
        graphics.fill();

        this.roundStartNode.active = false;
        console.log("回合开始测试节点创建完成");
    }

    private testAnimation() {
        console.log("开始测试回合开始动画");
        
        if (!this.roundStartNode) {
            console.error("回合开始节点不存在");
            return;
        }

        // 显示节点并开始动画
        this.roundStartNode.active = true;
        this.roundStartNode.setPosition(-750, -1);
        this.roundStartNode.opacity = 255;
        this.roundStartNode.scale = 1;

        console.log("节点状态 - 位置:", this.roundStartNode.position, "可见:", this.roundStartNode.active);

        // 执行动画：0.5秒移动到中间，1秒展示，0.5秒移动到右边
        cc.tween(this.roundStartNode)
            .to(0.5, { x: 0 }, { easing: 'quartOut' })
            .call(() => console.log("到达中间位置"))
            .delay(1.0)
            .call(() => console.log("开始移动到右边"))
            .to(0.5, { x: 750 }, { easing: 'quartIn' })
            .call(() => {
                console.log("动画完成，恢复初始状态");
                this.roundStartNode.setPosition(-750, -1);
                this.roundStartNode.active = false;
            })
            .start();
    }

    start() {
        if (this.testButton) {
            this.testButton.node.on('click', this.testAnimation, this);
        }
    }
}
