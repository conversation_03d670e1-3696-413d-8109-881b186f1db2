// 游戏页面初始化器 - 确保GamePageController被正确初始化
// 这个脚本可以附加到任何节点上，它会自动创建和初始化GamePageController

import GamePageController from "./GamePageController";

const { ccclass, property } = cc._decorator;

@ccclass
export default class GamePageInitializer extends cc.Component {

    onLoad() {
        console.log("=== GamePageInitializer onLoad 开始 ===");
        
        // 检查是否已经有GamePageController实例
        if ((window as any).gamePageController) {
            console.log("GamePageController 实例已存在");
            return;
        }

        // 查找或创建GamePageController
        this.initializeGamePageController();
        
        console.log("=== GamePageInitializer onLoad 完成 ===");
    }

    private initializeGamePageController() {
        // 首先尝试在场景中查找现有的GamePageController
        const existingController = cc.find("Canvas").getComponent(GamePageController);
        if (existingController) {
            console.log("找到现有的GamePageController");
            (window as any).gamePageController = existingController;
            return;
        }

        // 如果没有找到，创建一个新的节点并添加GamePageController
        console.log("创建新的GamePageController");
        const gamePageNode = new cc.Node("GamePageController");
        const canvas = cc.find("Canvas");
        if (canvas) {
            canvas.addChild(gamePageNode);
            const controller = gamePageNode.addComponent(GamePageController);
            (window as any).gamePageController = controller;
            console.log("GamePageController 创建并初始化完成");
            
            // 暴露测试方法
            (window as any).testRoundAnimation = () => {
                console.log("全局测试方法被调用");
                controller.testRoundStartAnimation();
            };
        } else {
            console.error("找不到Canvas节点");
        }
    }
}
