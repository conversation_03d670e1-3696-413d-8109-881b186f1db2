// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import { RoomUser } from "../bean/GameBean";
import { Tools } from "../util/Tools";

const {ccclass, property} = cc._decorator;

@ccclass
export default class PlayerGameController extends cc.Component {

    @property(cc.Node)
    avatar: cc.Node = null;   //头像
    @property(cc.Node)
    flagNode: cc.Node = null;  //旗子节点
    @property(cc.Node)
    addScoreNode: cc.Node = null;  //加分背景节点 addscore
    @property(cc.Node)
    subScoreNode: cc.Node = null;  //减分背景节点 deductscore

 

    start () {
    
    }

    setData(user: RoomUser){
        this.scheduleOnce(() => {
            if (user == null) {
                this.avatar.active = false;
            } else {
                Tools.setNodeSpriteFrameUrl(this.avatar, user.avatar);//添加头像
                this.avatar.active = true;
            }
        }, 0.1);
    }

   

    /**
     * 显示加分效果，带动画
     * @param addValue 加分数值
     */
    showAddScore(addValue: number) {


        if (this.addScoreNode) {
            // 获取change_score文本节点并设置加分文本
            const changeScoreLabel = this.addScoreNode.getChildByName("change_score");
            if (changeScoreLabel) {
                const labelComponent = changeScoreLabel.getComponent(cc.Label);
                if (labelComponent) {
                    labelComponent.string = "+" + addValue.toString();

                }
            } else {
                console.warn("PlayerGame addScoreNode中找不到change_score子节点");
            }

            // 设置最高层级，确保不被头像遮挡
            this.addScoreNode.zIndex = cc.macro.MAX_ZINDEX - 1;

            // 停止之前的动画
            cc.Tween.stopAllByTarget(this.addScoreNode);

            // 重置节点状态
            this.addScoreNode.active = true;
            this.addScoreNode.opacity = 0;
            this.addScoreNode.scale = 0.8;

            // 保存原始位置
            let originalY = this.addScoreNode.y;

            // 使用新的Tween API
            cc.tween(this.addScoreNode)
                .parallel(
                    cc.tween().to(0.15, { opacity: 255 }),
                    cc.tween().to(0.15, { scale: 1.1 }),
                    cc.tween().by(0.15, { y: 15 })
                )
                .to(0.1, { scale: 1.0 })
                .delay(0.8)
                .parallel(
                    cc.tween().to(0.25, { opacity: 0 }),
                    cc.tween().to(0.25, { scale: 0.9 }),
                    cc.tween().by(0.25, { y: 8 })
                )
                .call(() => {
                    this.addScoreNode.active = false;
                    this.addScoreNode.opacity = 255;
                    this.addScoreNode.scale = 1.0;
                    this.addScoreNode.y = originalY;
                 
                })
                .start();
        } else {
            console.warn("PlayerGame addScoreNode未设置");
        }
    }

    /**
     * 显示减分效果
     * @param subValue 减分数值
     */
    showSubScore(subValue: number) {


        if (this.subScoreNode) {
            // 获取change_score文本节点并设置减分文本
            const changeScoreLabel = this.subScoreNode.getChildByName("change_score");
            if (changeScoreLabel) {
                const labelComponent = changeScoreLabel.getComponent(cc.Label);
                if (labelComponent) {
                    labelComponent.string = "-" + subValue.toString();

                }
            } else {
                console.warn("PlayerGame subScoreNode中找不到change_score子节点");
            }

            // 设置最高层级，确保不被头像遮挡
            this.subScoreNode.zIndex = cc.macro.MAX_ZINDEX - 1;

            this.subScoreNode.active = true;

            // 1秒后隐藏
            this.scheduleOnce(() => {
                if (this.subScoreNode) {
                    this.subScoreNode.active = false;
                   
                }
            }, 1.0);
        } else {
            console.warn("PlayerGame subScoreNode未设置");
        }
    }

    // 隐藏加减分节点
    hideScoreEffects() {
        if (this.addScoreNode) {
            this.addScoreNode.active = false;
        }
        if (this.subScoreNode) {
            this.subScoreNode.active = false;
        }
    }

    // update (dt) {}
}
