{"version": 3, "sources": ["assets/scripts/hall/HallCenterLayController.ts"], "names": [], "mappings": ";;;;;AAAA,oBAAoB;AACpB,4EAA4E;AAC5E,mBAAmB;AACnB,sFAAsF;AACtF,8BAA8B;AAC9B,sFAAsF;;;;;;;;;;;;;;;;;;;;;;AAGtF,iDAAgD;AAChD,8CAA6C;AAC7C,4DAA2D;AAC3D,sDAAiD;AACjD,2DAAsD;AACtD,uEAAkE;AAClE,mEAA8D;AAExD,IAAA,KAAwB,EAAE,CAAC,UAAU,EAAnC,OAAO,aAAA,EAAE,QAAQ,cAAkB,CAAC;AAG5C,IAAY,gBAIX;AAJD,WAAY,gBAAgB;IACxB,2EAAc,CAAA;IACd,uFAAoB,CAAA;IACpB,qFAAmB,CAAA;AACvB,CAAC,EAJW,gBAAgB,GAAhB,wBAAgB,KAAhB,wBAAgB,QAI3B;AAGD;IAAqD,2CAAY;IAAjE;QAAA,qEAgKC;QA7JG,kBAAY,GAAY,IAAI,CAAC,CAAE,UAAU;QAEzC,wBAAkB,GAAY,IAAI,CAAC,CAAC,WAAW;QAE/C,sBAAgB,GAAY,IAAI,CAAC,CAAC,YAAY;QAE9C,qBAAe,GAAoB,IAAI,CAAA,CAAE,WAAW;QAEpD,wBAAkB,GAAuB,IAAI,CAAA;QAC7C,8BAAwB,GAA6B,IAAI,CAAA;QACzD,4BAAsB,GAA2B,IAAI,CAAA;QAErD,sBAAgB,GAAqB,IAAI,CAAA,CAAC,eAAe;;QAgJzD,iBAAiB;IACrB,CAAC;IA5IG,wCAAM,GAAN;QACI,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,4BAAkB,CAAC,CAAA;QAC5E,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,kCAAwB,CAAC,CAAA;QAC9F,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,gCAAsB,CAAC,CAAA;IAE5F,CAAC;IACS,0CAAQ,GAAlB;QACI,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAA;IAC7D,CAAC;IAED,uCAAK,GAAL;QAAA,iBA0CC;QAzCG,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC;YACnC,eAAe;YACf,IAAI,KAAI,CAAC,UAAU,EAAE;gBACjB,KAAI,CAAC,UAAU,EAAE,CAAA;aACpB;QACL,CAAC,EAAE;YACC,gBAAgB;YAChB,IAAI,KAAI,CAAC,WAAW,EAAE;gBAClB,KAAI,CAAC,WAAW,EAAE,CAAA;aACrB;QAEL,CAAC,EAAE;YACC,cAAc;YACd,KAAI,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAA;QAClE,CAAC,CAAC,CAAA;QAGF,IAAI,CAAC,wBAAwB,CAAC,QAAQ,CAAC,UAAC,MAAc,EAAE,QAAgB;YACpE,WAAW;YACX,IAAI,KAAI,CAAC,YAAY,EAAE;gBACnB,KAAI,CAAC,YAAY,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAA;aACtC;QACL,CAAC,EAAE;YACC,cAAc;YAEd,WAAW;YACX,mCAAgB,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,qBAAS,CAAC,kBAAkB,EAAE,EAAE,CAAC,CAAC;QAC7E,CAAC,EAAE;YACC,UAAU;YACV,WAAW;YACX,mCAAgB,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,qBAAS,CAAC,kBAAkB,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;QAC5F,CAAC,EAAE;YACC,YAAY;YACZ,aAAa;YACb,mCAAgB,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,qBAAS,CAAC,kBAAkB,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC;QAC7F,CAAC,CAAE,CAAA;QAEH,IAAI,CAAC,sBAAsB,CAAC,cAAc,CAAC,UAAC,UAAkB;YAC1D,SAAS;YACT,mCAAgB,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,qBAAS,CAAC,mBAAmB,EAAE,EAAE,YAAY,EAAE,MAAM,CAAC,UAAU,CAAC,EAAC,CAAC,CAAA;QAC9G,CAAC,CAAC,CAAA;IACN,CAAC;IAED,cAAc;IACd,qDAAmB,GAAnB,UAAoB,gBAAkC;QAClD,IAAI,IAAI,CAAC,gBAAgB,KAAK,gBAAgB,EAAE;YAC5C,OAAM;SACT;QACD,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAA;QACxC,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,KAAK,CAAA;QAChC,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAG,KAAK,CAAA;QACtC,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,KAAK,CAAA;QAEpC,QAAQ,gBAAgB,EAAE;YACtB,KAAK,gBAAgB,CAAC,cAAc;gBAChC,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAA;gBAC/B,MAAK;YACT,KAAK,gBAAgB,CAAC,oBAAoB;gBACtC,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAG,IAAI,CAAA;gBACrC,MAAK;YACT,KAAK,gBAAgB,CAAC,mBAAmB;gBACrC,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,IAAI,CAAA;gBACnC,MAAK;SAEZ;IACL,CAAC;IAED,eAAe;IACf,qDAAmB,GAAnB;QACI,OAAO,IAAI,CAAC,gBAAgB,CAAA;IAChC,CAAC;IAED,WAAW;IACX,0CAAQ,GAAR,UAAS,UAAoB,EAAE,WAAqB,EAAE,YAAsB;QACxE,IAAI,CAAC,UAAU,GAAG,UAAU,CAAA;QAC5B,IAAI,CAAC,WAAW,GAAG,WAAW,CAAA;QAC9B,IAAI,CAAC,YAAY,GAAG,YAAY,CAAA;IACpC,CAAC;IAED,UAAU;IACV,iDAAe,GAAf,UAAgB,YAA0B;QAEtC,eAAe;QACf,IAAI,IAAI,CAAC,gBAAgB,IAAI,gBAAgB,CAAC,mBAAmB,EAAE;YAC/D,UAAU;YACV,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,oBAAoB,CAAC,CAAA;SAClE;aAAM,IAAI,IAAI,CAAC,gBAAgB,IAAI,gBAAgB,CAAC,oBAAoB,EAAE;YACvE,iBAAiB;YACjB,IAAI,CAAC,wBAAwB,CAAC,aAAa,CAAC,YAAY,CAAC,UAAU,CAAC,CAAA;SACvE;aAAM;YACH,cAAc;YACd,UAAU;YACV,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,oBAAoB,CAAC,CAAA;SAClE;IACL,CAAC;IACD,MAAM;IACN,2CAAS,GAAT,UAAU,iBAAoC;QAC1C,mCAAmC;QACnC,IAAI,iBAAiB,CAAC,MAAM,KAAK,uBAAU,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,IAAI,iBAAiB,CAAC,SAAS,EAAE;YAEhH,IAAI,iBAAiB,CAAC,MAAM,IAAI,uBAAU,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,EAAE;gBAChF,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,MAAM,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC,CAAA;aACxE;YACD,8BAA8B;YAC9B,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAA;SAC5D;aAAM;YACH,MAAM;YACN,IAAI,CAAC,wBAAwB,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAA;SAC/D;IACL,CAAC;IAED,MAAM;IACN,yCAAO,GAAP;QACI,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAA;IACrC,CAAC;IAED,QAAQ;IACR,gDAAc,GAAd;QACI,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,oBAAoB,CAAC,CAAA;IACnE,CAAC;IACD,2CAAS,GAAT;QACI,IAAI,CAAC,sBAAsB,CAAC,SAAS,EAAE,CAAA;IAC3C,CAAC;IACD,SAAS;IACT,+CAAa,GAAb,UAAc,sBAA8C;QACxD,IAAI,CAAC,wBAAwB,CAAC,aAAa,CAAC,sBAAsB,CAAC,CAAA;IACvE,CAAC;IA1JD;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;iEACW;IAE7B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;uEACiB;IAEnC;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;qEACe;IAEjC;QADC,QAAQ,CAAC,yBAAe,CAAC;oEACa;IATtB,uBAAuB;QAD3C,OAAO;OACa,uBAAuB,CAgK3C;IAAD,8BAAC;CAhKD,AAgKC,CAhKoD,EAAE,CAAC,SAAS,GAgKhE;kBAhKoB,uBAAuB", "file": "", "sourceRoot": "/", "sourcesContent": ["// Learn TypeScript:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html\n// Learn Attribute:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html\n// Learn life-cycle callbacks:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html\n\nimport { AcceptInvite, NoticeLeaveInvite, NoticeUserInviteStatus } from \"../bean/GameBean\";\nimport { GlobalBean } from \"../bean/GlobalBean\";\nimport { MessageId } from \"../net/MessageId\";\nimport { WebSocketManager } from \"../net/WebSocketManager\";\nimport ToastController from \"../ToastController\";\nimport HallAutoController from \"./HallAutoController\";\nimport HallCreateRoomController from \"./HallCreateRoomController\";\nimport HallJoinRoomController from \"./HallJoinRoomController\";\n\nconst { ccclass, property } = cc._decorator;\n\n\nexport enum CenterLaytouType {\n    HALL_AUTO_VIEW,\n    HALL_CREAT_ROOM_VIEW,\n    HALL_JOIN_ROOM_VIEW,\n}\n\n@ccclass\nexport default class HallCenterLayController extends cc.Component {\n\n    @property(cc.Node)\n    hallAutoView: cc.Node = null;  //快速开始view\n    @property(cc.Node)\n    hallCreateRoomView: cc.Node = null; //创建房间 view\n    @property(cc.Node)\n    hallJoinRoomView: cc.Node = null; //加入房间的 view\n    @property(ToastController)\n    toastController: ToastController = null  //toast 的布局\n\n    hallAutoController: HallAutoController = null\n    hallCreateRoomController: HallCreateRoomController = null\n    hallJoinRoomController: HallJoinRoomController = null\n\n    centerLaytouType: CenterLaytouType = null //当前展示的哪一个 view\n    startClick: Function\n    createClick: Function\n    seatCallback: Function\n\n    onLoad() {\n        this.hallAutoController = this.hallAutoView.getComponent(HallAutoController)\n        this.hallCreateRoomController = this.hallCreateRoomView.getComponent(HallCreateRoomController)\n        this.hallJoinRoomController = this.hallJoinRoomView.getComponent(HallJoinRoomController)\n    \n    }\n    protected onEnable(): void {\n        this.setCenterLaytouType(CenterLaytouType.HALL_AUTO_VIEW)\n    }\n\n    start() {\n        this.hallAutoController.setButtonClick(() => {\n            //start 按钮的点击回调\n            if (this.startClick) {\n                this.startClick()\n            }\n        }, () => {\n            //create 按钮的点击回调\n            if (this.createClick) {\n                this.createClick()\n            }\n\n        }, () => {\n            //join 按钮的点击回调\n            this.setCenterLaytouType(CenterLaytouType.HALL_JOIN_ROOM_VIEW)\n        })\n\n\n        this.hallCreateRoomController.setClick((userId: string, nickname: string) => {\n            //房间内点击玩家头像\n            if (this.seatCallback) {\n                this.seatCallback(userId, nickname)\n            }\n        }, () => {\n            //点击 start 的回调\n\n            //发送开始游戏的消息\n            WebSocketManager.GetInstance().sendMsg(MessageId.MsgTypeInviteStart, {});\n        }, () => {\n            //ready的回调\n            //发送游戏准备的消息\n            WebSocketManager.GetInstance().sendMsg(MessageId.MsgTypeInviteReady, { 'ready': true });\n        }, () => {\n            //cancel 的回调\n            //发送取消游戏准备的消息\n            WebSocketManager.GetInstance().sendMsg(MessageId.MsgTypeInviteReady, { 'ready': false });\n        },)\n\n        this.hallJoinRoomController.setButtonClick((inviteCode: string) => {\n            //加入房间的按钮\n            WebSocketManager.GetInstance().sendMsg(MessageId.MsgTypeAcceptInvite, { 'inviteCode': Number(inviteCode)})\n        })\n    }\n\n    //设置显示哪一个 view\n    setCenterLaytouType(centerLaytouType: CenterLaytouType) {\n        if (this.centerLaytouType === centerLaytouType) {\n            return\n        }\n        this.centerLaytouType = centerLaytouType\n        this.hallAutoView.active = false\n        this.hallCreateRoomView.active = false\n        this.hallJoinRoomView.active = false\n\n        switch (centerLaytouType) {\n            case CenterLaytouType.HALL_AUTO_VIEW:\n                this.hallAutoView.active = true\n                break\n            case CenterLaytouType.HALL_CREAT_ROOM_VIEW:\n                this.hallCreateRoomView.active = true\n                break\n            case CenterLaytouType.HALL_JOIN_ROOM_VIEW:\n                this.hallJoinRoomView.active = true\n                break\n\n        }\n    }\n\n    //获取当前正在显示的那个页面\n    getCenterLaytouType() {\n        return this.centerLaytouType\n    }\n\n    //设置按钮的点击回调\n    setClick(startClick: Function, createClick: Function, seatCallback: Function) {\n        this.startClick = startClick\n        this.createClick = createClick\n        this.seatCallback = seatCallback\n    }\n\n    //设置接受邀请成功\n    setAcceptInvite(acceptInvite: AcceptInvite) {\n\n        //如果正在，接受邀请页面的话\n        if (this.centerLaytouType == CenterLaytouType.HALL_JOIN_ROOM_VIEW) {\n            //进入创建房间页面\n            this.setCenterLaytouType(CenterLaytouType.HALL_CREAT_ROOM_VIEW)\n        } else if (this.centerLaytouType == CenterLaytouType.HALL_CREAT_ROOM_VIEW) {\n            //如果已经在房间页面，就刷新数据\n            this.hallCreateRoomController.refreshPlayer(acceptInvite.inviteInfo)\n        } else {\n            //还在大厅收到重链接的消息\n            //进入创建房间页面\n            this.setCenterLaytouType(CenterLaytouType.HALL_CREAT_ROOM_VIEW)\n        }\n    }\n    //离开房间\n    leaveRoom(noticeLeaveInvite: NoticeLeaveInvite) {\n        //判断是不是自己离开了房间，或者是房主离开（房主离开就是解散房间了）\n        if (noticeLeaveInvite.userId === GlobalBean.GetInstance().loginData.userInfo.userId || noticeLeaveInvite.isCreator) {\n\n            if (noticeLeaveInvite.userId != GlobalBean.GetInstance().loginData.userInfo.userId) {\n                this.toastController.showContent(window.getLocalizedStr('LeaveRoom'))\n            }\n            //当前展示的是加入房间 或者创建房间的话  返回键返回大厅\n            this.setCenterLaytouType(CenterLaytouType.HALL_AUTO_VIEW)\n        } else {\n            //刷新数据\n            this.hallCreateRoomController.leavePlayer(noticeLeaveInvite)\n        }\n    }\n\n    //设置门票\n    setFees() {\n        this.hallAutoController.setFees()\n    }\n\n    //进入私人房间\n    joinCreateRoom() {\n        this.setCenterLaytouType(CenterLaytouType.HALL_CREAT_ROOM_VIEW)\n    }\n    joinError() {\n        this.hallJoinRoomController.joinError()\n    }\n    //准备 取消准备\n    setReadyState(noticeUserInviteStatus: NoticeUserInviteStatus) {\n        this.hallCreateRoomController.setReadyState(noticeUserInviteStatus)\n    }\n\n    // update (dt) {}\n}\n"]}