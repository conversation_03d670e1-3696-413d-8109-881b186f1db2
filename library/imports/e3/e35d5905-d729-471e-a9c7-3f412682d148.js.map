{"version": 3, "sources": ["assets/scripts/hall/Level/ScrollViewHelper.ts"], "names": [], "mappings": ";;;;;AAAA,oBAAoB;AACpB,4EAA4E;;;AAE5E;;;GAGG;AACH;IAAA;IA6NA,CAAC;IA3NG;;;OAGG;IACI,sCAAqB,GAA5B,UAA6B,UAAyB;QAClD,IAAI,CAAC,UAAU,EAAE;YACb,EAAE,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;YAC3C,OAAO;SACV;QAED,gBAAgB;QAChB,UAAU,CAAC,mBAAmB,GAAG,IAAI,CAAC;QACtC,UAAU,CAAC,iBAAiB,GAAG,IAAI,CAAC;IAGxC,CAAC;IAED;;OAEG;IACI,0CAAyB,GAAhC,UAAiC,UAAyB,EAAE,OAAgB;QACxE,IAAI,CAAC,UAAU,IAAI,CAAC,OAAO,EAAE;YACzB,EAAE,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAC;YAC1C,OAAO;SACV;QAED,OAAO;QACP,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;QAC7B,UAAU,CAAC,UAAU,GAAG,IAAI,CAAC;QAC7B,UAAU,CAAC,QAAQ,GAAG,KAAK,CAAC;QAC5B,UAAU,CAAC,OAAO,GAAG,IAAI,CAAC;QAC1B,UAAU,CAAC,OAAO,GAAG,IAAI,CAAC;QAC1B,UAAU,CAAC,cAAc,GAAG,GAAG,CAAC;QAEhC,gBAAgB;QAChB,IAAI,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;IAG3C,CAAC;IAED;;OAEG;IACI,wCAAuB,GAA9B,UAA+B,UAAyB,EAAE,OAAgB;QACtE,IAAI,CAAC,UAAU,IAAI,CAAC,OAAO,EAAE;YACzB,EAAE,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAC;YAC1C,OAAO;SACV;QAED,OAAO;QACP,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;QAC7B,UAAU,CAAC,UAAU,GAAG,KAAK,CAAC;QAC9B,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC;QAC3B,UAAU,CAAC,OAAO,GAAG,IAAI,CAAC;QAC1B,UAAU,CAAC,OAAO,GAAG,IAAI,CAAC;QAC1B,UAAU,CAAC,cAAc,GAAG,GAAG,CAAC;QAEhC,gBAAgB;QAChB,IAAI,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;QAEvC,EAAE,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;IAC1D,CAAC;IAED;;OAEG;IACI,2CAA0B,GAAjC,UAAkC,MAAe,EAAE,IAAa;QAK5D,iBAAiB;QACjB,IAAM,cAAc,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACjD,IAAM,UAAU,GAAG,cAAc,CAAC,YAAY,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC;QAC9D,cAAc,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QACpC,cAAc,CAAC,MAAM,GAAG,MAAM,CAAC;QAE/B,eAAe;QACf,IAAM,QAAQ,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACzC,IAAM,IAAI,GAAG,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;QAC5C,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAC9B,QAAQ,CAAC,MAAM,GAAG,cAAc,CAAC;QAEjC,cAAc;QACd,IAAM,OAAO,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACvC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAC7B,OAAO,CAAC,MAAM,GAAG,QAAQ,CAAC;QAE1B,eAAe;QACf,IAAI,CAAC,yBAAyB,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAEpD,OAAO,EAAE,UAAU,YAAA,EAAE,QAAQ,UAAA,EAAE,OAAO,SAAA,EAAE,CAAC;IAC7C,CAAC;IAED;;OAEG;IACI,yCAAwB,GAA/B,UAAgC,MAAe,EAAE,IAAa;QAK1D,iBAAiB;QACjB,IAAM,cAAc,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACjD,IAAM,UAAU,GAAG,cAAc,CAAC,YAAY,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC;QAC9D,cAAc,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QACpC,cAAc,CAAC,MAAM,GAAG,MAAM,CAAC;QAE/B,eAAe;QACf,IAAM,QAAQ,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACzC,IAAM,IAAI,GAAG,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;QAC5C,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAC9B,QAAQ,CAAC,MAAM,GAAG,cAAc,CAAC;QAEjC,cAAc;QACd,IAAM,OAAO,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACvC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAC7B,OAAO,CAAC,MAAM,GAAG,QAAQ,CAAC;QAE1B,eAAe;QACf,IAAI,CAAC,uBAAuB,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAElD,OAAO,EAAE,UAAU,YAAA,EAAE,QAAQ,UAAA,EAAE,OAAO,SAAA,EAAE,CAAC;IAC7C,CAAC;IAED;;OAEG;IACI,0CAAyB,GAAhC,UAAiC,UAAyB,EAAE,OAAe,EAAE,QAAsB;QAAtB,yBAAA,EAAA,cAAsB;QAC/F,IAAI,CAAC,UAAU,EAAE;YACb,EAAE,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC;YAC/B,OAAO;SACV;QAED,IAAM,cAAc,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACrD,UAAU,CAAC,yBAAyB,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;IACnE,CAAC;IAED;;OAEG;IACI,wCAAuB,GAA9B,UAA+B,UAAyB,EAAE,OAAe,EAAE,QAAsB;QAAtB,yBAAA,EAAA,cAAsB;QAC7F,IAAI,CAAC,UAAU,EAAE;YACb,EAAE,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC;YAC/B,OAAO;SACV;QAED,IAAM,cAAc,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACrD,UAAU,CAAC,uBAAuB,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;IACjE,CAAC;IAED;;OAEG;IACI,2CAA0B,GAAjC,UAAkC,UAAyB;QACvD,IAAI,CAAC,UAAU,EAAE;YACb,EAAE,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC;YAC/B,OAAO,CAAC,CAAC;SACZ;QAED,IAAM,MAAM,GAAG,UAAU,CAAC,eAAe,EAAE,CAAC;QAC5C,IAAM,eAAe,GAAG,UAAU,CAAC,kBAAkB,EAAE,CAAC;QAExD,IAAI,eAAe,CAAC,CAAC,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAEtC,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;IAC5D,CAAC;IAED;;OAEG;IACI,yCAAwB,GAA/B,UAAgC,UAAyB;QACrD,IAAI,CAAC,UAAU,EAAE;YACb,EAAE,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC;YAC/B,OAAO,CAAC,CAAC;SACZ;QAED,IAAM,MAAM,GAAG,UAAU,CAAC,eAAe,EAAE,CAAC;QAC5C,IAAM,eAAe,GAAG,UAAU,CAAC,kBAAkB,EAAE,CAAC;QAExD,IAAI,eAAe,CAAC,CAAC,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAEtC,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;IAC5D,CAAC;IAED;;OAEG;IACI,0BAAS,GAAhB,UAAiB,UAAyB;QACtC,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE;YACpC,OAAO,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;SACjD;QAED,IAAM,QAAQ,GAAG,UAAU,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;QAClD,IAAM,WAAW,GAAG,UAAU,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAExD,OAAO;YACH,UAAU,EAAE,WAAW,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK;YAC9C,QAAQ,EAAE,WAAW,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM;SACjD,CAAC;IACN,CAAC;IAED;;OAEG;IACI,gCAAe,GAAtB,UAAuB,UAAyB,EAAE,IAA2B;QAA3B,qBAAA,EAAA,mBAA2B;QACzE,IAAI,CAAC,UAAU,EAAE;YACb,EAAE,CAAC,GAAG,CAAI,IAAI,yBAAsB,CAAC,CAAC;YACtC,OAAO;SACV;QAED,IAAM,QAAQ,GAAG,UAAU,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;QAClD,IAAM,WAAW,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC;QAC5F,IAAM,MAAM,GAAG,UAAU,CAAC,eAAe,EAAE,CAAC;QAC5C,IAAM,SAAS,GAAG,UAAU,CAAC,kBAAkB,EAAE,CAAC;QAClD,IAAM,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;IAGrD,CAAC;IACL,uBAAC;AAAD,CA7NA,AA6NC,IAAA;AA7NY,4CAAgB", "file": "", "sourceRoot": "/", "sourcesContent": ["// Learn TypeScript:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html\n\n/**\n * ScrollView辅助工具类\n * 用于解决ScrollView常见的配置问题\n */\nexport class ScrollViewHelper {\n    \n    /**\n     * 修复ScrollView的Scrollbar引用问题\n     * 这个方法会清除ScrollView的Scrollbar引用，避免\"Scrollbar referenced by component is invalid\"错误\n     */\n    static fixScrollbarReference(scrollView: cc.ScrollView) {\n        if (!scrollView) {\n            cc.warn(\"ScrollView is null or undefined\");\n            return;\n        }\n\n        // 清除Scrollbar引用\n        scrollView.horizontalScrollBar = null;\n        scrollView.verticalScrollBar = null;\n        \n      \n    }\n\n    /**\n     * 配置水平滚动的ScrollView\n     */\n    static setupHorizontalScrollView(scrollView: cc.ScrollView, content: cc.Node) {\n        if (!scrollView || !content) {\n            cc.error(\"ScrollView or content is null\");\n            return;\n        }\n\n        // 基本配置\n        scrollView.content = content;\n        scrollView.horizontal = true;\n        scrollView.vertical = false;\n        scrollView.inertia = true;\n        scrollView.elastic = true;\n        scrollView.bounceDuration = 0.3;\n        \n        // 清除Scrollbar引用\n        this.fixScrollbarReference(scrollView);\n        \n     \n    }\n\n    /**\n     * 配置垂直滚动的ScrollView\n     */\n    static setupVerticalScrollView(scrollView: cc.ScrollView, content: cc.Node) {\n        if (!scrollView || !content) {\n            cc.error(\"ScrollView or content is null\");\n            return;\n        }\n\n        // 基本配置\n        scrollView.content = content;\n        scrollView.horizontal = false;\n        scrollView.vertical = true;\n        scrollView.inertia = true;\n        scrollView.elastic = true;\n        scrollView.bounceDuration = 0.3;\n        \n        // 清除Scrollbar引用\n        this.fixScrollbarReference(scrollView);\n        \n        cc.log(\"Vertical ScrollView configured successfully\");\n    }\n\n    /**\n     * 创建一个完整的水平ScrollView结构\n     */\n    static createHorizontalScrollView(parent: cc.Node, size: cc.Size): {\n        scrollView: cc.ScrollView,\n        viewport: cc.Node,\n        content: cc.Node\n    } {\n        // 创建ScrollView节点\n        const scrollViewNode = new cc.Node(\"ScrollView\");\n        const scrollView = scrollViewNode.addComponent(cc.ScrollView);\n        scrollViewNode.setContentSize(size);\n        scrollViewNode.parent = parent;\n\n        // 创建Viewport节点\n        const viewport = new cc.Node(\"Viewport\");\n        const mask = viewport.addComponent(cc.Mask);\n        viewport.setContentSize(size);\n        viewport.parent = scrollViewNode;\n\n        // 创建Content节点\n        const content = new cc.Node(\"Content\");\n        content.setContentSize(size);\n        content.parent = viewport;\n\n        // 配置ScrollView\n        this.setupHorizontalScrollView(scrollView, content);\n\n        return { scrollView, viewport, content };\n    }\n\n    /**\n     * 创建一个完整的垂直ScrollView结构\n     */\n    static createVerticalScrollView(parent: cc.Node, size: cc.Size): {\n        scrollView: cc.ScrollView,\n        viewport: cc.Node,\n        content: cc.Node\n    } {\n        // 创建ScrollView节点\n        const scrollViewNode = new cc.Node(\"ScrollView\");\n        const scrollView = scrollViewNode.addComponent(cc.ScrollView);\n        scrollViewNode.setContentSize(size);\n        scrollViewNode.parent = parent;\n\n        // 创建Viewport节点\n        const viewport = new cc.Node(\"Viewport\");\n        const mask = viewport.addComponent(cc.Mask);\n        viewport.setContentSize(size);\n        viewport.parent = scrollViewNode;\n\n        // 创建Content节点\n        const content = new cc.Node(\"Content\");\n        content.setContentSize(size);\n        content.parent = viewport;\n\n        // 配置ScrollView\n        this.setupVerticalScrollView(scrollView, content);\n\n        return { scrollView, viewport, content };\n    }\n\n    /**\n     * 滚动到指定位置（水平）\n     */\n    static scrollToHorizontalPercent(scrollView: cc.ScrollView, percent: number, duration: number = 0.3) {\n        if (!scrollView) {\n            cc.error(\"ScrollView is null\");\n            return;\n        }\n\n        const clampedPercent = cc.misc.clampf(percent, 0, 1);\n        scrollView.scrollToPercentHorizontal(clampedPercent, duration);\n    }\n\n    /**\n     * 滚动到指定位置（垂直）\n     */\n    static scrollToVerticalPercent(scrollView: cc.ScrollView, percent: number, duration: number = 0.3) {\n        if (!scrollView) {\n            cc.error(\"ScrollView is null\");\n            return;\n        }\n\n        const clampedPercent = cc.misc.clampf(percent, 0, 1);\n        scrollView.scrollToPercentVertical(clampedPercent, duration);\n    }\n\n    /**\n     * 获取当前滚动位置（水平）\n     */\n    static getHorizontalScrollPercent(scrollView: cc.ScrollView): number {\n        if (!scrollView) {\n            cc.error(\"ScrollView is null\");\n            return 0;\n        }\n\n        const offset = scrollView.getScrollOffset();\n        const maxScrollOffset = scrollView.getMaxScrollOffset();\n        \n        if (maxScrollOffset.x === 0) return 0;\n        \n        return Math.abs(offset.x) / Math.abs(maxScrollOffset.x);\n    }\n\n    /**\n     * 获取当前滚动位置（垂直）\n     */\n    static getVerticalScrollPercent(scrollView: cc.ScrollView): number {\n        if (!scrollView) {\n            cc.error(\"ScrollView is null\");\n            return 0;\n        }\n\n        const offset = scrollView.getScrollOffset();\n        const maxScrollOffset = scrollView.getMaxScrollOffset();\n        \n        if (maxScrollOffset.y === 0) return 0;\n        \n        return Math.abs(offset.y) / Math.abs(maxScrollOffset.y);\n    }\n\n    /**\n     * 检查ScrollView是否可以滚动\n     */\n    static canScroll(scrollView: cc.ScrollView): { horizontal: boolean, vertical: boolean } {\n        if (!scrollView || !scrollView.content) {\n            return { horizontal: false, vertical: false };\n        }\n\n        const viewSize = scrollView.node.getContentSize();\n        const contentSize = scrollView.content.getContentSize();\n\n        return {\n            horizontal: contentSize.width > viewSize.width,\n            vertical: contentSize.height > viewSize.height\n        };\n    }\n\n    /**\n     * 调试ScrollView信息\n     */\n    static debugScrollView(scrollView: cc.ScrollView, name: string = \"ScrollView\") {\n        if (!scrollView) {\n            cc.log(`${name}: ScrollView is null`);\n            return;\n        }\n\n        const viewSize = scrollView.node.getContentSize();\n        const contentSize = scrollView.content ? scrollView.content.getContentSize() : cc.Size.ZERO;\n        const offset = scrollView.getScrollOffset();\n        const maxOffset = scrollView.getMaxScrollOffset();\n        const canScrollInfo = this.canScroll(scrollView);\n\n   \n    }\n}\n"]}