"use strict";
cc._RF.push(module, 'e35d5kF1ylHHqnHP0EmgtFI', 'ScrollViewHelper');
// scripts/hall/Level/ScrollViewHelper.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
Object.defineProperty(exports, "__esModule", { value: true });
exports.ScrollViewHelper = void 0;
/**
 * ScrollView辅助工具类
 * 用于解决ScrollView常见的配置问题
 */
var ScrollViewHelper = /** @class */ (function () {
    function ScrollViewHelper() {
    }
    /**
     * 修复ScrollView的Scrollbar引用问题
     * 这个方法会清除ScrollView的Scrollbar引用，避免"Scrollbar referenced by component is invalid"错误
     */
    ScrollViewHelper.fixScrollbarReference = function (scrollView) {
        if (!scrollView) {
            cc.warn("ScrollView is null or undefined");
            return;
        }
        // 清除Scrollbar引用
        scrollView.horizontalScrollBar = null;
        scrollView.verticalScrollBar = null;
    };
    /**
     * 配置水平滚动的ScrollView
     */
    ScrollViewHelper.setupHorizontalScrollView = function (scrollView, content) {
        if (!scrollView || !content) {
            cc.error("ScrollView or content is null");
            return;
        }
        // 基本配置
        scrollView.content = content;
        scrollView.horizontal = true;
        scrollView.vertical = false;
        scrollView.inertia = true;
        scrollView.elastic = true;
        scrollView.bounceDuration = 0.3;
        // 清除Scrollbar引用
        this.fixScrollbarReference(scrollView);
    };
    /**
     * 配置垂直滚动的ScrollView
     */
    ScrollViewHelper.setupVerticalScrollView = function (scrollView, content) {
        if (!scrollView || !content) {
            cc.error("ScrollView or content is null");
            return;
        }
        // 基本配置
        scrollView.content = content;
        scrollView.horizontal = false;
        scrollView.vertical = true;
        scrollView.inertia = true;
        scrollView.elastic = true;
        scrollView.bounceDuration = 0.3;
        // 清除Scrollbar引用
        this.fixScrollbarReference(scrollView);
        cc.log("Vertical ScrollView configured successfully");
    };
    /**
     * 创建一个完整的水平ScrollView结构
     */
    ScrollViewHelper.createHorizontalScrollView = function (parent, size) {
        // 创建ScrollView节点
        var scrollViewNode = new cc.Node("ScrollView");
        var scrollView = scrollViewNode.addComponent(cc.ScrollView);
        scrollViewNode.setContentSize(size);
        scrollViewNode.parent = parent;
        // 创建Viewport节点
        var viewport = new cc.Node("Viewport");
        var mask = viewport.addComponent(cc.Mask);
        viewport.setContentSize(size);
        viewport.parent = scrollViewNode;
        // 创建Content节点
        var content = new cc.Node("Content");
        content.setContentSize(size);
        content.parent = viewport;
        // 配置ScrollView
        this.setupHorizontalScrollView(scrollView, content);
        return { scrollView: scrollView, viewport: viewport, content: content };
    };
    /**
     * 创建一个完整的垂直ScrollView结构
     */
    ScrollViewHelper.createVerticalScrollView = function (parent, size) {
        // 创建ScrollView节点
        var scrollViewNode = new cc.Node("ScrollView");
        var scrollView = scrollViewNode.addComponent(cc.ScrollView);
        scrollViewNode.setContentSize(size);
        scrollViewNode.parent = parent;
        // 创建Viewport节点
        var viewport = new cc.Node("Viewport");
        var mask = viewport.addComponent(cc.Mask);
        viewport.setContentSize(size);
        viewport.parent = scrollViewNode;
        // 创建Content节点
        var content = new cc.Node("Content");
        content.setContentSize(size);
        content.parent = viewport;
        // 配置ScrollView
        this.setupVerticalScrollView(scrollView, content);
        return { scrollView: scrollView, viewport: viewport, content: content };
    };
    /**
     * 滚动到指定位置（水平）
     */
    ScrollViewHelper.scrollToHorizontalPercent = function (scrollView, percent, duration) {
        if (duration === void 0) { duration = 0.3; }
        if (!scrollView) {
            cc.error("ScrollView is null");
            return;
        }
        var clampedPercent = cc.misc.clampf(percent, 0, 1);
        scrollView.scrollToPercentHorizontal(clampedPercent, duration);
    };
    /**
     * 滚动到指定位置（垂直）
     */
    ScrollViewHelper.scrollToVerticalPercent = function (scrollView, percent, duration) {
        if (duration === void 0) { duration = 0.3; }
        if (!scrollView) {
            cc.error("ScrollView is null");
            return;
        }
        var clampedPercent = cc.misc.clampf(percent, 0, 1);
        scrollView.scrollToPercentVertical(clampedPercent, duration);
    };
    /**
     * 获取当前滚动位置（水平）
     */
    ScrollViewHelper.getHorizontalScrollPercent = function (scrollView) {
        if (!scrollView) {
            cc.error("ScrollView is null");
            return 0;
        }
        var offset = scrollView.getScrollOffset();
        var maxScrollOffset = scrollView.getMaxScrollOffset();
        if (maxScrollOffset.x === 0)
            return 0;
        return Math.abs(offset.x) / Math.abs(maxScrollOffset.x);
    };
    /**
     * 获取当前滚动位置（垂直）
     */
    ScrollViewHelper.getVerticalScrollPercent = function (scrollView) {
        if (!scrollView) {
            cc.error("ScrollView is null");
            return 0;
        }
        var offset = scrollView.getScrollOffset();
        var maxScrollOffset = scrollView.getMaxScrollOffset();
        if (maxScrollOffset.y === 0)
            return 0;
        return Math.abs(offset.y) / Math.abs(maxScrollOffset.y);
    };
    /**
     * 检查ScrollView是否可以滚动
     */
    ScrollViewHelper.canScroll = function (scrollView) {
        if (!scrollView || !scrollView.content) {
            return { horizontal: false, vertical: false };
        }
        var viewSize = scrollView.node.getContentSize();
        var contentSize = scrollView.content.getContentSize();
        return {
            horizontal: contentSize.width > viewSize.width,
            vertical: contentSize.height > viewSize.height
        };
    };
    /**
     * 调试ScrollView信息
     */
    ScrollViewHelper.debugScrollView = function (scrollView, name) {
        if (name === void 0) { name = "ScrollView"; }
        if (!scrollView) {
            cc.log(name + ": ScrollView is null");
            return;
        }
        var viewSize = scrollView.node.getContentSize();
        var contentSize = scrollView.content ? scrollView.content.getContentSize() : cc.Size.ZERO;
        var offset = scrollView.getScrollOffset();
        var maxOffset = scrollView.getMaxScrollOffset();
        var canScrollInfo = this.canScroll(scrollView);
    };
    return ScrollViewHelper;
}());
exports.ScrollViewHelper = ScrollViewHelper;

cc._RF.pop();