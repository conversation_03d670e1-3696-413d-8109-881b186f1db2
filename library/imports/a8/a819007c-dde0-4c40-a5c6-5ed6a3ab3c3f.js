"use strict";
cc._RF.push(module, 'a8190B83eBMQKXGXtajqzw/', 'LevelSelectExample');
// scripts/hall/Level/LevelSelectExample.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var LevelSelectController_1 = require("./LevelSelectController");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
/**
 * 关卡选择使用示例
 * 这个脚本展示了如何快速创建一个关卡选择界面
 */
var LevelSelectExample = /** @class */ (function (_super) {
    __extends(LevelSelectExample, _super);
    function LevelSelectExample() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        // UI节点
        _this.scrollView = null;
        _this.content = null;
        _this.infoLabel = null;
        // 关卡数据
        _this.levelDataList = [];
        _this.currentSelectedLevel = 1;
        _this.totalLevels = 30;
        _this.levelItemWidth = 150;
        // 关卡节点列表
        _this.levelNodes = [];
        return _this;
    }
    LevelSelectExample.prototype.onLoad = function () {
        this.createUI();
        this.initLevelData();
        this.createLevelSelectUI();
    };
    LevelSelectExample.prototype.start = function () {
        this.scrollToLevel(this.currentSelectedLevel);
        this.updateInfoDisplay();
    };
    /**
     * 创建UI结构
     */
    LevelSelectExample.prototype.createUI = function () {
        // 创建背景
        var bg = new cc.Node("Background");
        bg.addComponent(cc.Sprite);
        bg.setContentSize(750, 1334);
        bg.color = cc.Color.BLACK;
        bg.parent = this.node;
        // 创建标题
        var titleNode = new cc.Node("Title");
        var titleLabel = titleNode.addComponent(cc.Label);
        titleLabel.string = "选择关卡";
        titleLabel.fontSize = 36;
        titleLabel.node.color = cc.Color.WHITE;
        titleNode.setPosition(0, 500);
        titleNode.parent = this.node;
        // 创建ScrollView
        var scrollViewNode = new cc.Node("ScrollView");
        this.scrollView = scrollViewNode.addComponent(cc.ScrollView);
        scrollViewNode.setContentSize(650, 150);
        scrollViewNode.setPosition(0, 0);
        scrollViewNode.parent = this.node;
        // 创建Viewport
        var viewport = new cc.Node("Viewport");
        viewport.addComponent(cc.Mask);
        viewport.setContentSize(650, 150);
        viewport.parent = scrollViewNode;
        // 创建Content
        this.content = new cc.Node("Content");
        this.content.setContentSize(650, 150);
        this.content.parent = viewport;
        // 设置ScrollView属性
        this.scrollView.content = this.content;
        this.scrollView.horizontal = true;
        this.scrollView.vertical = false;
        this.scrollView.inertia = true;
        this.scrollView.elastic = true;
        // 修复Scrollbar问题
        this.scrollView.horizontalScrollBar = null;
        this.scrollView.verticalScrollBar = null;
        // 创建信息标签
        var infoNode = new cc.Node("InfoLabel");
        this.infoLabel = infoNode.addComponent(cc.Label);
        this.infoLabel.string = "当前选中: 关卡1";
        this.infoLabel.fontSize = 24;
        this.infoLabel.node.color = cc.Color.WHITE;
        infoNode.setPosition(0, -200);
        infoNode.parent = this.node;
        // 创建测试按钮
        this.createTestButtons();
    };
    /**
     * 创建测试按钮
     */
    LevelSelectExample.prototype.createTestButtons = function () {
        var _this = this;
        // 完成关卡按钮
        var completeBtn = this.createButton("完成当前关卡", cc.v2(-150, -300), function () {
            _this.completeCurrentLevel();
        });
        // 重置按钮
        var resetBtn = this.createButton("重置关卡", cc.v2(0, -300), function () {
            _this.resetLevels();
        });
        // 随机进度按钮
        var randomBtn = this.createButton("随机进度", cc.v2(150, -300), function () {
            _this.setRandomProgress();
        });
    };
    /**
     * 创建按钮
     */
    LevelSelectExample.prototype.createButton = function (text, position, callback) {
        var btnNode = new cc.Node("Button");
        var button = btnNode.addComponent(cc.Button);
        var sprite = btnNode.addComponent(cc.Sprite);
        // 设置按钮外观
        btnNode.setContentSize(120, 40);
        btnNode.color = cc.Color.BLUE;
        btnNode.setPosition(position);
        btnNode.parent = this.node;
        // 添加文字
        var labelNode = new cc.Node("Label");
        var label = labelNode.addComponent(cc.Label);
        label.string = text;
        label.fontSize = 16;
        label.node.color = cc.Color.WHITE;
        labelNode.parent = btnNode;
        // 设置点击事件
        btnNode.on('click', callback, this);
        return btnNode;
    };
    /**
     * 初始化关卡数据
     */
    LevelSelectExample.prototype.initLevelData = function () {
        this.levelDataList = [];
        for (var i = 1; i <= this.totalLevels; i++) {
            var status = void 0;
            if (i === 1) {
                status = LevelSelectController_1.LevelStatus.CURRENT;
            }
            else if (i <= 3) {
                status = LevelSelectController_1.LevelStatus.COMPLETED;
            }
            else {
                status = LevelSelectController_1.LevelStatus.LOCKED;
            }
            this.levelDataList.push({
                levelNumber: i,
                status: status
            });
        }
    };
    /**
     * 创建关卡选择UI
     */
    LevelSelectExample.prototype.createLevelSelectUI = function () {
        if (!this.content)
            return;
        // 清空现有内容
        this.content.removeAllChildren();
        this.levelNodes = [];
        // 计算总宽度
        var totalWidth = (this.totalLevels - 1) * this.levelItemWidth + 650;
        this.content.width = totalWidth;
        for (var i = 0; i < this.totalLevels; i++) {
            var levelData = this.levelDataList[i];
            // 创建关卡节点
            var levelNode = this.createLevelNode(levelData);
            this.content.addChild(levelNode);
            this.levelNodes.push(levelNode);
            // 设置位置
            var posX = i * this.levelItemWidth - totalWidth / 2 + 650 / 2;
            levelNode.setPosition(posX, 0);
            // 创建连接线（除了最后一个关卡）
            if (i < this.totalLevels - 1) {
                var lineNode = this.createLineNode();
                this.content.addChild(lineNode);
                lineNode.setPosition(posX + this.levelItemWidth / 2, 0);
            }
        }
    };
    /**
     * 创建关卡节点
     */
    LevelSelectExample.prototype.createLevelNode = function (levelData) {
        var _this = this;
        var node = new cc.Node("Level_" + levelData.levelNumber);
        // 添加Sprite组件
        var sprite = node.addComponent(cc.Sprite);
        // 添加Label组件显示关卡数字
        var labelNode = new cc.Node("LevelLabel");
        var label = labelNode.addComponent(cc.Label);
        label.string = levelData.levelNumber.toString();
        label.fontSize = 20;
        label.node.color = cc.Color.WHITE;
        labelNode.parent = node;
        // 添加Button组件
        var button = node.addComponent(cc.Button);
        button.target = node;
        // 设置点击事件
        node.on('click', function () {
            _this.onLevelClicked(levelData.levelNumber);
        }, this);
        // 更新关卡外观
        this.updateLevelNodeAppearance(node, levelData, false);
        return node;
    };
    /**
     * 创建连接线节点
     */
    LevelSelectExample.prototype.createLineNode = function () {
        var node = new cc.Node("Line");
        var sprite = node.addComponent(cc.Sprite);
        node.setContentSize(6, 6);
        node.color = cc.Color.WHITE;
        return node;
    };
    /**
     * 更新关卡节点外观
     */
    LevelSelectExample.prototype.updateLevelNodeAppearance = function (node, levelData, isSelected) {
        var size = cc.size(46, 46);
        var color = cc.Color.GRAY;
        // 根据状态确定颜色和大小
        if (isSelected) {
            size = cc.size(86, 86);
        }
        switch (levelData.status) {
            case LevelSelectController_1.LevelStatus.LOCKED:
                color = cc.Color.GRAY;
                break;
            case LevelSelectController_1.LevelStatus.CURRENT:
                color = cc.Color.YELLOW;
                break;
            case LevelSelectController_1.LevelStatus.COMPLETED:
                color = cc.Color.GREEN;
                break;
        }
        // 设置节点大小和颜色
        node.setContentSize(size);
        node.color = color;
    };
    /**
     * 更新所有关卡显示
     */
    LevelSelectExample.prototype.updateAllLevelsDisplay = function () {
        for (var i = 0; i < this.levelNodes.length; i++) {
            var node = this.levelNodes[i];
            var levelData = this.levelDataList[i];
            var isSelected = (levelData.levelNumber === this.currentSelectedLevel);
            this.updateLevelNodeAppearance(node, levelData, isSelected);
        }
    };
    /**
     * 滚动到指定关卡
     */
    LevelSelectExample.prototype.scrollToLevel = function (levelNumber) {
        if (levelNumber < 1 || levelNumber > this.totalLevels)
            return;
        var targetIndex = levelNumber - 1;
        var contentWidth = this.content.width;
        var scrollViewWidth = this.scrollView.node.width;
        var targetOffset = (targetIndex * this.levelItemWidth) / (contentWidth - scrollViewWidth);
        var clampedOffset = cc.misc.clampf(targetOffset, 0, 1);
        this.scrollView.scrollToPercentHorizontal(clampedOffset, 0.3);
    };
    /**
     * 关卡点击事件处理
     */
    LevelSelectExample.prototype.onLevelClicked = function (levelNumber) {
        var levelData = this.levelDataList[levelNumber - 1];
        if (levelData.status === LevelSelectController_1.LevelStatus.LOCKED) {
            return;
        }
        this.currentSelectedLevel = levelNumber;
        this.updateAllLevelsDisplay();
        this.scrollToLevel(levelNumber);
        this.updateInfoDisplay();
    };
    /**
     * 更新信息显示
     */
    LevelSelectExample.prototype.updateInfoDisplay = function () {
        if (this.infoLabel) {
            var levelData = this.levelDataList[this.currentSelectedLevel - 1];
            var statusText = "";
            switch (levelData.status) {
                case LevelSelectController_1.LevelStatus.LOCKED:
                    statusText = "未解锁";
                    break;
                case LevelSelectController_1.LevelStatus.CURRENT:
                    statusText = "进行中";
                    break;
                case LevelSelectController_1.LevelStatus.COMPLETED:
                    statusText = "已通关";
                    break;
            }
            this.infoLabel.string = "\u5F53\u524D\u9009\u4E2D: \u5173\u5361" + this.currentSelectedLevel + " (" + statusText + ")";
        }
    };
    /**
     * 完成当前关卡
     */
    LevelSelectExample.prototype.completeCurrentLevel = function () {
        var levelData = this.levelDataList[this.currentSelectedLevel - 1];
        if (levelData.status === LevelSelectController_1.LevelStatus.CURRENT) {
            levelData.status = LevelSelectController_1.LevelStatus.COMPLETED;
            // 解锁下一关
            if (this.currentSelectedLevel < this.totalLevels) {
                var nextLevelData = this.levelDataList[this.currentSelectedLevel];
                if (nextLevelData.status === LevelSelectController_1.LevelStatus.LOCKED) {
                    nextLevelData.status = LevelSelectController_1.LevelStatus.CURRENT;
                }
            }
            this.updateAllLevelsDisplay();
            this.updateInfoDisplay();
        }
    };
    /**
     * 重置关卡
     */
    LevelSelectExample.prototype.resetLevels = function () {
        for (var i = 0; i < this.levelDataList.length; i++) {
            if (i === 0) {
                this.levelDataList[i].status = LevelSelectController_1.LevelStatus.CURRENT;
            }
            else {
                this.levelDataList[i].status = LevelSelectController_1.LevelStatus.LOCKED;
            }
        }
        this.currentSelectedLevel = 1;
        this.updateAllLevelsDisplay();
        this.scrollToLevel(1);
        this.updateInfoDisplay();
    };
    /**
     * 设置随机进度
     */
    LevelSelectExample.prototype.setRandomProgress = function () {
        var completedLevels = Math.floor(Math.random() * 10) + 1;
        var currentLevel = Math.min(completedLevels + 1, this.totalLevels);
        for (var i = 0; i < this.levelDataList.length; i++) {
            if (i < completedLevels) {
                this.levelDataList[i].status = LevelSelectController_1.LevelStatus.COMPLETED;
            }
            else if (i === completedLevels) {
                this.levelDataList[i].status = LevelSelectController_1.LevelStatus.CURRENT;
            }
            else {
                this.levelDataList[i].status = LevelSelectController_1.LevelStatus.LOCKED;
            }
        }
        this.currentSelectedLevel = currentLevel;
        this.updateAllLevelsDisplay();
        this.scrollToLevel(currentLevel);
        this.updateInfoDisplay();
    };
    LevelSelectExample = __decorate([
        ccclass
    ], LevelSelectExample);
    return LevelSelectExample;
}(cc.Component));
exports.default = LevelSelectExample;

cc._RF.pop();