{"version": 3, "sources": ["assets/scripts/start_up/StartUpCenterController.ts"], "names": [], "mappings": ";;;;;AAAA,oBAAoB;AACpB,4EAA4E;AAC5E,mBAAmB;AACnB,sFAAsF;AACtF,8BAA8B;AAC9B,sFAAsF;;;;;;;;;;;;;;;;;;;;;AAGtF,qDAAkD;AAClD,6CAA4C;AAC5C,0DAAwE;AACxE,yCAAwC;AAElC,IAAA,KAAwB,EAAE,CAAC,UAAU,EAAnC,OAAO,aAAA,EAAE,QAAQ,cAAkB,CAAC;AAG5C;IAAqD,2CAAY;IAAjE;QAAA,qEAoFC;QAhFG,cAAQ,GAAc,IAAI,CAAA;QAE1B,mBAAa,GAAa,IAAI,CAAA;QAE9B,uBAAiB,GAAW,IAAI,CAAC,CAAA,SAAS;QAE1C,WAAK,GAAG,KAAK,CAAA,CAAG,QAAQ;QACxB,YAAM,GAAG,KAAK,CAAA,CAAE,QAAQ;QACxB,aAAO,GAAG,KAAK,CAAA,CAAE,QAAQ;;QAuEzB,iBAAiB;IACrB,CAAC;IAtEa,wCAAM,GAAhB;QAAA,iBAWC;QAVG,EAAE,CAAC,SAAS,CAAC,UAAU,CAAC,eAAM,CAAC,SAAS,EAAC,EAAE,CAAC,WAAW,EAAC,UAAC,KAAK,EAAE,KAAK;YACjE,IAAG,KAAK,EAAC;gBACL,iBAAO,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAA;aACnC;iBAAI;gBACD,iBAAO,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAA;gBAChC,KAAI,CAAC,OAAO,GAAG,IAAI,CAAA;gBACnB,KAAI,CAAC,QAAQ,EAAE,CAAA;aAClB;QAEL,CAAC,CAAC,CAAC,CAAA,SAAS;IAChB,CAAC;IAES,0CAAQ,GAAlB;QACI,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAA;QAC5B,IAAI,CAAC,cAAc,EAAE,CAAA;IACzB,CAAC;IAED,uCAAK,GAAL;IAGA,CAAC;IAED,gDAAc,GAAd;QAAA,iBAeC;QAdG,IAAI,gBAAgB,GAAG,CAAC,CAAC;QACzB,IAAI,CAAC,iBAAiB,GAAG,WAAW,CAAC;YACjC,gBAAgB,EAAE,CAAC;YAEnB,IAAI,gBAAgB,GAAG,GAAG,EAAE;gBACxB,aAAa,CAAC,KAAI,CAAC,iBAAiB,CAAC,CAAC;gBACtC,KAAI,CAAC,iBAAiB,GAAG,IAAI,CAAA;gBAC7B,KAAI,CAAC,MAAM,GAAI,IAAI,CAAA;gBACnB,aAAa;gBACb,KAAI,CAAC,QAAQ,EAAE,CAAA;gBACf,OAAM;aACT;YACD,KAAI,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,CAAC;QAChD,CAAC,EAAE,EAAE,CAAC,CAAC;IACX,CAAC;IAED,sDAAoB,GAApB,UAAqB,OAAe;QAChC,IAAI,CAAC,aAAa,CAAC,MAAM,GAAM,OAAO,MAAG,CAAC;QAC1C,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,OAAO,GAAC,GAAG,CAAC;IAC1C,CAAC;IAES,2CAAS,GAAnB;QACI,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,aAAa,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YACtC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAA;SAChC;IACL,CAAC;IACD,0CAAQ,GAAR;QACI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAA;QACjB,IAAI,CAAC,QAAQ,EAAE,CAAA;IACnB,CAAC;IAED,0CAAQ,GAAR;QACI,uBAAuB;QACvB,IAAG,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,OAAO,EAAC;YAEzC,IAAI,eAAe,GAAoB;gBACnC,OAAO,EAAE,+BAAa,CAAC,YAAY;gBACnC,MAAM,EAAE,EAAC,MAAM,EAAC,CAAC,EAAC,CAAC,WAAW;aACjC,CAAA;YACD,iBAAO,CAAC,KAAK,CAAC,IAAI,CAAC,uBAAS,CAAC,WAAW,EAAE,eAAe,CAAC,CAAC;SAC9D;IAEL,CAAC;IA7ED;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;6DACM;IAE1B;QADC,QAAQ,CAAC,EAAE,CAAC,KAAK,CAAC;kEACW;IANb,uBAAuB;QAD3C,OAAO;OACa,uBAAuB,CAoF3C;IAAD,8BAAC;CApFD,AAoFC,CApFoD,EAAE,CAAC,SAAS,GAoFhE;kBApFoB,uBAAuB", "file": "", "sourceRoot": "/", "sourcesContent": ["// Learn TypeScript:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html\n// Learn Attribute:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html\n// Learn life-cycle callbacks:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html\n\n\nimport { EventType } from \"../common/EventCenter\";\nimport { GameMgr } from \"../common/GameMgr\";\nimport { AutoMessageBean, AutoMessageId } from \"../net/MessageBaseBean\";\nimport { Config } from \"../util/Config\";\n\nconst { ccclass, property } = cc._decorator;\n\n@ccclass\nexport default class StartUpCenterController extends cc.Component {\n\n\n    @property(cc.Sprite)\n    progress: cc.Sprite = null\n    @property(cc.Label)\n    progressLabel: cc.Label = null\n\n    countdownInterval: number = null;//倒计时的 id\n\n    login = false   //登录是否成功\n    loding = false  //加载是否成功\n    preload = false  //加载是否成功\n\n    protected onLoad(): void {\n        cc.resources.preloadDir(Config.buttonRes,cc.SpriteAtlas,(error, items) =>{\n            if(error){\n                GameMgr.Console.Log('预加载按钮资源失败')\n            }else{\n                GameMgr.Console.Log('预加载按钮资源成功')\n                this.preload = true\n                this.jumpHall()\n            }\n            \n        });//提前预加载图片\n    }\n\n    protected onEnable(): void {\n        this.updateCountdownLabel(0)\n        this.startCountdown()\n    }\n\n    start() {\n\n\n    }\n\n    startCountdown() {\n        let remainingSeconds = 0;\n        this.countdownInterval = setInterval(() => {\n            remainingSeconds++;\n\n            if (remainingSeconds > 100) {\n                clearInterval(this.countdownInterval);\n                this.countdownInterval = null\n                this.loding  = true\n                // 进度到 100的处理\n                this.jumpHall()\n                return\n            }\n            this.updateCountdownLabel(remainingSeconds);\n        }, 10);\n    }\n\n    updateCountdownLabel(percent: number) {\n        this.progressLabel.string = `${percent}%`;\n        this.progress.fillRange = percent/100;\n    }\n\n    protected onDisable(): void {\n        if (this.countdownInterval) {\n            clearInterval(this.countdownInterval);\n            this.countdownInterval = null\n        }\n    }\n    setLogin(){\n        this.login = true\n        this.jumpHall()\n    }\n\n    jumpHall(){\n        //加载成功 并且登录成功 才允许跳转大厅页面\n        if(this.loding && this.login && this.preload){\n\n            let autoMessageBean: AutoMessageBean = {\n                'msgId': AutoMessageId.JumpHallPage,//跳转进大厅页面\n                'data': {'type':1} //1是启动页面跳转的\n            }\n            GameMgr.Event.Send(EventType.AutoMessage, autoMessageBean);\n        }\n\n    }\n\n    // update (dt) {}\n}\n"]}