"use strict";
cc._RF.push(module, 'fb002PTGrFERKCYkhthVy56', 'LevelSelectDemo');
// scripts/hall/LevelSelectDemo.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var LevelSelectController_1 = require("./Level/LevelSelectController");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
/**
 * 关卡选择演示控制器
 * 这个脚本展示了如何创建一个完整的关卡选择界面
 */
var LevelSelectDemo = /** @class */ (function (_super) {
    __extends(LevelSelectDemo, _super);
    function LevelSelectDemo() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.scrollView = null;
        _this.content = null;
        _this.infoLabel = null;
        // 关卡数据
        _this.levelDataList = [];
        _this.currentSelectedLevel = 1;
        _this.totalLevels = 30;
        _this.levelItemWidth = 150;
        // 关卡节点列表
        _this.levelNodes = [];
        return _this;
    }
    LevelSelectDemo.prototype.onLoad = function () {
        this.initLevelData();
        this.createLevelSelectUI();
    };
    LevelSelectDemo.prototype.start = function () {
        this.scrollToLevel(this.currentSelectedLevel);
        this.updateInfoDisplay();
    };
    /**
     * 初始化关卡数据
     */
    LevelSelectDemo.prototype.initLevelData = function () {
        this.levelDataList = [];
        for (var i = 1; i <= this.totalLevels; i++) {
            var status = void 0;
            if (i === 1) {
                status = LevelSelectController_1.LevelStatus.CURRENT; // 第一关为当前关卡
            }
            else if (i <= 3) {
                status = LevelSelectController_1.LevelStatus.COMPLETED; // 前3关已通关（示例）
            }
            else {
                status = LevelSelectController_1.LevelStatus.LOCKED; // 其他关卡未解锁
            }
            this.levelDataList.push({
                levelNumber: i,
                status: status
            });
        }
    };
    /**
     * 创建关卡选择UI
     */
    LevelSelectDemo.prototype.createLevelSelectUI = function () {
        if (!this.content) {
            cc.error("Content node is not assigned!");
            return;
        }
        // 清空现有内容
        this.content.removeAllChildren();
        this.levelNodes = [];
        // 计算总宽度
        var totalWidth = (this.totalLevels - 1) * this.levelItemWidth + 650;
        this.content.width = totalWidth;
        for (var i = 0; i < this.totalLevels; i++) {
            var levelData = this.levelDataList[i];
            // 创建关卡节点
            var levelNode = this.createLevelNode(levelData);
            this.content.addChild(levelNode);
            this.levelNodes.push(levelNode);
            // 设置位置
            var posX = i * this.levelItemWidth - totalWidth / 2 + 650 / 2;
            levelNode.setPosition(posX, 0);
            // 创建连接线（除了最后一个关卡）
            if (i < this.totalLevels - 1) {
                var lineNode = this.createLineNode();
                this.content.addChild(lineNode);
                lineNode.setPosition(posX + this.levelItemWidth / 2, 0);
            }
        }
    };
    /**
     * 创建关卡节点
     */
    LevelSelectDemo.prototype.createLevelNode = function (levelData) {
        var _this = this;
        var node = new cc.Node("Level_" + levelData.levelNumber);
        // 添加Sprite组件
        node.addComponent(cc.Sprite);
        // 添加Label组件显示关卡数字
        var labelNode = new cc.Node("LevelLabel");
        var label = labelNode.addComponent(cc.Label);
        label.string = levelData.levelNumber.toString();
        label.fontSize = 20;
        label.node.color = cc.Color.WHITE;
        labelNode.parent = node;
        // 添加Button组件
        var button = node.addComponent(cc.Button);
        button.target = node;
        // 设置点击事件
        node.on('click', function () {
            _this.onLevelClicked(levelData.levelNumber);
        }, this);
        // 更新关卡外观
        this.updateLevelNodeAppearance(node, levelData, false);
        return node;
    };
    /**
     * 创建连接线节点
     */
    LevelSelectDemo.prototype.createLineNode = function () {
        var node = new cc.Node("Line");
        var sprite = node.addComponent(cc.Sprite);
        // 加载连接线图片
        cc.resources.load("hall_page_res/Level_Btn/pop_line", cc.SpriteFrame, function (err, spriteFrame) {
            if (!err && spriteFrame) {
                sprite.spriteFrame = spriteFrame;
            }
        });
        return node;
    };
    /**
     * 更新关卡节点外观
     */
    LevelSelectDemo.prototype.updateLevelNodeAppearance = function (node, levelData, isSelected) {
        var sprite = node.getComponent(cc.Sprite);
        if (!sprite)
            return;
        var imagePath = "";
        var size = cc.size(46, 46);
        // 根据状态和是否选中确定图片路径
        if (isSelected) {
            size = cc.size(86, 86);
            switch (levelData.status) {
                case LevelSelectController_1.LevelStatus.LOCKED:
                    imagePath = "hall_page_res/Level_Btn/pop_gray_choose";
                    break;
                case LevelSelectController_1.LevelStatus.CURRENT:
                    imagePath = "hall_page_res/Level_Btn/pop_yellow_choose";
                    break;
                case LevelSelectController_1.LevelStatus.COMPLETED:
                    imagePath = "hall_page_res/Level_Btn/pop_green_choose";
                    break;
            }
        }
        else {
            switch (levelData.status) {
                case LevelSelectController_1.LevelStatus.LOCKED:
                    imagePath = "hall_page_res/Level_Btn/pop_gray";
                    break;
                case LevelSelectController_1.LevelStatus.CURRENT:
                    imagePath = "hall_page_res/Level_Btn/pop_yellow";
                    break;
                case LevelSelectController_1.LevelStatus.COMPLETED:
                    imagePath = "hall_page_res/Level_Btn/pop_green";
                    break;
            }
        }
        // 设置节点大小
        node.setContentSize(size);
        // 加载并设置图片
        cc.resources.load(imagePath, cc.SpriteFrame, function (err, spriteFrame) {
            if (!err && spriteFrame) {
                sprite.spriteFrame = spriteFrame;
            }
        });
    };
    /**
     * 更新所有关卡显示
     */
    LevelSelectDemo.prototype.updateAllLevelsDisplay = function () {
        for (var i = 0; i < this.levelNodes.length; i++) {
            var node = this.levelNodes[i];
            var levelData = this.levelDataList[i];
            var isSelected = (levelData.levelNumber === this.currentSelectedLevel);
            this.updateLevelNodeAppearance(node, levelData, isSelected);
        }
    };
    /**
     * 滚动到指定关卡
     */
    LevelSelectDemo.prototype.scrollToLevel = function (levelNumber) {
        if (levelNumber < 1 || levelNumber > this.totalLevels)
            return;
        var targetIndex = levelNumber - 1;
        var contentWidth = this.content.width;
        var scrollViewWidth = this.scrollView.node.width;
        // 计算目标位置的偏移量
        var targetOffset = (targetIndex * this.levelItemWidth) / (contentWidth - scrollViewWidth);
        // 限制偏移量在有效范围内
        var clampedOffset = cc.misc.clampf(targetOffset, 0, 1);
        // 设置滚动位置
        this.scrollView.scrollToPercentHorizontal(clampedOffset, 0.3);
    };
    /**
     * 关卡点击事件处理
     */
    LevelSelectDemo.prototype.onLevelClicked = function (levelNumber) {
        // 检查关卡是否可以选择（未锁定）
        var levelData = this.levelDataList[levelNumber - 1];
        if (levelData.status === LevelSelectController_1.LevelStatus.LOCKED) {
            return;
        }
        // 更新当前选中关卡
        this.currentSelectedLevel = levelNumber;
        this.updateAllLevelsDisplay();
        // 滚动到选中关卡
        this.scrollToLevel(levelNumber);
        this.updateInfoDisplay();
    };
    /**
     * 更新信息显示
     */
    LevelSelectDemo.prototype.updateInfoDisplay = function () {
        if (this.infoLabel) {
            var levelData = this.levelDataList[this.currentSelectedLevel - 1];
            var statusText = "";
            switch (levelData.status) {
                case LevelSelectController_1.LevelStatus.LOCKED:
                    statusText = "未解锁";
                    break;
                case LevelSelectController_1.LevelStatus.CURRENT:
                    statusText = "进行中";
                    break;
                case LevelSelectController_1.LevelStatus.COMPLETED:
                    statusText = "已通关";
                    break;
            }
            this.infoLabel.string = "\u5F53\u524D\u9009\u4E2D: \u5173\u5361" + this.currentSelectedLevel + " (" + statusText + ")";
        }
    };
    /**
     * 完成当前关卡（测试用）
     */
    LevelSelectDemo.prototype.completeCurrentLevel = function () {
        var levelData = this.levelDataList[this.currentSelectedLevel - 1];
        if (levelData.status === LevelSelectController_1.LevelStatus.CURRENT) {
            levelData.status = LevelSelectController_1.LevelStatus.COMPLETED;
            // 解锁下一关
            if (this.currentSelectedLevel < this.totalLevels) {
                var nextLevelData = this.levelDataList[this.currentSelectedLevel];
                if (nextLevelData.status === LevelSelectController_1.LevelStatus.LOCKED) {
                    nextLevelData.status = LevelSelectController_1.LevelStatus.CURRENT;
                }
            }
            this.updateAllLevelsDisplay();
            this.updateInfoDisplay();
        }
    };
    __decorate([
        property(cc.ScrollView)
    ], LevelSelectDemo.prototype, "scrollView", void 0);
    __decorate([
        property(cc.Node)
    ], LevelSelectDemo.prototype, "content", void 0);
    __decorate([
        property(cc.Label)
    ], LevelSelectDemo.prototype, "infoLabel", void 0);
    LevelSelectDemo = __decorate([
        ccclass
    ], LevelSelectDemo);
    return LevelSelectDemo;
}(cc.Component));
exports.default = LevelSelectDemo;

cc._RF.pop();