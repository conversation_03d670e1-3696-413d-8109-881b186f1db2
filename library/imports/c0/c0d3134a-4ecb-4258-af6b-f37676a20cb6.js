"use strict";
cc._RF.push(module, 'c0d31NKTstCWK9r83Z2ogy2', 'WebSocketManager');
// scripts/net/WebSocketManager.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebSocketManager = void 0;
var Singleton_1 = require("../../meshTools/Singleton");
var EventCenter_1 = require("../common/EventCenter");
var GameMgr_1 = require("../common/GameMgr");
var WebSocketTool_1 = require("./WebSocketTool");
var WebSocketManager = /** @class */ (function (_super) {
    __extends(WebSocketManager, _super);
    function WebSocketManager() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.webState = WebSocketTool_1.WebSocketToolState.Closing;
        return _this;
    }
    //连接 socket
    WebSocketManager.prototype.connect = function () {
        WebSocketTool_1.WebSocketTool.GetInstance().setSocketFunction(this.msgData, this.socketState);
        WebSocketTool_1.WebSocketTool.GetInstance().connect(); //连接长链接
    };
    //这个是用来接收服务器返回回来的数据
    WebSocketManager.prototype.msgData = function (msg) {
        var parsedData = JSON.parse(msg);
        if (parsedData.code == 0) { //正常数据
            GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.ReceiveMessage, parsedData); //将收到的消息发送出去
        }
        else {
            GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.ReceiveErrorMessage, parsedData); //将收到的消息发送出去
        }
    };
    //这个是接收 websocket 状态的
    WebSocketManager.prototype.socketState = function (webState) {
        GameMgr_1.GameMgr.Console.Log('webSocket状态' + webState);
        WebSocketManager.GetInstance().webState = webState;
    };
    //发送消息  data 请求参数 是个对象{}
    WebSocketManager.prototype.sendMsg = function (msgId, data) {
        var sendMessageBean = {
            msgId: msgId,
            data: data,
        };
        var jsonString = JSON.stringify(sendMessageBean);
        WebSocketTool_1.WebSocketTool.GetInstance().send(jsonString);
    };
    return WebSocketManager;
}(Singleton_1.Singleton));
exports.WebSocketManager = WebSocketManager;

cc._RF.pop();