{"version": 3, "sources": ["assets/scripts/hall/TopUpDialogController.ts"], "names": [], "mappings": ";;;;;AAAA,oBAAoB;AACpB,4EAA4E;AAC5E,mBAAmB;AACnB,sFAAsF;AACtF,8BAA8B;AAC9B,sFAAsF;;;;;;;;;;;;;;;;;;;;;AAEtF,6CAA4C;AAC5C,yCAAwC;AACxC,uCAAsC;AAEhC,IAAA,KAAwB,EAAE,CAAC,UAAU,EAAnC,OAAO,aAAA,EAAE,QAAQ,cAAkB,CAAC;AAG5C;IAAmD,yCAAY;IAA/D;QAAA,qEA8DC;QA3DG,aAAO,GAAY,IAAI,CAAA;QAEvB,mBAAa,GAAY,IAAI,CAAA;QAE7B,gBAAU,GAAY,IAAI,CAAA;QAE1B,eAAS,GAAY,IAAI,CAAA;QAEzB,gBAAU,GAAY,IAAI,CAAA;QAE1B,gBAAU,GAAa,IAAI,CAAA;QAE3B,kBAAY,GAAa,IAAI,CAAA,CAAC,SAAS;;QA8CvC,iBAAiB;IACrB,CAAC;IA5CG,eAAe;IAEf,qCAAK,GAAL;QAAA,iBAeC;QAbG,aAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,aAAa,EAAE,eAAM,CAAC,SAAS,GAAG,wBAAwB,EAAE,eAAM,CAAC,SAAS,GAAG,yBAAyB,EAAE;YAClI,KAAI,CAAC,IAAI,EAAE,CAAA;QACf,CAAC,CAAC,CAAC;QAEH,eAAe;QACf,aAAK,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,EAAE;YAC/B,KAAI,CAAC,IAAI,EAAE,CAAA;QACf,CAAC,CAAC,CAAA;QACF,gBAAgB;QAChB,aAAK,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,EAAE;YAC/B,iBAAO,CAAC,KAAK,CAAC,WAAW,EAAE,CAAA;YAC3B,KAAI,CAAC,IAAI,EAAE,CAAA;QACf,CAAC,CAAC,CAAA;IACN,CAAC;IAGD,oCAAI,GAAJ,UAAK,YAAsB;QAEvB,IAAI,CAAC,YAAY,GAAG,YAAY,CAAA;QAChC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAA;QACvB,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,CAAC,CAAA;QACtB,SAAS;QACT,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC;aACjB,EAAE,CAAC,eAAM,CAAC,eAAe,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;aACxC,KAAK,EAAE,CAAC;IACjB,CAAC;IACD,oCAAI,GAAJ;QAAA,iBAWC;QAVG,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,YAAY,EAAE,CAAA;SACtB;QACD,SAAS;QACT,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC;aACjB,EAAE,CAAC,eAAM,CAAC,eAAe,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;aACxC,IAAI,CAAC;YACF,KAAI,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAA;QAC5B,CAAC,CAAC;aACD,KAAK,EAAE,CAAC;IACjB,CAAC;IAxDD;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;0DACK;IAEvB;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;gEACW;IAE7B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;6DACQ;IAE1B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;4DACO;IAEzB;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;6DACQ;IAE1B;QADC,QAAQ,CAAC,EAAE,CAAC,KAAK,CAAC;6DACQ;IAbV,qBAAqB;QADzC,OAAO;OACa,qBAAqB,CA8DzC;IAAD,4BAAC;CA9DD,AA8DC,CA9DkD,EAAE,CAAC,SAAS,GA8D9D;kBA9DoB,qBAAqB", "file": "", "sourceRoot": "/", "sourcesContent": ["// Learn TypeScript:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html\n// Learn Attribute:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html\n// Learn life-cycle callbacks:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html\n\nimport { GameMgr } from \"../common/GameMgr\";\nimport { Config } from \"../util/Config\";\nimport { Tools } from \"../util/Tools\";\n\nconst { ccclass, property } = cc._decorator;\n\n@ccclass\nexport default class TopUpDialogController extends cc.Component {\n\n    @property(cc.Node)\n    boardBg: cc.Node = null\n    @property(cc.Node)\n    boardBtnClose: cc.Node = null\n    @property(cc.Node)\n    contentLay: cc.Node = null\n    @property(cc.Node)\n    cancelBtn: cc.Node = null\n    @property(cc.Node)\n    confirmBtn: cc.Node = null\n    @property(cc.Label)\n    tipContent: cc.Label = null\n\n    backCallback: Function = null //隐藏弹窗的回调\n\n\n    // onLoad () {}\n\n    start() {\n\n        Tools.imageButtonClick(this.boardBtnClose, Config.buttonRes + 'board_btn_close_normal', Config.buttonRes + 'board_btn_close_pressed', () => {\n            this.hide()\n        });\n\n        //cancel 按钮点击事件\n        Tools.yellowButton(this.cancelBtn, () => {\n            this.hide()\n        })\n        //Confirm 按钮点击事件\n        Tools.greenButton(this.confirmBtn, () => {\n            GameMgr.H5SDK.ShowAppShop()\n            this.hide()\n        })\n    }\n\n\n    show(backCallback: Function) {\n\n        this.backCallback = backCallback\n        this.node.active = true\n        this.boardBg.scale = 0\n        // 执行缩放动画\n        cc.tween(this.boardBg)\n            .to(Config.dialogScaleTime, { scale: 1 })\n            .start();\n    }\n    hide() {\n        if (this.backCallback) {\n            this.backCallback()\n        }\n        // 执行缩放动画\n        cc.tween(this.boardBg)\n            .to(Config.dialogScaleTime, { scale: 0 })\n            .call(() => {\n                this.node.active = false\n            })\n            .start();\n    }\n\n    // update (dt) {}\n}\n"]}