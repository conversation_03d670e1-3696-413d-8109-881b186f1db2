{"version": 3, "sources": ["assets/scripts/hall/LeaveDialogController.ts"], "names": [], "mappings": ";;;;;AAAA,oBAAoB;AACpB,4EAA4E;AAC5E,mBAAmB;AACnB,sFAAsF;AACtF,8BAA8B;AAC9B,sFAAsF;;;;;;;;;;;;;;;;;;;;;AAEtF,6CAA4C;AAC5C,8CAA6C;AAC7C,4DAA2D;AAC3D,yCAAwC;AACxC,uCAAsC;AAEhC,IAAA,KAAwB,EAAE,CAAC,UAAU,EAAnC,OAAO,aAAA,EAAE,QAAQ,cAAkB,CAAC;AAG5C;IAAmD,yCAAY;IAA/D;QAAA,qEAoFC;QAjFG,aAAO,GAAY,IAAI,CAAA;QAEvB,mBAAa,GAAY,IAAI,CAAA;QAE7B,gBAAU,GAAY,IAAI,CAAA;QAE1B,eAAS,GAAY,IAAI,CAAA;QAEzB,cAAQ,GAAY,IAAI,CAAA;QAExB,gBAAU,GAAa,IAAI,CAAA;QAE3B,UAAI,GAAW,CAAC,CAAA,CAAC,qBAAqB;QACtC,YAAM,GAAW,CAAC,CAAA,CAAC,mBAAmB;QAEtC,kBAAY,GAAa,IAAI,CAAA,CAAC,SAAS;;QAiEvC,iBAAiB;IACrB,CAAC;IA/DG,eAAe;IAEf,qCAAK,GAAL;QAAA,iBAyBC;QAvBG,aAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,aAAa,EAAE,eAAM,CAAC,SAAS,GAAG,wBAAwB,EAAE,eAAM,CAAC,SAAS,GAAG,yBAAyB,EAAE;YAClI,KAAI,CAAC,IAAI,EAAE,CAAA;QACf,CAAC,CAAC,CAAC;QACH,eAAe;QACf,aAAK,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,EAAE;YAC/B,KAAI,CAAC,IAAI,EAAE,CAAA;QACf,CAAC,CAAC,CAAA;QACF,cAAc;QACd,aAAK,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE;YAC3B,IAAG,KAAI,CAAC,IAAI,KAAG,CAAC,EAAC;gBACb,iBAAO,CAAC,KAAK,CAAC,YAAY,EAAE,CAAA,CAAG,UAAU;aAC5C;iBAAI;gBACF,YAAY;gBACZ,KAAI,CAAC,IAAI,EAAE,CAAA;gBACX,UAAU;gBACV,IAAM,aAAa,GAAQ,EAAE,gBAAgB,EAAE,IAAI,EAAE,CAAC;gBACtD,IAAI,KAAI,CAAC,MAAM,GAAG,CAAC,EAAE;oBACjB,aAAa,CAAC,MAAM,GAAG,KAAI,CAAC,MAAM,CAAC;iBAEtC;gBACD,mCAAgB,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,qBAAS,CAAC,gBAAgB,EAAE,aAAa,CAAC,CAAC;aACpF;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAGD,oCAAI,GAAJ,UAAK,IAAY,EAAE,YAAsB,EAAE,MAAkB;QAAlB,uBAAA,EAAA,UAAkB;QACzD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAChB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;QACpB,IAAI,CAAC,YAAY,GAAG,YAAY,CAAA;QAChC,IAAG,IAAI,KAAG,CAAC,EAAC;YACR,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,MAAM,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAA,CAAC,aAAa;SACnF;aAAI;YACD,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,MAAM,CAAC,eAAe,CAAC,aAAa,CAAC,CAAA,CAAC,aAAa;SAC/E;QAID,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAA;QACvB,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,CAAC,CAAA;QACtB,SAAS;QACT,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC;aACjB,EAAE,CAAC,eAAM,CAAC,eAAe,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;aACxC,KAAK,EAAE,CAAC;IACjB,CAAC;IACD,oCAAI,GAAJ;QAAA,iBAWC;QAVG,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,YAAY,EAAE,CAAA;SACtB;QACD,SAAS;QACT,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC;aACjB,EAAE,CAAC,eAAM,CAAC,eAAe,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;aACxC,IAAI,CAAC;YACF,KAAI,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAA;QAC5B,CAAC,CAAC;aACD,KAAK,EAAE,CAAC;IACjB,CAAC;IA9ED;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;0DACK;IAEvB;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;gEACW;IAE7B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;6DACQ;IAE1B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;4DACO;IAEzB;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;2DACM;IAExB;QADC,QAAQ,CAAC,EAAE,CAAC,KAAK,CAAC;6DACQ;IAbV,qBAAqB;QADzC,OAAO;OACa,qBAAqB,CAoFzC;IAAD,4BAAC;CApFD,AAoFC,CApFkD,EAAE,CAAC,SAAS,GAoF9D;kBApFoB,qBAAqB", "file": "", "sourceRoot": "/", "sourcesContent": ["// Learn TypeScript:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html\n// Learn Attribute:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html\n// Learn life-cycle callbacks:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html\n\nimport { GameMgr } from \"../common/GameMgr\";\nimport { MessageId } from \"../net/MessageId\";\nimport { WebSocketManager } from \"../net/WebSocketManager\";\nimport { Config } from \"../util/Config\";\nimport { Tools } from \"../util/Tools\";\n\nconst { ccclass, property } = cc._decorator;\n\n@ccclass\nexport default class LeaveDialogController extends cc.Component {\n\n    @property(cc.Node)\n    boardBg: cc.Node = null\n    @property(cc.Node)\n    boardBtnClose: cc.Node = null\n    @property(cc.Node)\n    contentLay: cc.Node = null\n    @property(cc.Node)\n    cancelBtn: cc.Node = null\n    @property(cc.Node)\n    leaveBtn: cc.Node = null\n    @property(cc.Label)\n    tipContent: cc.Label = null\n\n    type: number = 0 //0是完全退出这个游戏 1是退出本局游戏\n    roomId: number = 0 // 房间ID，用于退出时发送给服务器\n\n    backCallback: Function = null //隐藏弹窗的回调\n\n\n    // onLoad () {}\n\n    start() {\n\n        Tools.imageButtonClick(this.boardBtnClose, Config.buttonRes + 'board_btn_close_normal', Config.buttonRes + 'board_btn_close_pressed', () => {\n            this.hide()\n        });\n        //cancel 按钮点击事件\n        Tools.yellowButton(this.cancelBtn, () => {\n            this.hide()\n        })\n        //leave 按钮点击事件\n        Tools.redButton(this.leaveBtn, () => {\n            if(this.type===0){\n                GameMgr.H5SDK.CloseWebView()   //这个是退出游戏的\n            }else{\n               //这个是退出本局游戏的\n               this.hide()\n               //退出当前房间游戏\n               const leaveRoomData: any = { 'isConfirmLeave': true };\n               if (this.roomId > 0) {\n                   leaveRoomData.roomId = this.roomId;\n                  \n               }\n               WebSocketManager.GetInstance().sendMsg(MessageId.MsgTypeLeaveRoom, leaveRoomData);\n            }\n        })\n    }\n\n\n    show(type: number, backCallback: Function, roomId: number = 0) {\n        this.type = type\n        this.roomId = roomId\n        this.backCallback = backCallback\n        if(type===0){\n            this.tipContent.string = window.getLocalizedStr('ExitApplication') //显示完全退出的文案退出\n        }else{\n            this.tipContent.string = window.getLocalizedStr('QuitTheGame') //显示退出本局游戏的文案\n        }\n\n    \n\n        this.node.active = true\n        this.boardBg.scale = 0\n        // 执行缩放动画\n        cc.tween(this.boardBg)\n            .to(Config.dialogScaleTime, { scale: 1 })\n            .start();\n    }\n    hide() {\n        if (this.backCallback) {\n            this.backCallback()\n        }\n        // 执行缩放动画\n        cc.tween(this.boardBg)\n            .to(Config.dialogScaleTime, { scale: 0 })\n            .call(() => {\n                this.node.active = false\n            })\n            .start();\n    }\n\n    // update (dt) {}\n}\n"]}