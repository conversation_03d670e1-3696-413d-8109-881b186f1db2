"use strict";
cc._RF.push(module, 'dae83aDtN5Oe6VqQSxT8XBO', 'LeaveDialogController');
// scripts/hall/LeaveDialogController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var GameMgr_1 = require("../common/GameMgr");
var MessageId_1 = require("../net/MessageId");
var WebSocketManager_1 = require("../net/WebSocketManager");
var Config_1 = require("../util/Config");
var Tools_1 = require("../util/Tools");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var LeaveDialogController = /** @class */ (function (_super) {
    __extends(LeaveDialogController, _super);
    function LeaveDialogController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.boardBg = null;
        _this.boardBtnClose = null;
        _this.contentLay = null;
        _this.cancelBtn = null;
        _this.leaveBtn = null;
        _this.tipContent = null;
        _this.type = 0; //0是完全退出这个游戏 1是退出本局游戏
        _this.roomId = 0; // 房间ID，用于退出时发送给服务器
        _this.backCallback = null; //隐藏弹窗的回调
        return _this;
        // update (dt) {}
    }
    // onLoad () {}
    LeaveDialogController.prototype.start = function () {
        var _this = this;
        Tools_1.Tools.imageButtonClick(this.boardBtnClose, Config_1.Config.buttonRes + 'board_btn_close_normal', Config_1.Config.buttonRes + 'board_btn_close_pressed', function () {
            _this.hide();
        });
        //cancel 按钮点击事件
        Tools_1.Tools.yellowButton(this.cancelBtn, function () {
            _this.hide();
        });
        //leave 按钮点击事件
        Tools_1.Tools.redButton(this.leaveBtn, function () {
            if (_this.type === 0) {
                GameMgr_1.GameMgr.H5SDK.CloseWebView(); //这个是退出游戏的
            }
            else {
                //这个是退出本局游戏的
                _this.hide();
                //退出当前房间游戏
                var leaveRoomData = { 'isConfirmLeave': true };
                if (_this.roomId > 0) {
                    leaveRoomData.roomId = _this.roomId;
                }
                WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeLeaveRoom, leaveRoomData);
            }
        });
    };
    LeaveDialogController.prototype.show = function (type, backCallback, roomId) {
        if (roomId === void 0) { roomId = 0; }
        this.type = type;
        this.roomId = roomId;
        this.backCallback = backCallback;
        if (type === 0) {
            this.tipContent.string = window.getLocalizedStr('ExitApplication'); //显示完全退出的文案退出
        }
        else {
            this.tipContent.string = window.getLocalizedStr('QuitTheGame'); //显示退出本局游戏的文案
        }
        this.node.active = true;
        this.boardBg.scale = 0;
        // 执行缩放动画
        cc.tween(this.boardBg)
            .to(Config_1.Config.dialogScaleTime, { scale: 1 })
            .start();
    };
    LeaveDialogController.prototype.hide = function () {
        var _this = this;
        if (this.backCallback) {
            this.backCallback();
        }
        // 执行缩放动画
        cc.tween(this.boardBg)
            .to(Config_1.Config.dialogScaleTime, { scale: 0 })
            .call(function () {
            _this.node.active = false;
        })
            .start();
    };
    __decorate([
        property(cc.Node)
    ], LeaveDialogController.prototype, "boardBg", void 0);
    __decorate([
        property(cc.Node)
    ], LeaveDialogController.prototype, "boardBtnClose", void 0);
    __decorate([
        property(cc.Node)
    ], LeaveDialogController.prototype, "contentLay", void 0);
    __decorate([
        property(cc.Node)
    ], LeaveDialogController.prototype, "cancelBtn", void 0);
    __decorate([
        property(cc.Node)
    ], LeaveDialogController.prototype, "leaveBtn", void 0);
    __decorate([
        property(cc.Label)
    ], LeaveDialogController.prototype, "tipContent", void 0);
    LeaveDialogController = __decorate([
        ccclass
    ], LeaveDialogController);
    return LeaveDialogController;
}(cc.Component));
exports.default = LeaveDialogController;

cc._RF.pop();