{"version": 3, "sources": ["assets/scripts/test/RoundStartAnimationTest.ts"], "names": [], "mappings": ";;;;;AAAA,aAAa;AACb,4BAA4B;;;;;;;;;;;;;;;;;;;;;AAEtB,IAAA,KAAwB,EAAE,CAAC,UAAU,EAAnC,OAAO,aAAA,EAAE,QAAQ,cAAkB,CAAC;AAG5C;IAAqD,2CAAY;IAAjE;QAAA,qEA+GC;QA5GG,gBAAU,GAAc,IAAI,CAAC;QAErB,oBAAc,GAAY,IAAI,CAAC;;IA0G3C,CAAC;IAxGG,wCAAM,GAAN;QAAA,iBAgBC;QAfG,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;QAEtD,sBAAsB;QACtB,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAClB,IAAI,CAAC,gBAAgB,EAAE,CAAC;SAC3B;QAED,WAAW;QACX,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAE5B,UAAU;QACV,IAAI,CAAC,YAAY,CAAC;YACd,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;YACzB,KAAI,CAAC,aAAa,EAAE,CAAC;QACzB,CAAC,EAAE,CAAC,CAAC,CAAC;IACV,CAAC;IAEO,kDAAgB,GAAxB;QACI,IAAM,UAAU,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC7C,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;QACnC,IAAM,MAAM,GAAG,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;QAClD,IAAM,KAAK,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACnC,KAAK,CAAC,YAAY,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,MAAM,CAAC;QAC7C,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QAE3B,UAAU,CAAC,WAAW,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QAC/B,UAAU,CAAC,KAAK,GAAG,GAAG,CAAC;QACvB,UAAU,CAAC,MAAM,GAAG,EAAE,CAAC;QAEvB,IAAM,MAAM,GAAG,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACjC,IAAI,MAAM,EAAE;YACR,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;YAC5B,UAAU,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;SACpD;IACL,CAAC;IAEO,sDAAoB,GAA5B;QACI,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QAE1B,IAAI,CAAC,cAAc,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;QAC3D,IAAM,MAAM,GAAG,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACjC,IAAI,MAAM,EAAE;YACR,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;SACxC;QAED,SAAS;QACT,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;QAC1C,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,IAAI,CAAC;QAClC,IAAI,CAAC,cAAc,CAAC,KAAK,GAAG,GAAG,CAAC;QAChC,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,GAAG,CAAC;QAEjC,SAAS;QACT,IAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;QACzD,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;QACtB,KAAK,CAAC,QAAQ,GAAG,EAAE,CAAC;QACpB,KAAK,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC;QAElC,QAAQ;QACR,IAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC;QAC/D,QAAQ,CAAC,SAAS,GAAG,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC;QAClC,QAAQ,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QACnC,QAAQ,CAAC,IAAI,EAAE,CAAC;QAEhB,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,KAAK,CAAC;QACnC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;IAChC,CAAC;IAEO,+CAAa,GAArB;QAAA,iBA6BC;QA5BG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QAE1B,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACtB,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;YAC3B,OAAO;SACV;QAED,YAAY;QACZ,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,IAAI,CAAC;QAClC,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;QAC1C,IAAI,CAAC,cAAc,CAAC,OAAO,GAAG,GAAG,CAAC;QAClC,IAAI,CAAC,cAAc,CAAC,KAAK,GAAG,CAAC,CAAC;QAE9B,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QAE3F,gCAAgC;QAChC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC;aACxB,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC;aACzC,IAAI,CAAC,cAAM,OAAA,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,EAArB,CAAqB,CAAC;aACjC,KAAK,CAAC,GAAG,CAAC;aACV,IAAI,CAAC,cAAM,OAAA,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,EAAtB,CAAsB,CAAC;aAClC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC;aAC1C,IAAI,CAAC;YACF,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;YAC3B,KAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;YAC1C,KAAI,CAAC,cAAc,CAAC,MAAM,GAAG,KAAK,CAAC;QACvC,CAAC,CAAC;aACD,KAAK,EAAE,CAAC;IACjB,CAAC;IAED,uCAAK,GAAL;QACI,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;SAC9D;IACL,CAAC;IA3GD;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;+DACS;IAHZ,uBAAuB;QAD3C,OAAO;OACa,uBAAuB,CA+G3C;IAAD,8BAAC;CA/GD,AA+GC,CA/GoD,EAAE,CAAC,SAAS,GA+GhE;kBA/GoB,uBAAuB", "file": "", "sourceRoot": "/", "sourcesContent": ["// 回合开始动画测试脚本\n// 这个脚本可以附加到任何节点上，用于测试回合开始动画\n\nconst { ccclass, property } = cc._decorator;\n\n@ccclass\nexport default class RoundStartAnimationTest extends cc.Component {\n\n    @property(cc.Button)\n    testButton: cc.Button = null;\n\n    private roundStartNode: cc.Node = null;\n\n    onLoad() {\n        console.log(\"=== RoundStartAnimationTest onLoad ===\");\n        \n        // 创建测试按钮（如果没有在编辑器中设置）\n        if (!this.testButton) {\n            this.createTestButton();\n        }\n\n        // 创建回合开始节点\n        this.createRoundStartNode();\n        \n        // 3秒后自动测试\n        this.scheduleOnce(() => {\n            console.log(\"3秒后自动测试动画\");\n            this.testAnimation();\n        }, 3);\n    }\n\n    private createTestButton() {\n        const buttonNode = new cc.Node(\"TestButton\");\n        buttonNode.addComponent(cc.Button);\n        const sprite = buttonNode.addComponent(cc.Sprite);\n        const label = new cc.Node(\"Label\");\n        label.addComponent(cc.Label).string = \"测试动画\";\n        buttonNode.addChild(label);\n        \n        buttonNode.setPosition(0, 200);\n        buttonNode.width = 200;\n        buttonNode.height = 80;\n        \n        const canvas = cc.find(\"Canvas\");\n        if (canvas) {\n            canvas.addChild(buttonNode);\n            buttonNode.on('click', this.testAnimation, this);\n        }\n    }\n\n    private createRoundStartNode() {\n        console.log(\"创建回合开始测试节点\");\n        \n        this.roundStartNode = new cc.Node('round_start_test_node');\n        const canvas = cc.find('Canvas');\n        if (canvas) {\n            canvas.addChild(this.roundStartNode);\n        }\n\n        // 设置节点属性\n        this.roundStartNode.setPosition(-750, -1);\n        this.roundStartNode.zIndex = 1000;\n        this.roundStartNode.width = 200;\n        this.roundStartNode.height = 100;\n\n        // 添加文本标签\n        const label = this.roundStartNode.addComponent(cc.Label);\n        label.string = \"回合开始\";\n        label.fontSize = 48;\n        label.node.color = cc.Color.WHITE;\n        \n        // 添加背景色\n        const graphics = this.roundStartNode.addComponent(cc.Graphics);\n        graphics.fillColor = cc.Color.RED;\n        graphics.rect(-100, -50, 200, 100);\n        graphics.fill();\n\n        this.roundStartNode.active = false;\n        console.log(\"回合开始测试节点创建完成\");\n    }\n\n    private testAnimation() {\n        console.log(\"开始测试回合开始动画\");\n        \n        if (!this.roundStartNode) {\n            console.error(\"回合开始节点不存在\");\n            return;\n        }\n\n        // 显示节点并开始动画\n        this.roundStartNode.active = true;\n        this.roundStartNode.setPosition(-750, -1);\n        this.roundStartNode.opacity = 255;\n        this.roundStartNode.scale = 1;\n\n        console.log(\"节点状态 - 位置:\", this.roundStartNode.position, \"可见:\", this.roundStartNode.active);\n\n        // 执行动画：0.5秒移动到中间，1秒展示，0.5秒移动到右边\n        cc.tween(this.roundStartNode)\n            .to(0.5, { x: 0 }, { easing: 'quartOut' })\n            .call(() => console.log(\"到达中间位置\"))\n            .delay(1.0)\n            .call(() => console.log(\"开始移动到右边\"))\n            .to(0.5, { x: 750 }, { easing: 'quartIn' })\n            .call(() => {\n                console.log(\"动画完成，恢复初始状态\");\n                this.roundStartNode.setPosition(-750, -1);\n                this.roundStartNode.active = false;\n            })\n            .start();\n    }\n\n    start() {\n        if (this.testButton) {\n            this.testButton.node.on('click', this.testAnimation, this);\n        }\n    }\n}\n"]}