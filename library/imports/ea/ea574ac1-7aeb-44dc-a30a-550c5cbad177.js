"use strict";
cc._RF.push(module, 'ea574rBeutE3KMKVQxcutF3', 'RoundStartAnimationTest');
// scripts/test/RoundStartAnimationTest.ts

"use strict";
// 回合开始动画测试脚本
// 这个脚本可以附加到任何节点上，用于测试回合开始动画
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var RoundStartAnimationTest = /** @class */ (function (_super) {
    __extends(RoundStartAnimationTest, _super);
    function RoundStartAnimationTest() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.testButton = null;
        _this.roundStartNode = null;
        return _this;
    }
    RoundStartAnimationTest.prototype.onLoad = function () {
        var _this = this;
        console.log("=== RoundStartAnimationTest onLoad ===");
        // 创建测试按钮（如果没有在编辑器中设置）
        if (!this.testButton) {
            this.createTestButton();
        }
        // 创建回合开始节点
        this.createRoundStartNode();
        // 3秒后自动测试
        this.scheduleOnce(function () {
            console.log("3秒后自动测试动画");
            _this.testAnimation();
        }, 3);
    };
    RoundStartAnimationTest.prototype.createTestButton = function () {
        var buttonNode = new cc.Node("TestButton");
        buttonNode.addComponent(cc.Button);
        var sprite = buttonNode.addComponent(cc.Sprite);
        var label = new cc.Node("Label");
        label.addComponent(cc.Label).string = "测试动画";
        buttonNode.addChild(label);
        buttonNode.setPosition(0, 200);
        buttonNode.width = 200;
        buttonNode.height = 80;
        var canvas = cc.find("Canvas");
        if (canvas) {
            canvas.addChild(buttonNode);
            buttonNode.on('click', this.testAnimation, this);
        }
    };
    RoundStartAnimationTest.prototype.createRoundStartNode = function () {
        console.log("创建回合开始测试节点");
        this.roundStartNode = new cc.Node('round_start_test_node');
        var canvas = cc.find('Canvas');
        if (canvas) {
            canvas.addChild(this.roundStartNode);
        }
        // 设置节点属性
        this.roundStartNode.setPosition(-750, -1);
        this.roundStartNode.zIndex = 1000;
        this.roundStartNode.width = 200;
        this.roundStartNode.height = 100;
        // 添加文本标签
        var label = this.roundStartNode.addComponent(cc.Label);
        label.string = "回合开始";
        label.fontSize = 48;
        label.node.color = cc.Color.WHITE;
        // 添加背景色
        var graphics = this.roundStartNode.addComponent(cc.Graphics);
        graphics.fillColor = cc.Color.RED;
        graphics.rect(-100, -50, 200, 100);
        graphics.fill();
        this.roundStartNode.active = false;
        console.log("回合开始测试节点创建完成");
    };
    RoundStartAnimationTest.prototype.testAnimation = function () {
        var _this = this;
        console.log("开始测试回合开始动画");
        if (!this.roundStartNode) {
            console.error("回合开始节点不存在");
            return;
        }
        // 显示节点并开始动画
        this.roundStartNode.active = true;
        this.roundStartNode.setPosition(-750, -1);
        this.roundStartNode.opacity = 255;
        this.roundStartNode.scale = 1;
        console.log("节点状态 - 位置:", this.roundStartNode.position, "可见:", this.roundStartNode.active);
        // 执行动画：0.5秒移动到中间，1秒展示，0.5秒移动到右边
        cc.tween(this.roundStartNode)
            .to(0.5, { x: 0 }, { easing: 'quartOut' })
            .call(function () { return console.log("到达中间位置"); })
            .delay(1.0)
            .call(function () { return console.log("开始移动到右边"); })
            .to(0.5, { x: 750 }, { easing: 'quartIn' })
            .call(function () {
            console.log("动画完成，恢复初始状态");
            _this.roundStartNode.setPosition(-750, -1);
            _this.roundStartNode.active = false;
        })
            .start();
    };
    RoundStartAnimationTest.prototype.start = function () {
        if (this.testButton) {
            this.testButton.node.on('click', this.testAnimation, this);
        }
    };
    __decorate([
        property(cc.Button)
    ], RoundStartAnimationTest.prototype, "testButton", void 0);
    RoundStartAnimationTest = __decorate([
        ccclass
    ], RoundStartAnimationTest);
    return RoundStartAnimationTest;
}(cc.Component));
exports.default = RoundStartAnimationTest;

cc._RF.pop();