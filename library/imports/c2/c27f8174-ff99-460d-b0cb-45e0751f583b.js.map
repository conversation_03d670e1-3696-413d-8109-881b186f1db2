{"version": 3, "sources": ["assets/scripts/game/GamePageInitializer.ts"], "names": [], "mappings": ";;;;;AAAA,wCAAwC;AACxC,8CAA8C;;;;;;;;;;;;;;;;;;;;;AAE9C,2DAAsD;AAEhD,IAAA,KAAwB,EAAE,CAAC,UAAU,EAAnC,OAAO,aAAA,EAAE,QAAQ,cAAkB,CAAC;AAG5C;IAAiD,uCAAY;IAA7D;;IA6CA,CAAC;IA3CG,oCAAM,GAAN;QACI,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;QAErD,8BAA8B;QAC9B,IAAK,MAAc,CAAC,kBAAkB,EAAE;YACpC,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;YACxC,OAAO;SACV;QAED,0BAA0B;QAC1B,IAAI,CAAC,4BAA4B,EAAE,CAAC;QAEpC,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;IACzD,CAAC;IAEO,0DAA4B,GAApC;QACI,kCAAkC;QAClC,IAAM,kBAAkB,GAAG,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC,4BAAkB,CAAC,CAAC;QAC9E,IAAI,kBAAkB,EAAE;YACpB,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;YACtC,MAAc,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;YACxD,OAAO;SACV;QAED,uCAAuC;QACvC,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;QACtC,IAAM,YAAY,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;QACvD,IAAM,MAAM,GAAG,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACjC,IAAI,MAAM,EAAE;YACR,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;YAC9B,IAAM,YAAU,GAAG,YAAY,CAAC,YAAY,CAAC,4BAAkB,CAAC,CAAC;YAChE,MAAc,CAAC,kBAAkB,GAAG,YAAU,CAAC;YAChD,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;YAE3C,SAAS;YACR,MAAc,CAAC,kBAAkB,GAAG;gBACjC,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;gBACzB,YAAU,CAAC,uBAAuB,EAAE,CAAC;YACzC,CAAC,CAAC;SACL;aAAM;YACH,OAAO,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;SAChC;IACL,CAAC;IA5CgB,mBAAmB;QADvC,OAAO;OACa,mBAAmB,CA6CvC;IAAD,0BAAC;CA7CD,AA6CC,CA7CgD,EAAE,CAAC,SAAS,GA6C5D;kBA7CoB,mBAAmB", "file": "", "sourceRoot": "/", "sourcesContent": ["// 游戏页面初始化器 - 确保GamePageController被正确初始化\n// 这个脚本可以附加到任何节点上，它会自动创建和初始化GamePageController\n\nimport GamePageController from \"./GamePageController\";\n\nconst { ccclass, property } = cc._decorator;\n\n@ccclass\nexport default class GamePageInitializer extends cc.Component {\n\n    onLoad() {\n        console.log(\"=== GamePageInitializer onLoad 开始 ===\");\n        \n        // 检查是否已经有GamePageController实例\n        if ((window as any).gamePageController) {\n            console.log(\"GamePageController 实例已存在\");\n            return;\n        }\n\n        // 查找或创建GamePageController\n        this.initializeGamePageController();\n        \n        console.log(\"=== GamePageInitializer onLoad 完成 ===\");\n    }\n\n    private initializeGamePageController() {\n        // 首先尝试在场景中查找现有的GamePageController\n        const existingController = cc.find(\"Canvas\").getComponent(GamePageController);\n        if (existingController) {\n            console.log(\"找到现有的GamePageController\");\n            (window as any).gamePageController = existingController;\n            return;\n        }\n\n        // 如果没有找到，创建一个新的节点并添加GamePageController\n        console.log(\"创建新的GamePageController\");\n        const gamePageNode = new cc.Node(\"GamePageController\");\n        const canvas = cc.find(\"Canvas\");\n        if (canvas) {\n            canvas.addChild(gamePageNode);\n            const controller = gamePageNode.addComponent(GamePageController);\n            (window as any).gamePageController = controller;\n            console.log(\"GamePageController 创建并初始化完成\");\n            \n            // 暴露测试方法\n            (window as any).testRoundAnimation = () => {\n                console.log(\"全局测试方法被调用\");\n                controller.testRoundStartAnimation();\n            };\n        } else {\n            console.error(\"找不到Canvas节点\");\n        }\n    }\n}\n"]}