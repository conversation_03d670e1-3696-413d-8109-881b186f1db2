{"version": 3, "sources": ["assets/scripts/util/AudioMgr.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,mCAAkC;AAClC,2CAA0C;AAGpC,IAAA,KAAwB,EAAE,CAAC,UAAU,EAAnC,OAAO,aAAA,EAAE,QAAQ,cAAkB,CAAC;AAE5C,eAAe;AACf,IAAM,UAAU,GAAW,CAAC,CAAA;AAE5B;;GAEG;AAEH;IAAA;QAWI,YAAY;QACJ,qBAAgB,GAAY,IAAI,CAAC;QACzC,aAAa;QACL,WAAM,GAAmB,IAAI,CAAA;QACrC,cAAc;QACN,YAAO,GAAqB,IAAI,CAAA;QACxC,mBAAmB;QACX,iBAAY,GAAW,CAAC,CAAA;QAChC,qBAAqB;QACb,iBAAY,GAAW,CAAC,CAAA;QAChC,UAAU;QACF,kBAAa,GAAW,CAAC,CAAA;QACjC,YAAY;QACJ,kBAAa,GAAW,CAAC,CAAA;QACjC,kCAAkC;QAC1B,iBAAY,GAAW,CAAC,CAAA;QAChC,iBAAiB;QACT,oBAAe,GAAW,EAAE,CAAA;IAmMxC,CAAC;iBA/NY,QAAQ;IAGjB,sBAAW,eAAG;aAAd;YACI,IAAI,IAAI,CAAC,SAAS,EAAE;gBAChB,OAAO,IAAI,CAAC,SAAS,CAAC;aACzB;YACD,IAAI,CAAC,SAAS,GAAG,IAAI,UAAQ,EAAE,CAAC;YAChC,OAAO,IAAI,CAAC,SAAS,CAAC;QAC1B,CAAC;;;OAAA;IAsBD,uBAAI,GAAJ;QACI,IAAI,IAAI,CAAC,gBAAgB;YAAE,OAAO,CAAC,aAAa;QAChD,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA,CAAA,mCAAmC;QAC9E,EAAE,CAAC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAA;QACjD,IAAI,CAAC,OAAO,GAAG,EAAE,CAAA;QACjB,IAAI,CAAC,WAAW,GAAG,IAAI,uBAAU,EAAwB,CAAA;QACzD;;;WAGG;QACH,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,EAAE,CAAC,WAAW,CAAC,CAAA;QAChE,UAAU;QACV,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,CAAA;QACvC,aAAa;QACb,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAA;QACpE,YAAY;QACZ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE;YACjC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,EAAE,CAAC,WAAW,CAAC,CAAA;YACpE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,CAAA;YAC3C,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAA;SAC3E;IACL,CAAC;IAED;;;;OAIG;IACH,4BAAS,GAAT,UAAU,SAAiB,EAAE,MAAsB;QAAnD,iBA6BC;QA7B4B,uBAAA,EAAA,aAAsB;QAC/C,IAAI,IAAI,CAAC,eAAe,IAAI,SAAS,EAAE;YACnC,OAAM;SACT;QACD,IAAI,IAAI,GAAG,UAAC,IAAI;YACZ,0BAA0B;YAC1B,KAAI,CAAC,MAAM,CAAC,IAAI,GAAG,IAAI,CAAA;YACvB,KAAI,CAAC,MAAM,CAAC,IAAI,GAAG,MAAM,CAAA;YACzB,KAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAA;YAClB,IAAI,CAAC,KAAI,CAAC,WAAW,CAAC,WAAW,CAAC,SAAS,CAAC,EAAE;gBAC1C,KAAI,CAAC,WAAW,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,CAAA;aACxC;QACL,CAAC,CAAA;QACD,IAAI,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,SAAS,CAAC,EAAE;YACzC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAA;SAC7C;aAAM;YACH,IAAI,UAAU,GAAG,WAAW,CAAA;YAC5B,EAAE,CAAC,YAAY,CAAC,UAAU,CAAC,UAAU,EAAC,UAAC,GAAG,EAAE,MAAM;gBAC9C,MAAM,CAAC,IAAI,CAAC,eAAM,CAAC,KAAK,GAAC,SAAS,EAAC,EAAE,CAAC,SAAS,EAAC,UAAC,GAAQ,EAAE,IAAS;oBAChE,IAAI,GAAG,EAAE;wBACL,OAAO,CAAC,KAAK,CAAC,eAAe,GAAG,GAAG,CAAC,CAAC;qBACxC;yBAAI;wBACD,IAAI,CAAC,IAAI,CAAC,CAAA;qBACb;gBACL,CAAC,CAAC,CAAC;YAEP,CAAC,CAAC,CAAA;SAEL;IACL,CAAC;IAED;;;;OAIG;IACH,4BAAS,GAAT,UAAU,SAAiB,EAAE,MAAuB;QAApD,iBA0BC;QA1B4B,uBAAA,EAAA,cAAuB;QAChD,IAAI,IAAI,GAAG,UAAC,IAAI;YACZ,8CAA8C;YAC9C,KAAI,CAAC,OAAO,CAAC,KAAI,CAAC,YAAY,CAAC,CAAC,IAAI,GAAG,IAAI,CAAA;YAC3C,KAAI,CAAC,OAAO,CAAC,KAAI,CAAC,YAAY,CAAC,CAAC,IAAI,GAAG,MAAM,CAAA;YAC7C,KAAI,CAAC,OAAO,CAAC,KAAI,CAAC,YAAY,CAAC,CAAC,IAAI,EAAE,CAAA;YACtC,IAAI,CAAC,KAAI,CAAC,WAAW,CAAC,WAAW,CAAC,SAAS,CAAC,EAAE;gBAC1C,KAAI,CAAC,WAAW,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,CAAA;aACxC;YACD,KAAI,CAAC,YAAY,GAAG,KAAI,CAAC,YAAY,GAAG,CAAC,IAAI,KAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAI,CAAC,YAAY,GAAG,CAAC,CAAA;QAChG,CAAC,CAAA;QACD,IAAI,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,SAAS,CAAC,EAAE;YACzC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAA;SAC7C;aAAM;YACH,IAAI,UAAU,GAAG,WAAW,CAAA;YAC5B,EAAE,CAAC,YAAY,CAAC,UAAU,CAAC,UAAU,EAAC,UAAC,GAAG,EAAE,MAAM;gBAC9C,MAAM,CAAC,IAAI,CAAC,eAAM,CAAC,KAAK,GAAC,SAAS,EAAC,EAAE,CAAC,SAAS,EAAC,UAAC,GAAQ,EAAE,IAAS;oBAChE,IAAI,GAAG,EAAE;wBACL,OAAO,CAAC,KAAK,CAAC,eAAe,GAAG,GAAG,CAAC,CAAC;qBACxC;yBAAI;wBACD,IAAI,CAAC,IAAI,CAAC,CAAA;qBACb;gBACL,CAAC,CAAC,CAAC;YAEP,CAAC,CAAC,CAAA;SACL;IACL,CAAC;IAED;;OAEG;IACH,4BAAS,GAAT;QACI,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAA;QAClB,0BAA0B;IAC9B,CAAC;IAED;;OAEG;IACH,+BAAY,GAAZ;QACI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC1C,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAA;YACtB,8BAA8B;SACjC;QACD,IAAI,CAAC,YAAY,GAAG,CAAC,CAAA;IACzB,CAAC;IAED;;;OAGG;IACH,+BAAY,GAAZ,UAAa,IAAa;QACtB,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,CAAC,EAAE;YAClC,OAAM;SACT;QACD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QAChC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAA;QAClD,6BAA6B;IACjC,CAAC;IAED;;;OAGG;IACH,+BAAY,GAAZ,UAAa,IAAa;QACtB,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,CAAC,EAAE;YAClC,OAAM;SACT;QACD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QAChC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC1C,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAA;SACzD;QACD,6BAA6B;IACjC,CAAC;IAED;;;OAGG;IACH,iCAAc,GAAd,UAAe,KAAa;QACxB,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,KAAK,CAAA;QAC1B,IAAI,CAAC,aAAa,GAAG,KAAK,CAAA;QAC1B,gCAAgC;IACpC,CAAC;IAED;;;OAGG;IACH,iCAAc,GAAd,UAAe,KAAa;QACxB,IAAI,CAAC,aAAa,GAAG,KAAK,CAAA;QAC1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC1C,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,KAAK,CAAA;SACjC;QACD,gCAAgC;IACpC,CAAC;IAED;;;OAGG;IACH,+BAAY,GAAZ;QACI,OAAO,IAAI,CAAC,YAAY,CAAA;IAC5B,CAAC;IAED;;;OAGG;IACH,+BAAY,GAAZ;QACI,OAAO,IAAI,CAAC,YAAY,CAAA;IAC5B,CAAC;IAED;;;OAGG;IACH,iCAAc,GAAd;QACI,OAAO,IAAI,CAAC,aAAa,CAAA;IAC7B,CAAC;IAED;;;OAGG;IACH,iCAAc,GAAd;QACI,OAAO,IAAI,CAAC,aAAa,CAAA;IAC7B,CAAC;;IA9NQ,QAAQ;QADpB,OAAO,CAAC,UAAU,CAAC;OACP,QAAQ,CA+NpB;IAAD,eAAC;CA/ND,AA+NC,IAAA;AA/NY,4BAAQ", "file": "", "sourceRoot": "/", "sourcesContent": ["import { Config } from \"./Config\";\nimport { Dictionary } from \"./Dictionary\";\n\n \nconst { ccclass, property } = cc._decorator;\n \n/**最多有几个音效播放器*/\nconst MAX_SOUNDS: number = 8\n \n/**\n * 音效管理器\n */\n@ccclass(\"AudioMgr\")\nexport class AudioMgr {\n \n    static _instance: AudioMgr;\n    static get ins() {\n        if (this._instance) {\n            return this._instance;\n        }\n        this._instance = new AudioMgr();\n        return this._instance;\n    }\n \n    /**音效的常驻节点*/\n    private _persistRootNode: cc.Node = null;\n    //bgm音效全局唯一一个\n    private _music: cc.AudioSource = null\n    //sound音效可以有多个\n    private _sounds: cc.AudioSource[] = null\n    /**bgm静音 0没静音 1静音*/\n    private _music_muted: number = 0\n    /**sound静音 0没静音 1静音*/\n    private _sound_muted: number = 0\n    /**bgm音量*/\n    private _music_volume: number = 1\n    /**sound音量*/\n    private _sound_volume: number = 1\n    /**当前播放的音效索引用以控制使用不同的AudioSource*/\n    private _now_soundid: number = 0\n    /**当前播放的bgm音效名字*/\n    private _cur_music_name: string = \"\"\n    //存储所有的音效片段\n    private music_clips: Dictionary<string, cc.AudioClip>\n    init() {\n        if (this._persistRootNode) return; //避免切换场景初始化报错\n        this._persistRootNode = cc.find(\"DataNode\")//这个节点是常驻节点，如果不是的话 就用下面那行代码把它变成常驻节点\n        cc.game.addPersistRootNode(this._persistRootNode)\n        this._sounds = []\n        this.music_clips = new Dictionary<string, cc.AudioClip>()\n        /**\n         * 读取本地存储的数据\n         * \n         */\n        this._music = this._persistRootNode.addComponent(cc.AudioSource)\n        //获取bgm的音量\n        this._music.volume = this._music_volume\n        //获取bgm是否存储静音\n        this._music.volume = this._music_muted == 1 ? 0 : this._music_volume\n        //获取sounds列表\n        for (let i = 0; i < MAX_SOUNDS; i++) {\n            this._sounds[i] = this._persistRootNode.addComponent(cc.AudioSource)\n            this._sounds[i].volume = this._sound_volume\n            this._sounds[i].volume = this._sound_muted == 1 ? 0 : this._sound_volume\n        }\n    }\n \n    /**\n     * @param audioName 音效名字\n     * @param isLoop 是否循环播放\n     * @protected 播放音效\n     */\n    playMusic(audioName: string, isLoop: boolean = true) {\n        if (this._cur_music_name == audioName) {\n            return\n        }\n        let call = (clip) => {\n            // this._music.clip = null\n            this._music.clip = clip\n            this._music.loop = isLoop\n            this._music.play()\n            if (!this.music_clips.containsKey(audioName)) {\n                this.music_clips.add(audioName, clip)\n            }\n        }\n        if (this.music_clips.containsKey(audioName)) {\n            call(this.music_clips.getValue(audioName))\n        } else {\n            let bundleName = \"resources\"\n            cc.assetManager.loadBundle(bundleName,(err, bundle) => {\n                bundle.load(Config.audio+audioName,cc.AudioClip,(err: any, clip: any)=>{\n                    if (err) {\n                        console.error(\"loadAudioClip\" + err);\n                    }else{\n                        call(clip)\n                    }\n                });\n                \n            })\n        \n        }\n    }\n \n    /**\n     * @param audioName 音效名字\n     * @param isLoop 是否循环播放\n     * @protected 播放音效\n     */\n    playSound(audioName: string, isLoop: boolean = false) {\n        let call = (clip) => {\n            // this._sounds[this._now_soundid].clip = null\n            this._sounds[this._now_soundid].clip = clip\n            this._sounds[this._now_soundid].loop = isLoop\n            this._sounds[this._now_soundid].play()\n            if (!this.music_clips.containsKey(audioName)) {\n                this.music_clips.add(audioName, clip)\n            }\n            this._now_soundid = this._now_soundid + 1 >= this._sounds.length ? 0 : this._now_soundid + 1\n        }\n        if (this.music_clips.containsKey(audioName)) {\n            call(this.music_clips.getValue(audioName))\n        } else {\n            let bundleName = \"resources\"\n            cc.assetManager.loadBundle(bundleName,(err, bundle) => {\n                bundle.load(Config.audio+audioName,cc.AudioClip,(err: any, clip: any)=>{\n                    if (err) {\n                        console.error(\"loadAudioClip\" + err);\n                    }else{\n                        call(clip)\n                    }\n                });\n                \n            })\n        }\n    }\n \n    /**\n     * 停止播放bgm\n     */\n    stopMusic() {\n        this._music.stop()\n        // this._music.clip = null\n    }\n \n    /**\n     * 停止播放所有的sound\n     */\n    stopAllSound() {\n        for (let i = 0; i < this._sounds.length; i++) {\n            this._sounds[i].stop()\n            // this._sounds[i].clip = null\n        }\n        this._now_soundid = 0\n    }\n \n    /**\n     * \n     * @param mute 是否静音music\n     */\n    setMusicMute(mute: boolean) {\n        if (mute == (this._music_muted == 1)) {\n            return\n        }\n        this._music_muted = mute ? 1 : 0\n        this._music.volume = mute ? this._music_volume : 0\n        //存储music静音 this._music_muted\n    }\n \n    /**\n     * \n     * @param mute 是否静音sound\n     */\n    setSoundMute(mute: boolean) {\n        if (mute == (this._sound_muted == 1)) {\n            return\n        }\n        this._sound_muted = mute ? 1 : 0\n        for (let i = 0; i < this._sounds.length; i++) {\n            this._sounds[i].volume = mute ? this._sound_volume : 0\n        }\n        //存储sound静音 this._sound_muted\n    }\n \n    /**\n     * \n     * @param value 设置音乐声音大小\n     */\n    setMusicVolume(value: number) {\n        this._music.volume = value\n        this._music_volume = value\n        //存储music音量大小 this._music_volume\n    }\n \n    /**\n     * \n     * @param value 设置sound声音大小\n     */\n    setSoundVolume(value: number) {\n        this._sound_volume = value\n        for (let i = 0; i < this._sounds.length; i++) {\n            this._sounds[i].volume = value\n        }\n        //存储sound音量大小 this._sound_volume\n    }\n \n    /**\n     * \n     * @returns 返回bgm静音状态\n     */\n    getMusicMute() {\n        return this._music_muted\n    }\n \n    /**\n     * \n     * @returns 返回sound音效静音状态\n     */\n    getSoundMute() {\n        return this._sound_muted\n    }\n \n    /**\n     * \n     * @returns 返回bgm声音大小\n     */\n    getMusicVolume() {\n        return this._music_volume\n    }\n \n    /**\n     * \n     * @returns 返回sound音效声音大小\n     */\n    getSoundVolume() {\n        return this._sound_volume\n    }\n}"]}