"use strict";
cc._RF.push(module, 'f764exjceBDhJY0TWFTY4tZ', 'StartUpPageController');
// scripts/start_up/StartUpPageController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var GameMgr_1 = require("../common/GameMgr");
var StartUpCenterController_1 = require("./StartUpCenterController");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var StartUpPageController = /** @class */ (function (_super) {
    __extends(StartUpPageController, _super);
    function StartUpPageController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.startUpCenterController = null;
        return _this;
        // update (dt) {}
    }
    StartUpPageController.prototype.start = function () {
        //这是是通知 web 端 游戏加载完成
        this.scheduleOnce(function () {
            if (window.closeLoadingBg) {
                window.closeLoadingBg();
            }
            GameMgr_1.GameMgr.H5SDK.HideLoading();
        }, 0.1);
    };
    //设置登录成功
    StartUpPageController.prototype.setLogin = function () {
        this.startUpCenterController.setLogin();
    };
    __decorate([
        property(StartUpCenterController_1.default)
    ], StartUpPageController.prototype, "startUpCenterController", void 0);
    StartUpPageController = __decorate([
        ccclass
    ], StartUpPageController);
    return StartUpPageController;
}(cc.Component));
exports.default = StartUpPageController;

cc._RF.pop();