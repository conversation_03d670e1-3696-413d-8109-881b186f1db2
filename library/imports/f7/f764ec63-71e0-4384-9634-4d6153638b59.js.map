{"version": 3, "sources": ["assets/scripts/start_up/StartUpPageController.ts"], "names": [], "mappings": ";;;;;AAAA,oBAAoB;AACpB,4EAA4E;AAC5E,mBAAmB;AACnB,sFAAsF;AACtF,8BAA8B;AAC9B,sFAAsF;;;;;;;;;;;;;;;;;;;;;AAGtF,6CAA4C;AAC5C,qEAAgE;AAE1D,IAAA,KAAwB,EAAE,CAAC,UAAU,EAAnC,OAAO,aAAA,EAAE,QAAQ,cAAkB,CAAC;AAG5C;IAAmD,yCAAY;IAA/D;QAAA,qEAsBC;QAnBG,6BAAuB,GAA4B,IAAI,CAAA;;QAkBvD,iBAAiB;IACrB,CAAC;IAjBG,qCAAK,GAAL;QACI,oBAAoB;QACpB,IAAI,CAAC,YAAY,CAAC;YACd,IAAU,MAAO,CAAC,cAAc,EAAE;gBACxB,MAAO,CAAC,cAAc,EAAE,CAAC;aAClC;YACD,iBAAO,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;QAChC,CAAC,EAAE,GAAG,CAAC,CAAC;IAEZ,CAAC;IAED,QAAQ;IACR,wCAAQ,GAAR;QACI,IAAI,CAAC,uBAAuB,CAAC,QAAQ,EAAE,CAAA;IAC3C,CAAC;IAhBD;QADC,QAAQ,CAAC,iCAAuB,CAAC;0EACqB;IAHtC,qBAAqB;QADzC,OAAO;OACa,qBAAqB,CAsBzC;IAAD,4BAAC;CAtBD,AAsBC,CAtBkD,EAAE,CAAC,SAAS,GAsB9D;kBAtBoB,qBAAqB", "file": "", "sourceRoot": "/", "sourcesContent": ["// Learn TypeScript:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html\n// Learn Attribute:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html\n// Learn life-cycle callbacks:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html\n\n\nimport { GameMgr } from \"../common/GameMgr\";\nimport StartUpCenterController from \"./StartUpCenterController\";\n\nconst { ccclass, property } = cc._decorator;\n\n@ccclass\nexport default class StartUpPageController extends cc.Component {\n\n    @property(StartUpCenterController)\n    startUpCenterController: StartUpCenterController = null\n\n    start() {\n        //这是是通知 web 端 游戏加载完成\n        this.scheduleOnce(() => {\n            if ((<any>window).closeLoadingBg) {\n                (<any>window).closeLoadingBg();\n            }\n            GameMgr.H5SDK.HideLoading();\n        }, 0.1);\n\n    }\n\n    //设置登录成功\n    setLogin() {\n        this.startUpCenterController.setLogin()\n    }\n\n    // update (dt) {}\n}\n"]}