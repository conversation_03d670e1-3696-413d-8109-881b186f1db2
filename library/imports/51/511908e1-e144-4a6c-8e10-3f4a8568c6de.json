{"__type__": "cc.TextAsset", "_name": "SingleChessBoardController_README", "_objFlags": 0, "_native": "", "text": "# 单机模式棋盘控制器使用说明\n\n## 概述\n\n`SingleChessBoardController` 是基于联机四边形棋盘控制器 `ChessBoardController` 开发的单机模式棋盘控制器。它支持5种不同大小的棋盘，并实现了单机扫雷游戏的核心功能。\n\n## 主要特性\n\n### 1. 支持5种棋盘类型\n- **8x8棋盘**: 大小752x752，格子大小88x88\n- **8x9棋盘**: 大小752x845，格子大小88x88  \n- **9x9棋盘**: 大小752x747，格子大小76x76\n- **9x10棋盘**: 大小752x830，格子大小78x78\n- **10x10棋盘**: 大小752x745，格子大小69x69\n\n### 2. 核心功能\n- ✅ 点击格子发送 `LevelClickBlock` 消息\n- ✅ 长按格子生成 `biaoji` 预制体\n- ✅ 根据服务器响应更新棋盘状态\n- ✅ 接收 `ExtendLevelInfo` 和 `LevelGameEnd` 时显示所有隐藏格子并清除预制体\n- ✅ 不生成头像预制体（单机模式特性）\n- ✅ 删除加减分逻辑（单机模式特性）\n\n### 3. 预制体管理\n- `boomPrefab`: 地雷爆炸预制体\n- `biaojiPrefab`: 标记预制体\n- `boom1Prefab` ~ `boom8Prefab`: 数字1-8预制体\n\n## 使用方法\n\n### 1. 在编辑器中配置\n\n```typescript\n// 在 Inspector 中配置以下预制体和棋盘节点\n@property(cc.Prefab)\nboomPrefab: cc.Prefab = null;\n\n@property(cc.Prefab)\nbiaojiPrefab: cc.Prefab = null;\n\n@property(cc.Prefab)\nboom1Prefab: cc.Prefab = null;\n// ... boom2Prefab 到 boom8Prefab\n\n// 五个棋盘节点\n@property(cc.Node)\nqipan8x8Node: cc.Node = null;\n\n@property(cc.Node)\nqipan8x9Node: cc.Node = null;\n\n@property(cc.Node)\nqipan9x9Node: cc.Node = null;\n\n@property(cc.Node)\nqipan9x10Node: cc.Node = null;\n\n@property(cc.Node)\nqipan10x10Node: cc.Node = null;\n```\n\n### 2. 初始化棋盘\n\n```typescript\n// 初始化指定类型的棋盘，控制器会自动选择对应的棋盘节点\nsingleChessBoardController.initBoard(\"8x8\");  // 或 \"8x9\", \"9x9\", \"9x10\", \"10x10\"\n```\n\n### 3. 处理服务器响应\n\n```typescript\n// 处理点击响应\nsingleChessBoardController.handleClickResponse(x, y, result);\n\n// 处理连锁反应\nsingleChessBoardController.handleChainReaction(revealedGrids);\n\n// 处理游戏结束\nsingleChessBoardController.onExtendLevelInfo();\nsingleChessBoardController.onLevelGameEnd();\n```\n\n## 消息协议\n\n### 发送消息\n\n#### LevelClickBlock\n```typescript\n{\n    x: number,      // 格子x坐标\n    y: number,      // 格子y坐标  \n    action: number  // 1=挖掘，2=标记\n}\n```\n\n### 接收消息\n\n#### LevelClickBlock 响应\n```typescript\n{\n    x: number,                    // 点击的格子坐标\n    y: number,\n    result: \"boom\" | number,      // \"boom\"表示地雷，数字表示周围地雷数\n    chainReaction?: Array<{       // 连锁反应的格子列表\n        x: number,\n        y: number,\n        neighborMines: number\n    }>\n}\n```\n\n#### ExtendLevelInfo\n游戏开始时接收，触发显示所有格子并清除预制体\n\n#### LevelGameEnd  \n游戏结束时接收，触发显示所有格子并清除预制体\n\n## 与联机版本的区别\n\n### 相同点\n- 格子查找逻辑：通过计算位置获取格子坐标\n- 触摸事件处理：点击和长按的实现方式\n- 预制体生成：boom、biaoji、数字预制体的创建\n- 动画效果：格子隐藏、预制体出现动画\n\n### 不同点\n- ❌ 不生成头像预制体\n- ❌ 删除加减分逻辑  \n- ✅ 支持5种不同大小的棋盘\n- ✅ 发送 `LevelClickBlock` 而不是 `ClickBlock`\n- ✅ 处理 `ExtendLevelInfo` 和 `LevelGameEnd` 消息\n\n## 集成示例\n\n### 在 LevelPageController 中使用\n\n```typescript\n// 配置单个棋盘控制器\n@property(SingleChessBoardController)\nsingleChessBoardController: SingleChessBoardController = null;\n\n// 根据关卡设置棋盘\nprivate enterLevel(levelNumber: number) {\n    if (this.singleChessBoardController) {\n        // 根据关卡设置棋盘类型，控制器会自动选择对应的棋盘节点\n        const boardType = this.getBoardTypeByLevel(levelNumber);\n        this.singleChessBoardController.initBoard(boardType);\n        this.currentSingleChessBoard = this.singleChessBoardController;\n    }\n}\n\n// 处理消息（通过EventCenter监听）\nprivate onReceiveMessage(messageBean: ReceivedMessageBean) {\n    switch (messageBean.msgId) {\n        case MessageId.MsgTypeLevelClickBlock:\n            this.onLevelClickBlockResponse(messageBean.data);\n            break;\n        case MessageId.MsgTypeLevelGameEnd:\n            this.onLevelGameEnd(messageBean.data);\n            break;\n    }\n}\n```\n\n## 注意事项\n\n1. **格子节点命名**: 格子节点需要按照 `Grid_x_y` 格式命名，或者确保位置计算正确\n2. **预制体配置**: 所有预制体都需要在编辑器中正确配置\n3. **棋盘节点配置**: 五个棋盘节点(qipan8x8Node, qipan8x9Node, qipan9x9Node, qipan9x10Node, qipan10x10Node)需要在编辑器中正确配置\n4. **消息监听**: 使用EventCenter监听WebSocket消息，需要在适当的地方注册和取消监听器\n5. **内存管理**: 游戏结束时记得清理预制体和重置状态\n6. **自动节点选择**: 控制器会根据棋盘类型自动选择对应的棋盘节点，无需手动传入\n\n## API 参考\n\n### 公共方法\n\n- `initBoard(boardType: string)`: 初始化指定类型的棋盘\n- `handleClickResponse(x, y, result)`: 处理点击响应\n- `handleChainReaction(revealedGrids)`: 处理连锁反应\n- `onExtendLevelInfo()`: 处理关卡信息\n- `onLevelGameEnd()`: 处理游戏结束\n- `createBiaojiPrefab(x, y)`: 创建标记预制体\n- `createBoomPrefab(x, y)`: 创建地雷预制体\n- `createNumberPrefab(x, y, number)`: 创建数字预制体\n- `showAllHiddenGrids()`: 显示所有隐藏格子\n- `clearAllPrefabs()`: 清除所有预制体\n- `resetBoard()`: 重置棋盘到初始状态\n\n### 棋盘配置\n\n```typescript\nconst BOARD_CONFIGS = {\n    \"8x8\": { width: 752, height: 752, rows: 8, cols: 8, gridWidth: 88, gridHeight: 88 },\n    \"8x9\": { width: 752, height: 845, rows: 8, cols: 9, gridWidth: 88, gridHeight: 88 },\n    \"9x9\": { width: 752, height: 747, rows: 9, cols: 9, gridWidth: 76, gridHeight: 76 },\n    \"9x10\": { width: 752, height: 830, rows: 9, cols: 10, gridWidth: 78, gridHeight: 78 },\n    \"10x10\": { width: 752, height: 745, rows: 10, cols: 10, gridWidth: 69, gridHeight: 69 }\n};\n```\n"}