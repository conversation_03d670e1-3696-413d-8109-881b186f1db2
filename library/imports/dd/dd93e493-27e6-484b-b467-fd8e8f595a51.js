"use strict";
cc._RF.push(module, 'dd93eSTJ+ZIS7Rn/Y6PWVpR', 'NickNameLabel');
// scripts/util/NickNameLabel.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property, requireComponent = _a.requireComponent;
var NickNameLabel = /** @class */ (function (_super) {
    __extends(NickNameLabel, _super);
    function NickNameLabel() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.MaxWidth = 0;
        _this._labelComp = null;
        return _this;
    }
    Object.defineProperty(NickNameLabel.prototype, "_label", {
        get: function () {
            if (this._labelComp == null) {
                this._labelComp = this.node.getComponent(cc.Label);
            }
            return this._labelComp;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(NickNameLabel.prototype, "string", {
        get: function () {
            return this._label.string;
        },
        set: function (value) {
            var overflow = this._label.overflow;
            value = this.processNickname(value, this.MaxWidth);
            this._label.string = value;
            this._label.overflow = overflow;
        },
        enumerable: false,
        configurable: true
    });
    NickNameLabel.prototype.getTextWidth = function (text) {
        this._label.string = text;
        // @ts-ignore
        this._label._forceUpdateRenderData();
        return this.node.width;
    };
    NickNameLabel.prototype.processNickname = function (nickname, max) {
        this._label.overflow = cc.Label.Overflow.NONE;
        var nickNameWidth = this.getTextWidth(nickname);
        if (nickNameWidth > max) {
            // 这里使用二分查找来找到最接近且不超出标签宽度的截断位置
            var left = 0;
            var right = nickname.length;
            var result = 0;
            while (left <= right) {
                var mid = Math.floor((left + right) / 2);
                var midString = nickname.substring(0, mid) + '...';
                var midWidth = this.getTextWidth(midString);
                if (midWidth <= max) {
                    result = mid;
                    left = mid + 1;
                }
                else {
                    right = mid - 1;
                }
            }
            return nickname.substring(0, result) + '...';
        }
        return nickname;
    };
    __decorate([
        property
    ], NickNameLabel.prototype, "MaxWidth", void 0);
    NickNameLabel = __decorate([
        ccclass,
        requireComponent(cc.Label)
    ], NickNameLabel);
    return NickNameLabel;
}(cc.Component));
exports.default = NickNameLabel;

cc._RF.pop();