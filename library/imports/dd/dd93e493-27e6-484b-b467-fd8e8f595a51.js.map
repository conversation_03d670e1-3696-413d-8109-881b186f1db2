{"version": 3, "sources": ["assets/scripts/util/NickNameLabel.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAM,IAAA,KAAwC,EAAE,CAAC,UAAU,EAApD,OAAO,aAAA,EAAE,QAAQ,cAAA,EAAE,gBAAgB,sBAAiB,CAAC;AAI5D;IAA2C,iCAAY;IAAvD;QAAA,qEAqEC;QAlEG,cAAQ,GAAW,CAAC,CAAC;QAEb,gBAAU,GAAa,IAAI,CAAC;;IAgExC,CAAC;IA9DG,sBAAY,iCAAM;aAAlB;YAEI,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,EAC3B;gBACI,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;aACtD;YACD,OAAO,IAAI,CAAC,UAAU,CAAC;QAC3B,CAAC;;;OAAA;IAED,sBAAW,iCAAM;aAQjB;YAEI,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;QAC9B,CAAC;aAXD,UAAkB,KAAa;YAE3B,IAAI,QAAQ,GAAsB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;YACvD,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;YACnD,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,KAAK,CAAC;YAC3B,IAAI,CAAC,MAAM,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACpC,CAAC;;;OAAA;IAOO,oCAAY,GAApB,UAAqB,IAAY;QAE7B,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC;QAC1B,aAAa;QACb,IAAI,CAAC,MAAM,CAAC,sBAAsB,EAAE,CAAC;QACrC,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;IAC3B,CAAC;IAEO,uCAAe,GAAvB,UAAwB,QAAgB,EAAE,GAAW;QAEjD,IAAI,CAAC,MAAM,CAAC,QAAQ,GAAG,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC;QAC9C,IAAI,aAAa,GAAW,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;QACxD,IAAI,aAAa,GAAG,GAAG,EACvB;YACI,8BAA8B;YAC9B,IAAI,IAAI,GAAW,CAAC,CAAC;YACrB,IAAI,KAAK,GAAW,QAAQ,CAAC,MAAM,CAAC;YACpC,IAAI,MAAM,GAAW,CAAC,CAAC;YACvB,OAAO,IAAI,IAAI,KAAK,EACpB;gBACI,IAAI,GAAG,GAAW,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;gBACjD,IAAI,SAAS,GAAW,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC;gBAC3D,IAAI,QAAQ,GAAW,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;gBAEpD,IAAI,QAAQ,IAAI,GAAG,EACnB;oBACI,MAAM,GAAG,GAAG,CAAC;oBACb,IAAI,GAAG,GAAG,GAAG,CAAC,CAAC;iBAClB;qBAED;oBACI,KAAK,GAAG,GAAG,GAAG,CAAC,CAAC;iBACnB;aACJ;YAED,OAAO,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,MAAM,CAAC,GAAG,KAAK,CAAC;SAChD;QAED,OAAO,QAAQ,CAAC;IACpB,CAAC;IAjED;QADC,QAAQ;mDACY;IAHJ,aAAa;QAFjC,OAAO;QACP,gBAAgB,CAAC,EAAE,CAAC,KAAK,CAAC;OACN,aAAa,CAqEjC;IAAD,oBAAC;CArED,AAqEC,CArE0C,EAAE,CAAC,SAAS,GAqEtD;kBArEoB,aAAa", "file": "", "sourceRoot": "/", "sourcesContent": ["const {ccclass, property, requireComponent} = cc._decorator;\n\n@ccclass\n@requireComponent(cc.Label)\nexport default class NickNameLabel extends cc.Component \n{\n    @property\n    MaxWidth: number = 0;\n\n    private _labelComp: cc.Label = null;\n\n    private get _label(): cc.Label\n    {\n        if (this._labelComp == null)\n        {\n            this._labelComp = this.node.getComponent(cc.Label);\n        }\n        return this._labelComp;\n    }\n\n    public set string(value: string) \n    {\n        let overflow: cc.Label.Overflow = this._label.overflow;\n        value = this.processNickname(value, this.MaxWidth);\n        this._label.string = value;\n        this._label.overflow = overflow;\n    }\n\n    public get string(): string \n    {\n        return this._label.string;\n    }\n\n    private getTextWidth(text: string): number \n    {\n        this._label.string = text;\n        // @ts-ignore\n        this._label._forceUpdateRenderData();\n        return this.node.width;\n    }\n\n    private processNickname(nickname: string, max: number): string \n    {\n        this._label.overflow = cc.Label.Overflow.NONE;\n        let nickNameWidth: number = this.getTextWidth(nickname);\n        if (nickNameWidth > max) \n        {\n            // 这里使用二分查找来找到最接近且不超出标签宽度的截断位置\n            let left: number = 0;\n            let right: number = nickname.length;\n            let result: number = 0;\n            while (left <= right) \n            {\n                let mid: number = Math.floor((left + right) / 2);\n                let midString: string = nickname.substring(0, mid) + '...';\n                let midWidth: number = this.getTextWidth(midString);\n\n                if (midWidth <= max) \n                {\n                    result = mid;\n                    left = mid + 1;\n                } \n                else \n                {\n                    right = mid - 1;\n                }\n            }\n\n            return nickname.substring(0, result) + '...';\n        }\n\n        return nickname;\n    }\n}"]}