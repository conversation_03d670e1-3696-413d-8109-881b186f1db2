{"version": 3, "sources": ["assets/scripts/hall/HallJoinRoomController.ts"], "names": [], "mappings": ";;;;;AAAA,oBAAoB;AACpB,4EAA4E;AAC5E,mBAAmB;AACnB,sFAAsF;AACtF,8BAA8B;AAC9B,sFAAsF;;;;;;;;;;;;;;;;;;;;;AAGtF,uCAAsC;AAGhC,IAAA,KAAwB,EAAE,CAAC,UAAU,EAAnC,OAAO,aAAA,EAAE,QAAQ,cAAkB,CAAC;AAG5C;IAAoD,0CAAY;IAAhE;QAAA,qEA0IC;QAvIG,gBAAU,GAAY,IAAI,CAAC;QAE3B,aAAO,GAAe,IAAI,CAAA,CAAG,OAAO;QAEpC,mBAAa,GAAY,IAAI,CAAC,CAAC,eAAe;QAE9C,iBAAW,GAAY,IAAI,CAAC,CAAC,cAAc;QAI3C,aAAO,GAAW,EAAE,CAAA;QACpB,yBAAmB,GAAW,EAAE,CAAA;QAEhC,iBAAW,GAAa,IAAI,CAAA;;QAyH5B,iBAAiB;IACrB,CAAC;IAtHG,eAAe;IAEf,sCAAK,GAAL;QAAA,iBAWC;QATG,aAAK,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,EAAE;YACjC,QAAQ;YACR,IAAI,KAAI,CAAC,QAAQ,EAAE;gBACf,KAAI,CAAC,QAAQ,CAAC,KAAI,CAAC,OAAO,CAAC,CAAC;aAC/B;QACL,CAAC,CAAC,CAAA;QAEF,aAAK,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,EAAE,cAAQ,CAAC,CAAC,CAAA;IAEnD,CAAC;IAES,yCAAQ,GAAlB;QACI,IAAI,CAAC,cAAc,EAAE,CAAA;QACrB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,cAAc,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;QAC/D,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,mBAAmB,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;QACrE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,mBAAmB,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;QACrE,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;IAE5B,CAAC;IACS,0CAAS,GAAnB;QACI,IAAI,CAAC,cAAc,EAAE,CAAA;QACrB,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,EAAE,CAAA;QACxB,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,KAAK,CAAA;QAC9B,oBAAoB;QACpB,IAAI,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,EAAE;YACrC,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC,IAAI,CAAC,OAAO,GAAG,GAAG,CAAC;SAC1D;QACD,oBAAoB;QACpB,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1B,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC,mBAAmB,CAAC;SACvD;IACL,CAAC;IAED,8CAAa,GAAb;QACI,4BAA4B;QAC5B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;QACnC,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,KAAK,CAAA;QAC9B,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;YACzB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;SAC1B;aAAM;YACH,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;SAC3B;IACL,CAAC;IAED,+CAAc,GAAd;QACI,sBAAsB;QACtB,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;QAErC,iBAAiB;QACjB,IAAM,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC;QAC7D,IAAI,gBAAgB,EAAE;YAClB,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;YAC9C,gBAAgB,CAAC,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC;SACrC;QAED,6BAA6B;QAC7B,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,IAAI,CAAC,mBAAmB,KAAK,EAAE,EAAE;YAC7D,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC;SACvD;QACD,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,EAAE,CAAC;QAC9B,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;IAC5C,CAAC;IAED,+CAAc,GAAd;QACI,8BAA8B;QAC9B,OAAO,CAAC,GAAG,CAAC,qCAAqC,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAE/E,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;YAClC,aAAa;YACb,IAAM,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC;YAC7D,IAAI,gBAAgB,EAAE;gBAClB,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;gBAC/C,gBAAgB,CAAC,IAAI,CAAC,OAAO,GAAG,GAAG,CAAC;aACvC;YAED,yBAAyB;YACzB,IAAI,IAAI,CAAC,mBAAmB,EAAE;gBAC1B,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC,mBAAmB,CAAC;gBACpD,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;aAC5C;SACJ;IACL,CAAC;IAED,+CAAc,GAAd;QACI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;QAChE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;QACtE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;IAC1E,CAAC;IAEO,4CAAW,GAAnB,UAAoB,OAAgB;QAChC,IAAI,OAAO,EAAE;YACT,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,IAAI,CAAA;YAC9B,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,KAAK,CAAA;SACpC;aAAM;YACH,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,KAAK,CAAA;YAC/B,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,IAAI,CAAA;SAEnC;IAEL,CAAC;IAED,QAAQ;IACR,0CAAS,GAAT;QACI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,IAAI,CAAA;IACjC,CAAC;IAED,mBAAmB;IACnB,+CAAc,GAAd,UAAe,QAAkB;QAE7B,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,CAAC,WAAW;IACzC,CAAC;IAnID;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;8DACS;IAE3B;QADC,QAAQ,CAAC,EAAE,CAAC,OAAO,CAAC;2DACK;IAE1B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;iEACY;IAE9B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;+DACU;IATX,sBAAsB;QAD1C,OAAO;OACa,sBAAsB,CA0I1C;IAAD,6BAAC;CA1ID,AA0IC,CA1ImD,EAAE,CAAC,SAAS,GA0I/D;kBA1IoB,sBAAsB", "file": "", "sourceRoot": "/", "sourcesContent": ["// Learn TypeScript:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html\n// Learn Attribute:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html\n// Learn life-cycle callbacks:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html\n\nimport { Config } from \"../util/Config\";\nimport { Tools } from \"../util/Tools\";\n\n\nconst { ccclass, property } = cc._decorator;\n\n@ccclass\nexport default class HallJoinRoomController extends cc.Component {\n\n    @property(cc.Node)\n    errorLabel: cc.Node = null;\n    @property(cc.EditBox)\n    editBox: cc.EditBox = null   //文本输入框\n    @property(cc.Node)\n    buttonNoClick: cc.Node = null; //join 按钮（不可点击）\n    @property(cc.Node)\n    buttonReady: cc.Node = null; //join 按钮（可点击）\n\n\n\n    newText: string = ''\n    originalPlaceholder: string = ''\n\n    skillsClick: Function = null\n    callback: Function;\n\n\n    // onLoad () {}\n\n    start() {\n\n        Tools.yellowButton(this.buttonReady, () => {\n            //点击加入房间\n            if (this.callback) {\n                this.callback(this.newText);\n            }\n        })\n\n        Tools.grayButton(this.buttonNoClick, () => { })\n\n    }\n\n    protected onEnable(): void {\n        this.destroyEditBox()\n        this.editBox.node.on('text-changed', this.onTextChanged, this);\n        this.editBox.node.on('editing-did-began', this.onEditingBegan, this);\n        this.editBox.node.on('editing-did-ended', this.onEditingEnded, this);\n        this.buttonStyle(false);\n\n    }\n    protected onDisable(): void {\n        this.destroyEditBox()\n        this.editBox.string = ''\n        this.errorLabel.active = false\n        // 恢复 placeholder 显示\n        if (this.editBox['_N$placeholderLabel']) {\n            this.editBox['_N$placeholderLabel'].node.opacity = 255;\n        }\n        // 恢复 placeholder 文本\n        if (this.originalPlaceholder) {\n            this.editBox.placeholder = this.originalPlaceholder;\n        }\n    }\n\n    onTextChanged() {\n        // 当 EditBox 的文本发生变化时执行这里的代码\n        this.newText = this.editBox.string;\n        this.errorLabel.active = false\n        if (this.newText.length > 0) {\n            this.buttonStyle(true);\n        } else {\n            this.buttonStyle(false);\n        }\n    }\n\n    onEditingBegan() {\n        // 开始编辑时隐藏 placeholder\n        console.log(\"EditBox editing began\");\n\n        // 方法1: 通过设置透明度隐藏\n        const placeholderLabel = this.editBox['_N$placeholderLabel'];\n        if (placeholderLabel) {\n            console.log(\"Hiding placeholder via opacity\");\n            placeholderLabel.node.opacity = 0;\n        }\n\n        // 方法2: 通过设置 placeholder 文本为空\n        if (this.editBox.placeholder && this.originalPlaceholder === '') {\n            this.originalPlaceholder = this.editBox.placeholder;\n        }\n        this.editBox.placeholder = '';\n        console.log(\"Placeholder text cleared\");\n    }\n\n    onEditingEnded() {\n        // 结束编辑时，如果没有文本则显示 placeholder\n        console.log(\"EditBox editing ended, text length:\", this.editBox.string.length);\n\n        if (this.editBox.string.length === 0) {\n            // 方法1: 恢复透明度\n            const placeholderLabel = this.editBox['_N$placeholderLabel'];\n            if (placeholderLabel) {\n                console.log(\"Showing placeholder via opacity\");\n                placeholderLabel.node.opacity = 255;\n            }\n\n            // 方法2: 恢复 placeholder 文本\n            if (this.originalPlaceholder) {\n                this.editBox.placeholder = this.originalPlaceholder;\n                console.log(\"Placeholder text restored\");\n            }\n        }\n    }\n\n    destroyEditBox() {\n        this.editBox.node.off('text-changed', this.onTextChanged, this);\n        this.editBox.node.off('editing-did-began', this.onEditingBegan, this);\n        this.editBox.node.off('editing-did-ended', this.onEditingEnded, this);\n    }\n\n    private buttonStyle(isEnter: boolean) {\n        if (isEnter) {\n            this.buttonReady.active = true\n            this.buttonNoClick.active = false\n        } else {\n            this.buttonReady.active = false\n            this.buttonNoClick.active = true\n\n        }\n\n    }\n\n    //加入房间失败\n    joinError() {\n        this.errorLabel.active = true\n    }\n\n    //设置点击道具 item 按钮的回调\n    setButtonClick(callback: Function) {\n\n        this.callback = callback; //点击加入房间的按钮\n    }\n\n\n    // update (dt) {}\n}\n"]}