"use strict";
cc._RF.push(module, 'a1a7eDE6kBEA66nRqV1LA92', '<PERSON><PERSON><PERSON>n<PERSON>oomController');
// scripts/hall/HallJoinRoomController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Tools_1 = require("../util/Tools");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var HallJoinRoomController = /** @class */ (function (_super) {
    __extends(HallJoinRoomController, _super);
    function HallJoinRoomController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.errorLabel = null;
        _this.editBox = null; //文本输入框
        _this.buttonNoClick = null; //join 按钮（不可点击）
        _this.buttonReady = null; //join 按钮（可点击）
        _this.newText = '';
        _this.originalPlaceholder = '';
        _this.skillsClick = null;
        return _this;
        // update (dt) {}
    }
    // onLoad () {}
    HallJoinRoomController.prototype.start = function () {
        var _this = this;
        Tools_1.Tools.yellowButton(this.buttonReady, function () {
            //点击加入房间
            if (_this.callback) {
                _this.callback(_this.newText);
            }
        });
        Tools_1.Tools.grayButton(this.buttonNoClick, function () { });
    };
    HallJoinRoomController.prototype.onEnable = function () {
        this.destroyEditBox();
        this.editBox.node.on('text-changed', this.onTextChanged, this);
        this.editBox.node.on('editing-did-began', this.onEditingBegan, this);
        this.editBox.node.on('editing-did-ended', this.onEditingEnded, this);
        this.buttonStyle(false);
    };
    HallJoinRoomController.prototype.onDisable = function () {
        this.destroyEditBox();
        this.editBox.string = '';
        this.errorLabel.active = false;
        // 恢复 placeholder 显示
        if (this.editBox['_N$placeholderLabel']) {
            this.editBox['_N$placeholderLabel'].node.opacity = 255;
        }
        // 恢复 placeholder 文本
        if (this.originalPlaceholder) {
            this.editBox.placeholder = this.originalPlaceholder;
        }
    };
    HallJoinRoomController.prototype.onTextChanged = function () {
        // 当 EditBox 的文本发生变化时执行这里的代码
        this.newText = this.editBox.string;
        this.errorLabel.active = false;
        if (this.newText.length > 0) {
            this.buttonStyle(true);
        }
        else {
            this.buttonStyle(false);
        }
    };
    HallJoinRoomController.prototype.onEditingBegan = function () {
        // 开始编辑时隐藏 placeholder
        console.log("EditBox editing began");
        // 方法1: 通过设置透明度隐藏
        var placeholderLabel = this.editBox['_N$placeholderLabel'];
        if (placeholderLabel) {
            console.log("Hiding placeholder via opacity");
            placeholderLabel.node.opacity = 0;
        }
        // 方法2: 通过设置 placeholder 文本为空
        if (this.editBox.placeholder && this.originalPlaceholder === '') {
            this.originalPlaceholder = this.editBox.placeholder;
        }
        this.editBox.placeholder = '';
        console.log("Placeholder text cleared");
    };
    HallJoinRoomController.prototype.onEditingEnded = function () {
        // 结束编辑时，如果没有文本则显示 placeholder
        console.log("EditBox editing ended, text length:", this.editBox.string.length);
        if (this.editBox.string.length === 0) {
            // 方法1: 恢复透明度
            var placeholderLabel = this.editBox['_N$placeholderLabel'];
            if (placeholderLabel) {
                console.log("Showing placeholder via opacity");
                placeholderLabel.node.opacity = 255;
            }
            // 方法2: 恢复 placeholder 文本
            if (this.originalPlaceholder) {
                this.editBox.placeholder = this.originalPlaceholder;
                console.log("Placeholder text restored");
            }
        }
    };
    HallJoinRoomController.prototype.destroyEditBox = function () {
        this.editBox.node.off('text-changed', this.onTextChanged, this);
        this.editBox.node.off('editing-did-began', this.onEditingBegan, this);
        this.editBox.node.off('editing-did-ended', this.onEditingEnded, this);
    };
    HallJoinRoomController.prototype.buttonStyle = function (isEnter) {
        if (isEnter) {
            this.buttonReady.active = true;
            this.buttonNoClick.active = false;
        }
        else {
            this.buttonReady.active = false;
            this.buttonNoClick.active = true;
        }
    };
    //加入房间失败
    HallJoinRoomController.prototype.joinError = function () {
        this.errorLabel.active = true;
    };
    //设置点击道具 item 按钮的回调
    HallJoinRoomController.prototype.setButtonClick = function (callback) {
        this.callback = callback; //点击加入房间的按钮
    };
    __decorate([
        property(cc.Node)
    ], HallJoinRoomController.prototype, "errorLabel", void 0);
    __decorate([
        property(cc.EditBox)
    ], HallJoinRoomController.prototype, "editBox", void 0);
    __decorate([
        property(cc.Node)
    ], HallJoinRoomController.prototype, "buttonNoClick", void 0);
    __decorate([
        property(cc.Node)
    ], HallJoinRoomController.prototype, "buttonReady", void 0);
    HallJoinRoomController = __decorate([
        ccclass
    ], HallJoinRoomController);
    return HallJoinRoomController;
}(cc.Component));
exports.default = HallJoinRoomController;

cc._RF.pop();