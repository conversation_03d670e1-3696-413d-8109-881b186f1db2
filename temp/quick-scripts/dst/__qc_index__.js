
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/__qc_index__.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}
require('./assets/meshTools/BaseSDK');
require('./assets/meshTools/MeshTools');
require('./assets/meshTools/Singleton');
require('./assets/meshTools/tools/MeshSdk');
require('./assets/meshTools/tools/MeshSdkApi');
require('./assets/meshTools/tools/Publish');
require('./assets/resources/i18n/en');
require('./assets/resources/i18n/zh_CN');
require('./assets/resources/i18n/zh_HK');
require('./assets/scripts/GlobalManagerController');
require('./assets/scripts/TipsDialogController');
require('./assets/scripts/ToastController');
require('./assets/scripts/bean/EnumBean');
require('./assets/scripts/bean/GameBean');
require('./assets/scripts/bean/GlobalBean');
require('./assets/scripts/bean/LanguageType');
require('./assets/scripts/common/EventCenter');
require('./assets/scripts/common/GameData');
require('./assets/scripts/common/GameMgr');
require('./assets/scripts/common/GameTools');
require('./assets/scripts/common/MineConsole');
require('./assets/scripts/game/BtnController');
require('./assets/scripts/game/Chess/ChessBoardController');
require('./assets/scripts/game/Chess/GridController');
require('./assets/scripts/game/Chess/HexChessBoardController');
require('./assets/scripts/game/Chess/SingleChessBoardController');
require('./assets/scripts/game/CongratsDialogController');
require('./assets/scripts/game/GamePageController');
require('./assets/scripts/game/GameScoreController');
require('./assets/scripts/hall/HallAutoController');
require('./assets/scripts/hall/HallCenterLayController');
require('./assets/scripts/hall/HallCreateRoomController');
require('./assets/scripts/hall/HallJoinRoomController');
require('./assets/scripts/hall/HallPageController');
require('./assets/scripts/hall/HallParentController');
require('./assets/scripts/hall/InfoDialogController');
require('./assets/scripts/hall/KickOutDialogController');
require('./assets/scripts/hall/LeaveDialogController');
require('./assets/scripts/hall/Level/LevelItemController');
require('./assets/scripts/hall/Level/LevelSelectController');
require('./assets/scripts/hall/Level/LevelSelectExample');
require('./assets/scripts/hall/Level/LevelSelectPageController');
require('./assets/scripts/hall/Level/ScrollViewHelper');
require('./assets/scripts/hall/LevelSelectDemo');
require('./assets/scripts/hall/MatchParentController');
require('./assets/scripts/hall/PlayerLayoutController');
require('./assets/scripts/hall/SettingDialogController');
require('./assets/scripts/hall/TopUpDialogController');
require('./assets/scripts/level/LevelPageController');
require('./assets/scripts/net/ErrorCode');
require('./assets/scripts/net/GameServerUrl');
require('./assets/scripts/net/HttpManager');
require('./assets/scripts/net/HttpUtils');
require('./assets/scripts/net/IHttpMsgBody');
require('./assets/scripts/net/MessageBaseBean');
require('./assets/scripts/net/MessageId');
require('./assets/scripts/net/WebSocketManager');
require('./assets/scripts/net/WebSocketTool');
require('./assets/scripts/pfb/CongratsItemController');
require('./assets/scripts/pfb/InfoItemController');
require('./assets/scripts/pfb/InfoItemOneController');
require('./assets/scripts/pfb/MatchItemController');
require('./assets/scripts/pfb/PlayerGameController ');
require('./assets/scripts/pfb/PlayerScoreController');
require('./assets/scripts/pfb/SeatItemController');
require('./assets/scripts/start_up/StartUpCenterController');
require('./assets/scripts/start_up/StartUpPageController');
require('./assets/scripts/test/NoticeRoundStartTest');
require('./assets/scripts/util/AudioManager');
require('./assets/scripts/util/AudioMgr');
require('./assets/scripts/util/BlockingQueue');
require('./assets/scripts/util/Config');
require('./assets/scripts/util/Dictionary');
require('./assets/scripts/util/LocalStorageManager');
require('./assets/scripts/util/NickNameLabel');
require('./assets/scripts/util/Tools');
require('./joui18n-script/LocalizedLabel');
require('./joui18n-script/LocalizedSprite');

                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();