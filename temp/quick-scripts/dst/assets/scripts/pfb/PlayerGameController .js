
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/pfb/PlayerGameController .js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'd06f40RGNtELrdV8XT/vrpM', 'PlayerGameController ');
// scripts/pfb/PlayerGameController .ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Tools_1 = require("../util/Tools");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var PlayerGameController = /** @class */ (function (_super) {
    __extends(PlayerGameController, _super);
    function PlayerGameController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.avatar = null; //头像
        _this.flagNode = null; //旗子节点
        _this.addScoreNode = null; //加分背景节点 addscore
        _this.subScoreNode = null; //减分背景节点 deductscore
        return _this;
        // update (dt) {}
    }
    PlayerGameController.prototype.start = function () {
    };
    PlayerGameController.prototype.setData = function (user) {
        var _this = this;
        this.scheduleOnce(function () {
            if (user == null) {
                _this.avatar.active = false;
            }
            else {
                Tools_1.Tools.setNodeSpriteFrameUrl(_this.avatar, user.avatar); //添加头像
                _this.avatar.active = true;
            }
        }, 0.1);
    };
    /**
     * 显示加分效果，带动画
     * @param addValue 加分数值
     */
    PlayerGameController.prototype.showAddScore = function (addValue) {
        var _this = this;
        if (this.addScoreNode) {
            // 获取change_score文本节点并设置加分文本
            var changeScoreLabel = this.addScoreNode.getChildByName("change_score");
            if (changeScoreLabel) {
                var labelComponent = changeScoreLabel.getComponent(cc.Label);
                if (labelComponent) {
                    labelComponent.string = "+" + addValue.toString();
                }
            }
            else {
                console.warn("PlayerGame addScoreNode中找不到change_score子节点");
            }
            // 设置最高层级，确保不被头像遮挡
            this.addScoreNode.zIndex = cc.macro.MAX_ZINDEX - 1;
            // 停止之前的动画
            cc.Tween.stopAllByTarget(this.addScoreNode);
            // 重置节点状态
            this.addScoreNode.active = true;
            this.addScoreNode.opacity = 0;
            this.addScoreNode.scale = 0.8;
            // 保存原始位置
            var originalY_1 = this.addScoreNode.y;
            // 使用新的Tween API
            cc.tween(this.addScoreNode)
                .parallel(cc.tween().to(0.15, { opacity: 255 }), cc.tween().to(0.15, { scale: 1.1 }), cc.tween().by(0.15, { y: 15 }))
                .to(0.1, { scale: 1.0 })
                .delay(0.8)
                .parallel(cc.tween().to(0.25, { opacity: 0 }), cc.tween().to(0.25, { scale: 0.9 }), cc.tween().by(0.25, { y: 8 }))
                .call(function () {
                _this.addScoreNode.active = false;
                _this.addScoreNode.opacity = 255;
                _this.addScoreNode.scale = 1.0;
                _this.addScoreNode.y = originalY_1;
            })
                .start();
        }
        else {
            console.warn("PlayerGame addScoreNode未设置");
        }
    };
    /**
     * 显示减分效果
     * @param subValue 减分数值
     */
    PlayerGameController.prototype.showSubScore = function (subValue) {
        var _this = this;
        if (this.subScoreNode) {
            // 获取change_score文本节点并设置减分文本
            var changeScoreLabel = this.subScoreNode.getChildByName("change_score");
            if (changeScoreLabel) {
                var labelComponent = changeScoreLabel.getComponent(cc.Label);
                if (labelComponent) {
                    labelComponent.string = "-" + subValue.toString();
                }
            }
            else {
                console.warn("PlayerGame subScoreNode中找不到change_score子节点");
            }
            // 设置最高层级，确保不被头像遮挡
            this.subScoreNode.zIndex = cc.macro.MAX_ZINDEX - 1;
            this.subScoreNode.active = true;
            // 1秒后隐藏
            this.scheduleOnce(function () {
                if (_this.subScoreNode) {
                    _this.subScoreNode.active = false;
                }
            }, 1.0);
        }
        else {
            console.warn("PlayerGame subScoreNode未设置");
        }
    };
    // 隐藏加减分节点
    PlayerGameController.prototype.hideScoreEffects = function () {
        if (this.addScoreNode) {
            this.addScoreNode.active = false;
        }
        if (this.subScoreNode) {
            this.subScoreNode.active = false;
        }
    };
    __decorate([
        property(cc.Node)
    ], PlayerGameController.prototype, "avatar", void 0);
    __decorate([
        property(cc.Node)
    ], PlayerGameController.prototype, "flagNode", void 0);
    __decorate([
        property(cc.Node)
    ], PlayerGameController.prototype, "addScoreNode", void 0);
    __decorate([
        property(cc.Node)
    ], PlayerGameController.prototype, "subScoreNode", void 0);
    PlayerGameController = __decorate([
        ccclass
    ], PlayerGameController);
    return PlayerGameController;
}(cc.Component));
exports.default = PlayerGameController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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