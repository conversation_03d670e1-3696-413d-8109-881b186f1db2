
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/game/GamePageController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'ae7d7j8qCJHEr/tVZKmu8hm', 'GamePageController');
// scripts/game/GamePageController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var GlobalBean_1 = require("../bean/GlobalBean");
var LeaveDialogController_1 = require("../hall/LeaveDialogController");
var AudioManager_1 = require("../util/AudioManager");
var Config_1 = require("../util/Config");
var Tools_1 = require("../util/Tools");
var CongratsDialogController_1 = require("./CongratsDialogController");
var GameScoreController_1 = require("./GameScoreController");
var ChessBoardController_1 = require("./Chess/ChessBoardController");
var HexChessBoardController_1 = require("./Chess/HexChessBoardController");
var PlayerGameController_1 = require("../pfb/PlayerGameController ");
var WebSocketManager_1 = require("../net/WebSocketManager");
var MessageId_1 = require("../net/MessageId");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var GamePageController = /** @class */ (function (_super) {
    __extends(GamePageController, _super);
    function GamePageController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.boardBtnBack = null; //返回按钮
        _this.timeLabel = null; // 计时器显示标签
        _this.mineCountLabel = null; // 炸弹数量显示标签
        _this.squareMapNode = null; // 方形地图节点 (mapType = 0)
        _this.hexMapNode = null; // 六边形地图节点 (mapType = 1)
        _this.leaveDialogController = null; // 退出游戏弹窗
        _this.congratsDialogController = null; //结算弹窗
        _this.gameScoreController = null; //分数控制器
        _this.chessBoardController = null; //方形棋盘控制器
        _this.hexChessBoardController = null; //六边形棋盘控制器
        _this.gameStartNode = null; // 游戏开始节点
        _this.roundStartNode = null; // 回合开始节点
        _this.isLeaveGameDialogShow = false; //是否显示退出游戏的弹窗
        _this.isCongratsDialog = false; //是否显示结算的弹窗
        // 计时器相关属性
        _this.countdownInterval = null; // 倒计时定时器ID
        _this.currentCountdown = 0; // 当前倒计时秒数
        _this.currentRoundNumber = 0; // 当前回合编号
        // 游戏状态管理
        _this.canOperate = false; // 是否可以操作（在NoticeRoundStart和NoticeActionDisplay之间）
        _this.gameStatus = 0; // 游戏状态
        _this.hasOperatedThisRound = false; // 本回合是否已经操作过
        // 游戏数据
        _this.currentMapType = 0; // 当前地图类型 0-方形地图，1-六边形地图
        _this.currentMineCount = 0; // 当前炸弹数量
        // 当前NoticeActionDisplay数据，用于倒计时显示逻辑
        _this.currentNoticeActionData = null;
        return _this;
        // update (dt) {}
    }
    GamePageController.prototype.onLoad = function () {
        var _this = this;
        console.log("=== GamePageController onLoad 开始 ===");
        // 如果timeLabel没有在编辑器中设置，尝试通过路径查找
        if (!this.timeLabel) {
            // 根据场景结构查找time_label节点
            var timeBgNode = cc.find('Canvas/time_bg');
            if (timeBgNode) {
                var timeLabelNode = timeBgNode.getChildByName('time_label');
                if (timeLabelNode) {
                    this.timeLabel = timeLabelNode.getComponent(cc.Label);
                }
            }
        }
        // 如果mineCountLabel没有在编辑器中设置，尝试通过路径查找
        if (!this.mineCountLabel) {
            // 根据场景结构查找mine_count_label节点
            var mineCountBgNode = cc.find('Canvas/mine_count_bg');
            if (mineCountBgNode) {
                var mineCountLabelNode = mineCountBgNode.getChildByName('mine_count_label');
                if (mineCountLabelNode) {
                    this.mineCountLabel = mineCountLabelNode.getComponent(cc.Label);
                }
            }
        }
        // 将测试方法暴露到全局，方便调试
        window.testGameReset = function () {
            _this.testReset();
        };
        // 暴露 GamePageController 实例到全局
        window.gamePageController = this;
        // 暴露测试方法到全局，方便在控制台直接调用
        window.testRoundAnimation = function () {
            console.log("全局测试方法被调用");
            _this.testRoundStartAnimation();
        };
        // 初始化游戏开始和回合开始节点
        this.initializeAnimationNodes();
        console.log("=== GamePageController onLoad 完成 ===");
    };
    GamePageController.prototype.start = function () {
        var _this = this;
        Tools_1.Tools.imageButtonClick(this.boardBtnBack, Config_1.Config.buttonRes + 'side_btn_back_normal', Config_1.Config.buttonRes + 'side_btn_back_pressed', function () {
            _this.isLeaveGameDialogShow = true;
            _this.leaveDialogController.show(1, function () {
                _this.isLeaveGameDialogShow = false;
            });
        });
        // 监听棋盘点击事件
        if (this.chessBoardController) {
            this.chessBoardController.node.on('chess-board-click', this.onChessBoardClick, this);
        }
        // 监听六边形棋盘点击事件
        if (this.hexChessBoardController) {
            this.hexChessBoardController.node.on('hex-chess-board-click', this.onHexChessBoardClick, this);
        }
    };
    /**
     * 处理棋盘点击事件
     * @param event 事件数据 {x: number, y: number, action: number}
     */
    GamePageController.prototype.onChessBoardClick = function (event) {
        var _a = event.detail || event, x = _a.x, y = _a.y, action = _a.action;
        // 检查是否可以操作（在操作时间内）
        if (!this.isCanOperate()) {
            return;
        }
        // 检查本回合是否已经操作过
        if (this.hasOperatedThisRound) {
            return;
        }
        // 发送点击操作
        this.sendClickBlock(x, y, action);
        // 操作有效，通知棋盘生成预制体
        if (this.chessBoardController) {
            if (action === 1) {
                // 挖掘操作，生成不带旗子的预制体
                this.chessBoardController.placePlayerOnGrid(x, y, false);
            }
            else if (action === 2) {
                // 标记操作，生成带旗子的预制体
                this.chessBoardController.placePlayerOnGrid(x, y, true);
            }
        }
        // 标记本回合已经操作过，禁止后续交互
        this.hasOperatedThisRound = true;
    };
    /**
     * 处理六边形棋盘点击事件
     * @param event 事件数据 {q: number, r: number, action: number}
     */
    GamePageController.prototype.onHexChessBoardClick = function (event) {
        var _a = event.detail || event, q = _a.q, r = _a.r, action = _a.action;
        // 检查是否可以操作（在操作时间内）
        if (!this.isCanOperate()) {
            return;
        }
        // 检查本回合是否已经操作过
        if (this.hasOperatedThisRound) {
            return;
        }
        // 发送六边形点击操作（需要将六边形坐标转换为服务器期望的格式）
        this.sendHexClickBlock(q, r, action);
        // 操作有效，通知六边形棋盘生成预制体
        if (this.hexChessBoardController) {
            if (action === 1) {
                // 挖掘操作，生成不带旗子的预制体
                this.hexChessBoardController.placePlayerOnHexGrid(q, r, false);
            }
            else if (action === 2) {
                // 标记操作，生成带旗子的预制体
                this.hexChessBoardController.placePlayerOnHexGrid(q, r, true);
            }
        }
        // 标记本回合已经操作过，禁止后续交互
        this.hasOperatedThisRound = true;
    };
    //结算
    GamePageController.prototype.setCongratsDialog = function (noticeSettlement) {
        var _this = this;
        this.setCongrats(noticeSettlement);
        //退出弹窗正在显示的话  就先关闭
        if (this.isLeaveGameDialogShow) {
            this.leaveDialogController.hide();
        }
        this.isCongratsDialog = true;
        //弹出结算弹窗
        this.congratsDialogController.show(noticeSettlement, function () {
            _this.isCongratsDialog = false;
        });
    };
    GamePageController.prototype.onDisable = function () {
        //退出弹窗正在显示的话  就先关闭
        if (this.isLeaveGameDialogShow) {
            this.leaveDialogController.hide();
        }
        //结算弹窗正在显示的话就先关闭掉
        if (this.isCongratsDialog) {
            this.congratsDialogController.hide();
        }
        // 清理计时器
        this.clearCountdownTimer();
    };
    //结算
    GamePageController.prototype.setCongrats = function (noticeSettlement) {
        // 获取用户列表，优先使用 finalRanking，其次使用 users
        var userList = noticeSettlement.finalRanking || noticeSettlement.users;
        // 检查用户列表是否存在
        if (!noticeSettlement || !userList || !Array.isArray(userList)) {
            console.warn('NoticeSettlement 用户数据无效:', noticeSettlement);
            AudioManager_1.AudioManager.winAudio(); // 默认播放胜利音效
            return;
        }
        var currentUserId = GlobalBean_1.GlobalBean.GetInstance().loginData.userInfo.userId;
        var index = userList.findIndex(function (item) { return item.userId === currentUserId; }); //搜索
        if (index >= 0) { //自己参与的话 才会显示正常的胜利和失败的音效，自己不参与的话 就全部显示胜利的音效
            if (userList[index].rank === 1) { //判断自己是不是第一名
                AudioManager_1.AudioManager.winAudio();
            }
            else {
                AudioManager_1.AudioManager.loseAudio();
            }
        }
        else {
            AudioManager_1.AudioManager.winAudio();
        }
    };
    // 处理游戏开始通知，获取炸弹数量和地图类型
    GamePageController.prototype.onGameStart = function (data) {
        // 保存地图类型
        this.currentMapType = data.mapType || 0;
        // 根据地图类型重置对应的棋盘控制器
        if (this.currentMapType === 0) {
            // 方形地图
            if (this.chessBoardController) {
                this.chessBoardController.resetGameScene();
            }
            else {
                console.error("❌ chessBoardController 不存在！");
            }
        }
        else if (this.currentMapType === 1) {
            // 六边形地图
            if (this.hexChessBoardController) {
                this.hexChessBoardController.resetGameScene();
                // 忽略服务器的 validHexCoords，使用前端节点坐标
                this.hexChessBoardController.setValidHexCoords([]); // 传入空数组，会被忽略
            }
            else {
                console.error("❌ hexChessBoardController 不存在！");
            }
        }
        // 重置游戏状态
        this.canOperate = false;
        this.hasOperatedThisRound = false;
        this.currentRoundNumber = 0;
        this.currentCountdown = 0;
        this.gameStatus = 0;
        // 根据地图类型获取炸弹数量
        if (data.mapType === 0 && data.mapConfig) {
            // 方形地图
            this.currentMineCount = data.mapConfig.mineCount || 13;
        }
        else if (data.mapType === 1) {
            // 六边形地图，根据前端节点数量计算炸弹数量
            if (this.hexChessBoardController) {
                this.currentMineCount = this.hexChessBoardController.getRecommendedMineCount();
            }
            else {
                this.currentMineCount = 15; // 备用固定值
            }
        }
        else {
            // 默认值
            this.currentMineCount = 13;
        }
        // 更新炸弹数UI
        this.updateMineCountDisplay(this.currentMineCount);
        // 根据地图类型控制地图节点的显示与隐藏
        this.switchMapDisplay(this.currentMapType);
        // 初始化分数界面（使用后端传回来的真实数据）
        if (this.gameScoreController) {
            this.gameScoreController.initializeScoreView();
        }
        // 显示游戏开始节点动画
        this.showGameStartAnimation();
    };
    /**
     * 测试重置功能（可以在浏览器控制台手动调用）
     */
    GamePageController.prototype.testReset = function () {
        if (this.chessBoardController) {
            this.chessBoardController.resetGameScene();
        }
        else {
            console.error("❌ chessBoardController 不存在！");
        }
    };
    // 处理扫雷回合开始通知
    GamePageController.prototype.onNoticeRoundStart = function (data) {
        this.currentRoundNumber = data.roundNumber || 1;
        this.currentCountdown = data.countDown || 25;
        this.gameStatus = data.gameStatus || 0;
        // 隐藏游戏开始节点
        this.hideGameStartAnimation();
        // 新回合开始，重置操作状态
        this.canOperate = true;
        this.hasOperatedThisRound = false;
        // 清理棋盘上的所有玩家预制体
        if (this.currentMapType === 0 && this.chessBoardController) {
            // 方形地图
            this.chessBoardController.clearAllPlayerNodes();
        }
        else if (this.currentMapType === 1 && this.hexChessBoardController) {
            // 六边形地图
            this.hexChessBoardController.clearAllPlayerNodes();
        }
        // 开始倒计时
        this.startCountdown(this.currentCountdown);
    };
    // 处理扫雷操作展示通知
    GamePageController.prototype.onNoticeActionDisplay = function (data) {
        // 保存当前NoticeActionDisplay数据，用于倒计时显示逻辑
        this.currentNoticeActionData = data;
        // 进入展示阶段，不能再操作
        this.canOperate = false;
        this.gameStatus = data.gameStatus || 0;
        // 根据countDown重置倒计时为5秒
        this.currentCountdown = data.countDown || 5;
        this.updateCountdownDisplay(this.currentCountdown);
        this.startCountdown(this.currentCountdown);
        // 更新剩余炸弹数量显示
        if (data.remainingMines !== undefined) {
            this.updateMineCountDisplay(data.remainingMines);
        }
        // 在棋盘上显示所有玩家的操作（头像）
        this.displayPlayerActions(data.playerActions, data.playerTotalScores);
        // 立即显示先手+1（如果先手不是我）
        this.showFirstChoiceBonusImmediately(data.playerActions);
    };
    /**
     * 立即显示先手+1奖励（只为其他人显示，如果我是先手则不显示）
     * @param playerActions 玩家操作列表
     */
    GamePageController.prototype.showFirstChoiceBonusImmediately = function (playerActions) {
        var _a, _b;
        // 获取当前用户ID
        var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
        if (!currentUserId) {
            console.warn("无法获取当前用户ID");
            return;
        }
        // 查找先手玩家
        var firstChoicePlayer = playerActions.find(function (action) { return action.isFirstChoice; });
        // 如果先手玩家存在且不是我，才显示+1
        if (firstChoicePlayer && firstChoicePlayer.userId !== currentUserId) {
            var firstChoiceUserIndex = this.findUserIndex(firstChoicePlayer.userId);
            if (firstChoiceUserIndex !== -1) {
                // 立即显示先手+1
                this.showScoreInScorePanel(firstChoiceUserIndex, 1);
                // 同时在player_game_pfb显示先手+1
                this.showScoreOnPlayerAvatar(firstChoicePlayer.userId, 1);
            }
        }
    };
    /**
     * 延迟更新棋盘的回调方法
     * @param data NoticeActionDisplay数据
     */
    GamePageController.prototype.delayedUpdateBoard = function (data) {
        this.updateBoardAfterActions(data);
    };
    /**
     * 更新棋盘（删除格子、生成预制体、连锁动画）
     * @param data NoticeActionDisplay数据
     */
    GamePageController.prototype.updateBoardAfterActions = function (data) {
        // 注意：分数动画和头像删除现在由倒计时逻辑控制，这里直接处理格子隐藏和数字生成
        var _this = this;
        // 立即处理每个玩家的操作结果
        // 先按位置分组，处理同一位置有多个操作的情况
        var processedPositions = new Set();
        data.playerActions.forEach(function (action) {
            var positionKey = action.x + "," + action.y;
            // 如果这个位置已经处理过，跳过
            if (processedPositions.has(positionKey)) {
                return;
            }
            // 查找同一位置的所有操作
            var samePositionActions = data.playerActions.filter(function (a) {
                return a.x === action.x && a.y === action.y;
            });
            // 处理同一位置的操作结果（格子隐藏和数字生成）
            _this.processPositionResult(action.x, action.y, samePositionActions);
            // 标记这个位置已处理
            processedPositions.add(positionKey);
        });
        // 处理连锁展开结果
        if (data.floodFillResults && data.floodFillResults.length > 0) {
            data.floodFillResults.forEach(function (floodFill) {
                _this.processFloodFillResult(floodFill);
            });
        }
    };
    /**
     * 让所有头像消失（支持方形地图和六边形地图）
     * @param playerActions 玩家操作列表
     * @param onComplete 完成回调
     */
    GamePageController.prototype.hideAllAvatars = function (playerActions, onComplete) {
        // 根据地图类型调用对应的控制器
        if (this.currentMapType === 0 && this.chessBoardController) {
            // 方形地图：直接调用一次头像删除，不区分位置
            this.chessBoardController.hideAvatarsAtPosition(0, 0, function () {
                onComplete();
            });
        }
        else if (this.currentMapType === 1 && this.hexChessBoardController) {
            // 六边形地图：直接调用方法（已经编译成功）
            this.hexChessBoardController.hideAllHexAvatars(function () {
                onComplete();
            });
        }
        else {
            // 没有可用的控制器，直接执行回调
            console.warn("没有可用的棋盘控制器，跳过头像消失动画");
            onComplete();
        }
    };
    /**
     * 处理同一位置的多个操作结果
     * @param x 格子x坐标（方形地图）或q坐标（六边形地图）
     * @param y 格子y坐标（方形地图）或r坐标（六边形地图）
     * @param actions 该位置的所有操作
     */
    GamePageController.prototype.processPositionResult = function (x, y, actions) {
        var _a, _b;
        // 根据地图类型删除该位置的格子（立即隐藏，不播放动画）
        if (this.currentMapType === 0) {
            // 方形地图
            this.chessBoardController.removeGridAt(x, y, true);
        }
        else if (this.currentMapType === 1) {
            // 六边形地图，x实际是q坐标，y实际是r坐标
            if (this.hexChessBoardController) {
                this.hexChessBoardController.hideHexGridAt(x, y, true);
            }
        }
        // 检查是否有地雷被点击（action=1且result="mine"）
        var mineClickAction = actions.find(function (action) {
            return action.action === 1 && action.result === "mine";
        });
        if (mineClickAction) {
            // 如果有地雷被点击，直接显示炸弹，不管是否有标记
            // 判断是否是当前用户点到的雷
            var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
            var isCurrentUser = mineClickAction.userId === currentUserId;
            // 根据地图类型调用对应的方法
            if (this.currentMapType === 0) {
                // 方形地图
                this.chessBoardController.createBoomPrefab(x, y, isCurrentUser);
            }
            else if (this.currentMapType === 1) {
                // 六边形地图，x实际是q坐标，y实际是r坐标
                if (this.hexChessBoardController) {
                    this.hexChessBoardController.createHexBoomPrefab(x, y, isCurrentUser);
                }
            }
            return;
        }
        // 如果没有地雷被点击，按原逻辑处理第一个操作的结果
        var firstAction = actions[0];
        var result = firstAction.result;
        if (result === "correct_mark") {
            // 正确标记：生成biaoji预制体
            if (this.currentMapType === 0) {
                // 方形地图
                this.chessBoardController.createBiaojiPrefab(x, y);
            }
            else if (this.currentMapType === 1) {
                // 六边形地图
                if (this.hexChessBoardController) {
                    this.hexChessBoardController.createHexBiaojiPrefab(x, y);
                }
            }
        }
        else if (typeof result === "number") {
            // 数字：更新neighborMines显示
            if (this.currentMapType === 0) {
                // 方形地图
                this.chessBoardController.updateNeighborMinesDisplay(x, y, result);
            }
            else if (this.currentMapType === 1) {
                // 六边形地图
                if (this.hexChessBoardController) {
                    this.hexChessBoardController.updateHexNeighborMinesDisplay(x, y, result);
                }
            }
        }
    };
    /**
     * 处理单个玩家操作结果（保留原方法以防其他地方调用）
     * @param action 玩家操作数据
     */
    GamePageController.prototype.processPlayerActionResult = function (action) {
        var _a, _b;
        var x = action.x;
        var y = action.y;
        var result = action.result;
        // 删除该位置的格子
        this.chessBoardController.removeGridAt(x, y);
        // 根据结果生成相应的预制体
        if (result === "mine") {
            // 地雷：生成boom预制体
            // 判断是否是当前用户点到的雷
            var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
            var isCurrentUser = action.userId === currentUserId;
            this.chessBoardController.createBoomPrefab(x, y, isCurrentUser);
        }
        else if (result === "correct_mark") {
            // 正确标记：生成biaoji预制体
            this.chessBoardController.createBiaojiPrefab(x, y);
        }
        else if (typeof result === "number") {
            // 数字：更新neighborMines显示
            this.chessBoardController.updateNeighborMinesDisplay(x, y, result);
        }
    };
    /**
     * 处理连锁展开结果
     * @param floodFill 连锁展开数据
     */
    GamePageController.prototype.processFloodFillResult = function (floodFill) {
        var _this = this;
        // 立即处理所有连锁格子，不再有延迟
        floodFill.revealedBlocks.forEach(function (block) {
            if (_this.currentMapType === 0) {
                // 方形地图：立即隐藏格子并显示数字
                _this.chessBoardController.removeGridAt(block.x, block.y, true);
                // 立即显示数字
                if (block.neighborMines > 0) {
                    _this.chessBoardController.updateNeighborMinesDisplay(block.x, block.y, block.neighborMines);
                }
            }
            else if (_this.currentMapType === 1) {
                // 六边形地图，x实际是q坐标，y实际是r坐标
                if (_this.hexChessBoardController) {
                    // 立即隐藏格子
                    _this.hexChessBoardController.hideHexGridAt(block.x, block.y, true);
                    // 立即显示数字（如果有的话）
                    if (block.neighborMines > 0) {
                        _this.hexChessBoardController.updateHexNeighborMinesDisplay(block.x, block.y, block.neighborMines);
                    }
                }
            }
        });
    };
    // 处理扫雷回合结束通知
    GamePageController.prototype.onNoticeRoundEnd = function (data) {
        // 进入回合结束阶段，不能再操作
        this.canOperate = false;
        this.gameStatus = data.gameStatus || 1;
        // 不再处理倒计时，让客户端自然倒计时到0，方便展示54321
        // 处理玩家分数动画和头像显示
        if (data.playerResults && data.playerResults.length > 0) {
            this.displayPlayerScoreAnimations(data.playerResults);
            // 如果本回合我没有操作，根据后端消息生成我的头像
            this.handleMyAvatarIfNotOperated(data.playerResults);
        }
        // 清理棋盘上的所有玩家预制体
        if (this.currentMapType === 0 && this.chessBoardController) {
            // 方形地图
            this.chessBoardController.clearAllPlayerNodes();
        }
        else if (this.currentMapType === 1 && this.hexChessBoardController) {
            // 六边形地图
            this.hexChessBoardController.clearAllPlayers();
        }
    };
    /**
     * 在棋盘上显示所有玩家的操作
     * @param playerActions 玩家操作列表
     * @param playerTotalScores 玩家总分数据
     */
    GamePageController.prototype.displayPlayerActions = function (playerActions, playerTotalScores) {
        var _this = this;
        var _a, _b;
        // 检查是否有可用的棋盘控制器
        var hasSquareBoard = this.chessBoardController && this.currentMapType === 0;
        var hasHexBoard = this.hexChessBoardController && this.currentMapType === 1;
        if ((!hasSquareBoard && !hasHexBoard) || !playerActions || playerActions.length === 0) {
            return;
        }
        // 获取当前用户ID
        var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
        if (!currentUserId) {
            console.warn("无法获取当前用户ID");
            return;
        }
        // 注意：分数动画已经在updateBoardAfterActions的第一步显示了，这里不再重复显示
        // 检查本回合是否进行了操作，如果没有，需要显示自己的头像
        var myAction = playerActions.find(function (action) { return action.userId === currentUserId; });
        var shouldDisplayMyAvatar = false;
        if (!this.hasOperatedThisRound && myAction) {
            shouldDisplayMyAvatar = true;
            // 生成我的头像
            var withFlag = (myAction.action === 2); // action=2表示标记操作，显示旗子
            if (this.currentMapType === 0) {
                // 方形地图
                this.chessBoardController.placePlayerOnGrid(myAction.x, myAction.y, withFlag);
            }
            else if (this.currentMapType === 1) {
                // 六边形地图，x实际是q坐标，y实际是r坐标
                if (this.hexChessBoardController) {
                    this.hexChessBoardController.placePlayerOnHexGrid(myAction.x, myAction.y, withFlag);
                }
            }
        }
        // 过滤掉自己的操作，只显示其他玩家的操作
        var otherPlayersActions = playerActions.filter(function (action) { return action.userId !== currentUserId; });
        if (otherPlayersActions.length === 0) {
            return;
        }
        // 按位置分组其他玩家的操作
        var positionGroups = this.groupActionsByPosition(otherPlayersActions);
        // 为每个位置生成预制体
        positionGroups.forEach(function (actions, positionKey) {
            var _a = positionKey.split(',').map(Number), x = _a[0], y = _a[1];
            if (_this.currentMapType === 0) {
                // 方形地图
                _this.chessBoardController.displayOtherPlayersAtPosition(x, y, actions);
            }
            else if (_this.currentMapType === 1) {
                // 六边形地图，x实际是q坐标，y实际是r坐标
                if (_this.hexChessBoardController) {
                    // 直接调用方法（已经编译成功）
                    _this.hexChessBoardController.displayOtherPlayersAtHexPosition(x, y, actions);
                }
            }
        });
    };
    /**
     * 按位置分组玩家操作
     * @param playerActions 玩家操作列表
     * @returns Map<string, PlayerActionDisplay[]> 位置为key，操作列表为value
     */
    GamePageController.prototype.groupActionsByPosition = function (playerActions) {
        var groups = new Map();
        for (var _i = 0, playerActions_1 = playerActions; _i < playerActions_1.length; _i++) {
            var action = playerActions_1[_i];
            var positionKey = action.x + "," + action.y;
            if (!groups.has(positionKey)) {
                groups.set(positionKey, []);
            }
            groups.get(positionKey).push(action);
        }
        return groups;
    };
    /**
     * 显示玩家分数动画
     * @param playerResults 玩家回合结果列表
     */
    GamePageController.prototype.displayPlayerScoreAnimations = function (playerResults) {
        var _this = this;
        var _a, _b;
        // 获取当前用户ID
        var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
        if (!currentUserId) {
            console.warn("无法获取当前用户ID");
            return;
        }
        // 为每个玩家显示分数动画
        playerResults.forEach(function (result, index) {
            // 延迟显示，让动画错开
            _this.scheduleOnce(function () {
                _this.showPlayerScoreAnimation(result, currentUserId);
            }, index * 0.2);
        });
    };
    /**
     * 显示单个玩家的分数动画
     * @param result 玩家回合结果
     * @param currentUserId 当前用户ID
     */
    GamePageController.prototype.showPlayerScoreAnimation = function (result, currentUserId) {
        var isMyself = result.userId === currentUserId;
        if (isMyself) {
            // 自己的分数动画：在player_game_pfb里只显示本回合得分
            this.showMyScoreAnimation(result);
        }
        else {
            // 其他人的分数动画：根据isFirstChoice决定显示逻辑
            this.showOtherPlayerScoreAnimation(result);
        }
    };
    /**
     * 显示自己的分数动画
     * @param result 玩家回合结果
     */
    GamePageController.prototype.showMyScoreAnimation = function (result) {
        // 在棋盘上的头像预制体中显示本回合得分
        if (this.currentMapType === 0 && this.chessBoardController) {
            // 方形地图
            this.chessBoardController.showScoreOnPlayerNode(result.x, result.y, result.score, false);
        }
        else if (this.currentMapType === 1 && this.hexChessBoardController) {
            // 六边形地图，x实际是q坐标，y实际是r坐标
            this.hexChessBoardController.showScoreOnHexPlayerNode(result.x, result.y, result.score, false);
        }
        // 在player_score_pfb中显示分数动画
        this.showScoreAnimationInScorePanel(result.userId, result.score, result.isFirstChoice);
    };
    /**
     * 显示其他玩家的分数动画
     * @param result 玩家回合结果
     */
    GamePageController.prototype.showOtherPlayerScoreAnimation = function (result) {
        if (result.isFirstChoice) {
            // 其他人为先手：player_game_pfb里不显示+1，只显示本回合得分
            if (this.currentMapType === 0 && this.chessBoardController) {
                // 方形地图
                this.chessBoardController.showScoreOnPlayerNode(result.x, result.y, result.score, false);
            }
            else if (this.currentMapType === 1 && this.hexChessBoardController) {
                // 六边形地图
                this.hexChessBoardController.showScoreOnHexPlayerNode(result.x, result.y, result.score, false);
            }
            // 在player_score_pfb里先显示+1，再显示本回合得分，然后更新总分
            this.showFirstChoiceScoreAnimation(result.userId, result.score);
        }
        else {
            // 其他人非先手：正常显示本回合得分
            if (this.currentMapType === 0 && this.chessBoardController) {
                // 方形地图
                this.chessBoardController.showScoreOnPlayerNode(result.x, result.y, result.score, false);
            }
            else if (this.currentMapType === 1 && this.hexChessBoardController) {
                // 六边形地图
                this.hexChessBoardController.showScoreOnHexPlayerNode(result.x, result.y, result.score, false);
            }
            // 在player_score_pfb中显示分数动画
            this.showScoreAnimationInScorePanel(result.userId, result.score, false);
        }
    };
    /**
     * 在分数面板中显示分数动画
     * @param userId 用户ID
     * @param score 本回合得分
     * @param isFirstChoice 是否为先手
     */
    GamePageController.prototype.showScoreAnimationInScorePanel = function (userId, score, isFirstChoice) {
        // 这里需要找到对应的PlayerScoreController并调用分数动画
        // 由于没有直接的引用，这里先用日志记录
        // TODO: 实现在player_score_pfb中显示分数动画的逻辑
        // 需要找到对应用户的PlayerScoreController实例并调用showAddScore方法
    };
    /**
     * 显示先手玩家的分数动画（先显示+1，再显示本回合得分）
     * @param userId 用户ID
     * @param score 本回合得分
     */
    GamePageController.prototype.showFirstChoiceScoreAnimation = function (userId, score) {
        var _this = this;
        // 先显示+1的先手奖励
        this.scheduleOnce(function () {
            _this.showScoreAnimationInScorePanel(userId, 1, true);
        }, 0.1);
        // 再显示本回合得分
        this.scheduleOnce(function () {
            _this.showScoreAnimationInScorePanel(userId, score, false);
        }, 1.2);
        // 最后更新总分
        this.scheduleOnce(function () {
            _this.updatePlayerTotalScore(userId, score + 1);
        }, 2.4);
    };
    /**
     * 更新玩家总分
     * @param userId 用户ID
     * @param totalScore 新的总分
     */
    GamePageController.prototype.updatePlayerTotalScore = function (userId, totalScore) {
        // TODO: 实现更新玩家总分的逻辑
        // 需要更新GlobalBean中的用户数据，并刷新UI显示
    };
    /**
     * 如果本回合我没有操作，根据后端消息生成我的头像
     * @param playerResults 玩家回合结果列表
     */
    GamePageController.prototype.handleMyAvatarIfNotOperated = function (playerResults) {
        var _a, _b;
        // 获取当前用户ID
        var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
        if (!currentUserId) {
            console.warn("无法获取当前用户ID");
            return;
        }
        // 检查本回合是否进行了操作
        if (this.hasOperatedThisRound) {
            return;
        }
        // 查找我的操作结果
        var myResult = playerResults.find(function (result) { return result.userId === currentUserId; });
        if (!myResult) {
            return;
        }
        // 根据后端消息生成我的头像
        if (this.chessBoardController) {
            // 根据操作类型决定是否显示旗子
            var withFlag = (myResult.action === 2); // action=2表示标记操作，显示旗子
            // 生成我的头像预制体
            this.chessBoardController.placePlayerOnGrid(myResult.x, myResult.y, withFlag);
        }
    };
    // 发送点击方块消息
    GamePageController.prototype.sendClickBlock = function (x, y, action) {
        if (!this.canOperate) {
            return;
        }
        // 检查本回合是否已经操作过
        if (this.hasOperatedThisRound) {
            return;
        }
        var clickData = {
            x: x,
            y: y,
            action: action // 1=挖掘方块，2=标记/取消标记地雷
        };
        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeClickBlock, clickData);
        // 标记本回合已经操作过，防止重复操作
        this.hasOperatedThisRound = true;
    };
    // 发送六边形点击方块消息
    GamePageController.prototype.sendHexClickBlock = function (q, r, action) {
        if (!this.canOperate) {
            return;
        }
        // 检查本回合是否已经操作过
        if (this.hasOperatedThisRound) {
            return;
        }
        // 根据当前地图类型决定发送格式
        if (this.currentMapType === 1) {
            // 六边形地图：使用六边形坐标格式
            var hexClickData = {
                q: q,
                r: r,
                action: action // 1=挖掘方块，2=标记/取消标记地雷
            };
            // 注意：这里仍然使用 MsgTypeClickBlock，但数据格式不同
            // 后端应该根据当前房间的 mapType 来解析不同的坐标格式
            WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeClickBlock, hexClickData);
        }
        else {
            // 方形地图：转换为x,y坐标（备用方案）
            var clickData = {
                x: q,
                y: r,
                action: action
            };
            WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeClickBlock, clickData);
        }
        // 标记本回合已经操作过，防止重复操作
        this.hasOperatedThisRound = true;
    };
    // 检查是否可以操作
    GamePageController.prototype.isCanOperate = function () {
        return this.canOperate && !this.hasOperatedThisRound;
    };
    /**
     * 处理首选玩家奖励通知
     * @param data NoticeFirstChoiceBonus 消息数据
     */
    GamePageController.prototype.onNoticeFirstChoiceBonus = function (data) {
        var _a, _b;
        // 转发给GameScoreController处理所有玩家的分数更新和加分动画
        if (this.gameScoreController) {
            this.gameScoreController.onNoticeFirstChoiceBonus(data);
        }
        // 判断是否为当前用户，如果是则同时更新player_game_pfb中的change_score
        var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
        var isMyself = (data.userId === currentUserId);
        if (isMyself) {
            // 更新player_game_pfb中的change_score显示
            this.updatePlayerGameScore(data.userId, data.bonusScore);
        }
    };
    /**
     * 更新player_game_pfb中的change_score显示
     * @param userId 用户ID
     * @param bonusScore 奖励分数
     */
    GamePageController.prototype.updatePlayerGameScore = function (userId, bonusScore) {
        // 根据地图类型调用对应的控制器显示加分效果
        if (this.currentMapType === 0 && this.chessBoardController) {
            // 方形地图
            this.chessBoardController.showPlayerGameScore(userId, bonusScore);
        }
        else if (this.currentMapType === 1 && this.hexChessBoardController) {
            // 六边形地图
            this.hexChessBoardController.showHexPlayerGameScore(userId, bonusScore);
        }
        else {
            console.warn("\u5730\u56FE\u7C7B\u578B" + this.currentMapType + "\u7684\u68CB\u76D8\u63A7\u5236\u5668\u672A\u8BBE\u7F6E\uFF0C\u65E0\u6CD5\u663E\u793Aplayer_game_pfb\u52A0\u5206\u6548\u679C");
        }
    };
    // 获取当前地图类型
    GamePageController.prototype.getCurrentMapType = function () {
        return this.currentMapType;
    };
    // 获取当前炸弹数量
    GamePageController.prototype.getCurrentMineCount = function () {
        return this.currentMineCount;
    };
    // 获取当前回合操作状态（用于调试）
    GamePageController.prototype.getCurrentRoundStatus = function () {
        return {
            roundNumber: this.currentRoundNumber,
            canOperate: this.canOperate,
            hasOperated: this.hasOperatedThisRound
        };
    };
    // 开始倒计时
    GamePageController.prototype.startCountdown = function (seconds) {
        var _this = this;
        // 清除之前的计时器
        this.clearCountdownTimer();
        var remainingSeconds = seconds;
        this.updateCountdownDisplay(remainingSeconds);
        this.countdownInterval = setInterval(function () {
            remainingSeconds--;
            console.log("\u5012\u8BA1\u65F6\u66F4\u65B0: " + remainingSeconds + "\u79D2, gameStatus: " + _this.gameStatus + ", hasNoticeData: " + !!_this.currentNoticeActionData);
            _this.updateCountdownDisplay(remainingSeconds);
            // 在NoticeActionDisplay阶段，根据倒计时执行不同的显示逻辑
            if (_this.gameStatus === 0 && _this.currentNoticeActionData) {
                _this.handleCountdownBasedDisplay(remainingSeconds);
            }
            if (remainingSeconds <= 0) {
                console.log("倒计时结束，清除计时器");
                _this.clearCountdownTimer();
            }
        }, 1000);
    };
    // 更新倒计时显示
    GamePageController.prototype.updateCountdownDisplay = function (seconds) {
        if (this.timeLabel) {
            this.timeLabel.string = seconds + "s"; // 显示数字加s：5s, 4s, 3s, 2s, 1s, 0s
        }
        this.currentCountdown = seconds;
    };
    /**
     * 根据倒计时处理不同时机的显示逻辑
     * @param remainingSeconds 剩余秒数
     */
    GamePageController.prototype.handleCountdownBasedDisplay = function (remainingSeconds) {
        if (!this.currentNoticeActionData) {
            return;
        }
        var data = this.currentNoticeActionData;
        if (remainingSeconds === 4) {
            // 4s时：同时展示本回合加减分
            this.showCurrentRoundScores(data.playerActions, data.playerTotalScores);
        }
        else if (remainingSeconds === 3) {
            // 3s时：隐藏加减分并删除头像预制体
            this.hideScoreEffectsAndAvatars(data.playerActions);
            // 3s时：立即执行格子隐藏和生成数字预制体等操作
            this.updateBoardAfterActions(data);
        }
        else if (remainingSeconds === 2) {
            // 2s时：显示回合开始节点动画
            console.log("倒计时剩余2秒，准备显示回合开始动画");
            this.showRoundStartAnimation();
            // 2s时：清空数据，避免重复执行（移到这里，确保动画能正常触发）
            this.currentNoticeActionData = null;
        }
    };
    /**
     * 显示本回合所有玩家的加减分
     * @param playerActions 玩家操作列表
     * @param playerTotalScores 玩家总分数据
     */
    GamePageController.prototype.showCurrentRoundScores = function (playerActions, playerTotalScores) {
        var _this = this;
        var _a, _b;
        // 获取当前用户ID
        var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
        if (!currentUserId) {
            console.warn("无法获取当前用户ID");
            return;
        }
        // 为每个玩家显示本回合的加减分
        playerActions.forEach(function (action) {
            // 在player_game_pfb中显示本回合的加减分
            _this.showScoreOnPlayerAvatar(action.userId, action.score);
            // 在分数面板显示本回合的加减分
            var userIndex = _this.findUserIndex(action.userId);
            if (userIndex !== -1) {
                _this.showScoreInScorePanel(userIndex, action.score);
            }
        });
        // 延迟更新总分，让加减分动画先显示
        this.scheduleOnce(function () {
            // 更新所有玩家的总分
            playerActions.forEach(function (action) {
                var totalScore = playerTotalScores[action.userId] || 0;
                if (_this.gameScoreController) {
                    _this.gameScoreController.updatePlayerScore(action.userId, totalScore);
                    // 更新全局数据中的总分
                    _this.updatePlayerTotalScoreInGlobalData(action.userId, totalScore);
                }
            });
        }, 1.2);
    };
    /**
     * 隐藏加减分效果并删除头像预制体
     * @param playerActions 玩家操作列表
     */
    GamePageController.prototype.hideScoreEffectsAndAvatars = function (playerActions) {
        // 隐藏所有加减分效果
        this.hideAllScoreEffects();
        // 删除头像预制体（不等待完成回调）
        this.hideAllAvatars(playerActions, function () {
            console.log("所有头像已隐藏完成");
        });
    };
    /**
     * 隐藏所有加减分效果
     */
    GamePageController.prototype.hideAllScoreEffects = function () {
        // 隐藏分数面板的加减分效果
        // 注意：这里暂时不处理分数面板的隐藏，因为PlayerScoreController的hideScoreEffects会在1秒后自动隐藏
        // 隐藏棋盘上所有头像的加减分效果
        this.hideAllPlayerGameScoreEffects();
    };
    /**
     * 隐藏棋盘上所有头像的加减分效果
     */
    GamePageController.prototype.hideAllPlayerGameScoreEffects = function () {
        // 遍历棋盘上的所有PlayerGameController，调用hideScoreEffects方法
        if (this.currentMapType === 0 && this.chessBoardController && this.chessBoardController.boardNode) {
            // 方形地图
            var children = this.chessBoardController.boardNode.children;
            for (var i = 0; i < children.length; i++) {
                var child = children[i];
                var playerController = child.getComponent(PlayerGameController_1.default);
                if (playerController) {
                    playerController.hideScoreEffects();
                }
            }
        }
        else if (this.currentMapType === 1 && this.hexChessBoardController && this.hexChessBoardController.boardNode) {
            // 六边形地图
            var children = this.hexChessBoardController.boardNode.children;
            for (var i = 0; i < children.length; i++) {
                var child = children[i];
                var playerController = child.getComponent(PlayerGameController_1.default);
                if (playerController) {
                    playerController.hideScoreEffects();
                }
            }
        }
    };
    // 更新炸弹数显示
    GamePageController.prototype.updateMineCountDisplay = function (mineCount) {
        if (this.mineCountLabel) {
            this.mineCountLabel.string = "" + mineCount;
        }
    };
    // 根据地图类型切换地图显示
    GamePageController.prototype.switchMapDisplay = function (mapType) {
        // 先隐藏所有地图
        this.hideAllMaps();
        // 根据地图类型显示对应的地图
        if (mapType === 0) {
            this.showSquareMap();
        }
        else if (mapType === 1) {
            this.showHexMap();
        }
        else {
            console.warn("\u672A\u77E5\u7684\u5730\u56FE\u7C7B\u578B: " + mapType + "\uFF0C\u9ED8\u8BA4\u663E\u793A\u65B9\u5F62\u5730\u56FE");
            this.showSquareMap();
        }
    };
    // 显示方形地图
    GamePageController.prototype.showSquareMap = function () {
        if (this.squareMapNode) {
            this.squareMapNode.active = true;
        }
        else {
            console.warn('方形地图节点未挂载');
        }
    };
    // 显示六边形地图
    GamePageController.prototype.showHexMap = function () {
        if (this.hexMapNode) {
            this.hexMapNode.active = true;
        }
        else {
            console.warn('六边形地图节点未挂载');
        }
    };
    // 隐藏所有地图
    GamePageController.prototype.hideAllMaps = function () {
        if (this.squareMapNode) {
            this.squareMapNode.active = false;
        }
        if (this.hexMapNode) {
            this.hexMapNode.active = false;
        }
    };
    // 清除倒计时定时器
    GamePageController.prototype.clearCountdownTimer = function () {
        if (this.countdownInterval) {
            clearInterval(this.countdownInterval);
            this.countdownInterval = null;
        }
    };
    /**
     * 显示所有玩家的分数动画和更新总分（参考先手加分逻辑）
     * @param playerActions 玩家操作列表
     * @param playerTotalScores 玩家总分数据
     */
    GamePageController.prototype.displayPlayerScoreAnimationsAndUpdateTotalScores = function (playerActions, playerTotalScores) {
        var _this = this;
        var _a, _b;
        // 获取当前用户ID
        var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
        if (!currentUserId) {
            console.warn("无法获取当前用户ID");
            return;
        }
        // 查找先手玩家
        var firstChoicePlayer = playerActions.find(function (action) { return action.isFirstChoice; });
        var isCurrentUserFirstChoice = firstChoicePlayer && firstChoicePlayer.userId === currentUserId;
        // 如果我不是先手，先为先手玩家在分数面板显示+1
        if (!isCurrentUserFirstChoice && firstChoicePlayer) {
            var firstChoiceUserIndex_1 = this.findUserIndex(firstChoicePlayer.userId);
            if (firstChoiceUserIndex_1 !== -1) {
                // 0.1秒后显示先手+1
                this.scheduleOnce(function () {
                    _this.showScoreInScorePanel(firstChoiceUserIndex_1, 1);
                }, 0.1);
            }
        }
        // 为每个玩家显示分数动画和更新总分
        playerActions.forEach(function (action, index) {
            var totalScore = playerTotalScores[action.userId] || 0;
            var isFirstChoice = action.isFirstChoice;
            // 延迟显示，让动画错开
            _this.scheduleOnce(function () {
                if (isFirstChoice) {
                    // 先手玩家：特殊处理（先显示+1，再显示本回合分数）
                    _this.showFirstChoicePlayerScoreAnimation(action, currentUserId, totalScore);
                }
                else {
                    // 非先手玩家：直接显示本回合分数
                    _this.showPlayerScoreAnimationAndUpdateTotal(action, currentUserId, totalScore);
                }
            }, index * 0.2);
        });
    };
    /**
     * 显示单个玩家的分数动画和更新总分（参考先手加分逻辑）
     * @param action 玩家操作数据
     * @param currentUserId 当前用户ID
     * @param totalScore 玩家总分
     */
    GamePageController.prototype.showPlayerScoreAnimationAndUpdateTotal = function (action, currentUserId, totalScore) {
        var _this = this;
        var isMyself = action.userId === currentUserId;
        // 1. 在分数面板显示加减分动画（参考先手加分的逻辑）
        if (this.gameScoreController) {
            // 找到用户索引
            var userIndex = this.findUserIndex(action.userId);
            if (userIndex !== -1) {
                // 在分数面板显示加减分效果
                this.showScoreInScorePanel(userIndex, action.score);
            }
        }
        // 2. 更新总分（参考先手加分的updatePlayerScore）
        this.scheduleOnce(function () {
            if (_this.gameScoreController) {
                _this.gameScoreController.updatePlayerScore(action.userId, totalScore);
                // 更新全局数据中的总分
                _this.updatePlayerTotalScoreInGlobalData(action.userId, totalScore);
            }
        }, 1.2);
        // 3. 在所有玩家头像上显示加减分（不仅仅是自己）
        this.scheduleOnce(function () {
            _this.showScoreOnPlayerAvatar(action.userId, action.score);
        }, 0.1);
    };
    /**
     * 更新全局数据中的玩家总分
     * @param userId 用户ID
     * @param totalScore 新的总分
     */
    GamePageController.prototype.updatePlayerTotalScoreInGlobalData = function (userId, totalScore) {
        if (!GlobalBean_1.GlobalBean.GetInstance().noticeStartGame || !GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users) {
            console.warn("没有游戏数据，无法更新玩家总分");
            return;
        }
        var users = GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users;
        var userIndex = users.findIndex(function (user) { return user.userId === userId; });
        if (userIndex !== -1) {
            users[userIndex].score = totalScore;
        }
        else {
            console.warn("\u627E\u4E0D\u5230\u73A9\u5BB6: userId=" + userId);
        }
    };
    /**
     * 查找用户索引
     * @param userId 用户ID
     * @returns 用户索引，找不到返回-1
     */
    GamePageController.prototype.findUserIndex = function (userId) {
        if (!GlobalBean_1.GlobalBean.GetInstance().noticeStartGame || !GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users) {
            console.warn("没有游戏数据，无法查找用户索引");
            return -1;
        }
        var users = GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users;
        return users.findIndex(function (user) { return user.userId === userId; });
    };
    /**
     * 在玩家头像上显示加减分
     * @param userId 用户ID
     * @param score 分数变化
     */
    GamePageController.prototype.showScoreOnPlayerAvatar = function (userId, score) {
        // 根据地图类型调用对应的控制器
        if (this.currentMapType === 0 && this.chessBoardController) {
            // 方形地图
            this.chessBoardController.showPlayerGameScore(userId, score);
        }
        else if (this.currentMapType === 1 && this.hexChessBoardController) {
            // 六边形地图
            this.hexChessBoardController.showHexPlayerGameScore(userId, score);
        }
        else {
            console.warn("没有可用的棋盘控制器，无法显示头像分数");
        }
    };
    /**
     * 在分数面板显示加减分效果
     * @param userIndex 用户索引
     * @param score 分数变化
     */
    GamePageController.prototype.showScoreInScorePanel = function (userIndex, score) {
        if (!this.gameScoreController) {
            console.warn("gameScoreController 不存在，无法在分数面板显示分数");
            return;
        }
        // 获取对应的PlayerScoreController
        var playerScoreController = this.gameScoreController.getPlayerScoreController(userIndex);
        if (playerScoreController) {
            // 显示加减分效果
            if (score > 0) {
                playerScoreController.showAddScore(score);
            }
            else if (score < 0) {
                playerScoreController.showSubScore(Math.abs(score));
            }
        }
        else {
            console.warn("\u627E\u4E0D\u5230\u7528\u6237\u7D22\u5F15 " + userIndex + " \u5BF9\u5E94\u7684PlayerScoreController");
        }
    };
    /**
     * 显示先手玩家的分数动画（在分数面板先显示+1，再显示本回合分数）
     * @param action 玩家操作数据
     * @param currentUserId 当前用户ID
     * @param totalScore 玩家总分
     */
    GamePageController.prototype.showFirstChoicePlayerScoreAnimation = function (action, currentUserId, totalScore) {
        var _this = this;
        var userIndex = this.findUserIndex(action.userId);
        // 第一步：在分数面板显示+1先手奖励（1.2秒，与非先手玩家同步）
        this.scheduleOnce(function () {
            // 分数面板显示本回合分数（+1已经在前面显示过了）
            if (userIndex !== -1) {
                _this.showScoreInScorePanel(userIndex, action.score);
            }
        }, 1.2);
        // 第二步：更新总分（2.4秒）
        this.scheduleOnce(function () {
            if (_this.gameScoreController) {
                _this.gameScoreController.updatePlayerScore(action.userId, totalScore);
                _this.updatePlayerTotalScoreInGlobalData(action.userId, totalScore);
            }
        }, 2.4);
        // 第三步：在player_game_pfb中显示本回合的加减分（与非先手玩家同步）
        this.scheduleOnce(function () {
            _this.showScoreOnPlayerAvatar(action.userId, action.score);
        }, 0.1);
    };
    /**
     * 初始化动画节点
     */
    GamePageController.prototype.initializeAnimationNodes = function () {
        console.log("开始初始化动画节点");
        // 如果节点没有在编辑器中设置，尝试通过路径查找或创建
        if (!this.gameStartNode) {
            this.gameStartNode = cc.find('Canvas/game_start_node');
            if (!this.gameStartNode) {
                // 创建游戏开始节点
                this.gameStartNode = this.createGameStartNode();
            }
        }
        if (!this.roundStartNode) {
            console.log("roundStartNode不存在，尝试查找");
            this.roundStartNode = cc.find('Canvas/round_start_node');
            if (!this.roundStartNode) {
                console.log("未找到现有的回合开始节点，创建新的");
                // 创建回合开始节点
                this.roundStartNode = this.createRoundStartNode();
            }
            else {
                console.log("找到现有的回合开始节点");
            }
        }
        else {
            console.log("roundStartNode已存在");
        }
        // 初始状态设为隐藏
        if (this.gameStartNode) {
            this.gameStartNode.active = false;
        }
        if (this.roundStartNode) {
            this.roundStartNode.active = false;
            console.log("回合开始节点初始化完成，设为隐藏");
        }
    };
    /**
     * 创建游戏开始节点
     */
    GamePageController.prototype.createGameStartNode = function () {
        var node = new cc.Node('game_start_node');
        var canvas = cc.find('Canvas');
        if (canvas) {
            canvas.addChild(node);
        }
        // 设置节点位置和层级
        node.setPosition(0, 0);
        node.zIndex = 1000;
        // 添加Sprite组件并加载图片
        var sprite = node.addComponent(cc.Sprite);
        cc.resources.load('开始游戏@2x-2', cc.SpriteFrame, function (err, spriteFrame) {
            if (!err && spriteFrame) {
                sprite.spriteFrame = spriteFrame;
            }
            else {
                console.warn("无法加载游戏开始图片资源");
            }
        });
        return node;
    };
    /**
     * 创建回合开始节点
     */
    GamePageController.prototype.createRoundStartNode = function () {
        console.log("开始创建回合开始节点");
        var node = new cc.Node('round_start_node');
        var canvas = cc.find('Canvas');
        if (canvas) {
            canvas.addChild(node);
            console.log("回合开始节点已添加到Canvas");
        }
        else {
            console.error("找不到Canvas节点");
        }
        // 设置节点位置和层级
        node.setPosition(-750, -1); // 修改初始位置为-750
        node.zIndex = 1000;
        node.width = 200; // 设置节点宽度
        node.height = 100; // 设置节点高度
        // 添加Sprite组件并加载图片
        var sprite = node.addComponent(cc.Sprite);
        // 设置Sprite的SizeMode为CUSTOM，这样可以控制显示大小
        sprite.sizeMode = cc.Sprite.SizeMode.CUSTOM;
        cc.resources.load('huihe@2x-2', cc.SpriteFrame, function (err, spriteFrame) {
            if (!err && spriteFrame) {
                sprite.spriteFrame = spriteFrame;
                // 设置节点大小为图片的原始大小
                node.width = spriteFrame.getRect().width;
                node.height = spriteFrame.getRect().height;
                console.log("回合开始图片资源加载成功，大小:", node.width, "x", node.height);
            }
            else {
                console.warn("无法加载回合开始图片资源:", err);
                // 如果图片加载失败，创建一个简单的文本标签作为替代
                var label = node.addComponent(cc.Label);
                label.string = "回合开始";
                label.fontSize = 48;
                label.node.color = cc.Color.WHITE;
                console.log("使用文本标签作为回合开始节点");
            }
        });
        console.log("回合开始节点创建完成:", node);
        return node;
    };
    /**
     * 显示游戏开始节点动画（放大展示）
     */
    GamePageController.prototype.showGameStartAnimation = function () {
        if (!this.gameStartNode) {
            console.warn("游戏开始节点不存在");
            return;
        }
        // 初始化节点状态
        this.gameStartNode.active = true;
        this.gameStartNode.scale = 0;
        this.gameStartNode.opacity = 255;
        // 放大展示动画
        cc.tween(this.gameStartNode)
            .to(0.3, { scale: 1.2 }, { easing: 'backOut' })
            .to(0.2, { scale: 1.0 }, { easing: 'backOut' })
            .start();
    };
    /**
     * 隐藏游戏开始节点动画（缩小隐藏）
     */
    GamePageController.prototype.hideGameStartAnimation = function () {
        var _this = this;
        if (!this.gameStartNode || !this.gameStartNode.active) {
            return;
        }
        // 缩小隐藏动画
        cc.tween(this.gameStartNode)
            .to(0.3, { scale: 0, opacity: 0 }, { easing: 'backIn' })
            .call(function () {
            _this.gameStartNode.active = false;
        })
            .start();
    };
    /**
     * 显示回合开始节点动画（从左边移入到中间）
     */
    GamePageController.prototype.showRoundStartAnimation = function () {
        var _this = this;
        console.log("showRoundStartAnimation 被调用");
        if (!this.roundStartNode) {
            console.warn("回合开始节点不存在，尝试重新初始化");
            this.initializeAnimationNodes();
            if (!this.roundStartNode) {
                console.error("重新初始化后回合开始节点仍然不存在");
                return;
            }
        }
        console.log("回合开始节点存在，开始动画", this.roundStartNode);
        // 初始化节点状态：在(-750, -1)位置
        this.roundStartNode.active = true;
        this.roundStartNode.setPosition(-750, -1);
        this.roundStartNode.opacity = 255;
        this.roundStartNode.scale = 1;
        console.log("节点初始化完成，位置:", this.roundStartNode.position, "可见性:", this.roundStartNode.active);
        // 0.3秒移动到(0, -1)，0.4秒展示，0.3秒移动到(750, -1)，然后恢复到(-750, -1)
        cc.tween(this.roundStartNode)
            .to(0.3, { x: 0 }, { easing: 'quartOut' })
            .delay(0.4)
            .to(0.3, { x: 750 }, { easing: 'quartIn' })
            .call(function () {
            console.log("回合开始动画完成");
            // 动画完成后，恢复到初始位置(-750, -1)为下一次回合开始做准备
            _this.roundStartNode.setPosition(-750, -1);
            _this.roundStartNode.active = false;
        })
            .start();
    };
    /**
     * 测试方法：手动触发回合开始动画
     */
    GamePageController.prototype.testRoundStartAnimation = function () {
        console.log("手动测试回合开始动画");
        this.showRoundStartAnimation();
    };
    GamePageController.prototype.onDestroy = function () {
        // 移除棋盘点击事件监听
        if (this.chessBoardController) {
            this.chessBoardController.node.off('chess-board-click', this.onChessBoardClick, this);
        }
    };
    __decorate([
        property(cc.Node)
    ], GamePageController.prototype, "boardBtnBack", void 0);
    __decorate([
        property(cc.Label)
    ], GamePageController.prototype, "timeLabel", void 0);
    __decorate([
        property(cc.Label)
    ], GamePageController.prototype, "mineCountLabel", void 0);
    __decorate([
        property(cc.Node)
    ], GamePageController.prototype, "squareMapNode", void 0);
    __decorate([
        property(cc.Node)
    ], GamePageController.prototype, "hexMapNode", void 0);
    __decorate([
        property(LeaveDialogController_1.default)
    ], GamePageController.prototype, "leaveDialogController", void 0);
    __decorate([
        property(CongratsDialogController_1.default)
    ], GamePageController.prototype, "congratsDialogController", void 0);
    __decorate([
        property(GameScoreController_1.default)
    ], GamePageController.prototype, "gameScoreController", void 0);
    __decorate([
        property(ChessBoardController_1.default)
    ], GamePageController.prototype, "chessBoardController", void 0);
    __decorate([
        property(HexChessBoardController_1.default)
    ], GamePageController.prototype, "hexChessBoardController", void 0);
    __decorate([
        property(cc.Node)
    ], GamePageController.prototype, "gameStartNode", void 0);
    __decorate([
        property(cc.Node)
    ], GamePageController.prototype, "roundStartNode", void 0);
    GamePageController = __decorate([
        ccclass
    ], GamePageController);
    return GamePageController;
}(cc.Component));
exports.default = GamePageController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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