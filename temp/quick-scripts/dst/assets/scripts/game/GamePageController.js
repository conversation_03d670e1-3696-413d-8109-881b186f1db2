
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/game/GamePageController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'ae7d7j8qCJHEr/tVZKmu8hm', 'GamePageController');
// scripts/game/GamePageController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var GlobalBean_1 = require("../bean/GlobalBean");
var LeaveDialogController_1 = require("../hall/LeaveDialogController");
var AudioManager_1 = require("../util/AudioManager");
var Config_1 = require("../util/Config");
var Tools_1 = require("../util/Tools");
var CongratsDialogController_1 = require("./CongratsDialogController");
var GameScoreController_1 = require("./GameScoreController");
var ChessBoardController_1 = require("./Chess/ChessBoardController");
var HexChessBoardController_1 = require("./Chess/HexChessBoardController");
var PlayerGameController_1 = require("../pfb/PlayerGameController ");
var WebSocketManager_1 = require("../net/WebSocketManager");
var MessageId_1 = require("../net/MessageId");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var GamePageController = /** @class */ (function (_super) {
    __extends(GamePageController, _super);
    function GamePageController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.boardBtnBack = null; //返回按钮
        _this.timeLabel = null; // 计时器显示标签
        _this.mineCountLabel = null; // 炸弹数量显示标签
        _this.squareMapNode = null; // 方形地图节点 (mapType = 0)
        _this.hexMapNode = null; // 六边形地图节点 (mapType = 1)
        _this.leaveDialogController = null; // 退出游戏弹窗
        _this.congratsDialogController = null; //结算弹窗
        _this.gameScoreController = null; //分数控制器
        _this.chessBoardController = null; //方形棋盘控制器
        _this.hexChessBoardController = null; //六边形棋盘控制器
        _this.gameStartNode = null; // 游戏开始节点
        _this.roundStartNode = null; // 回合开始节点
        _this.isLeaveGameDialogShow = false; //是否显示退出游戏的弹窗
        _this.isCongratsDialog = false; //是否显示结算的弹窗
        // 计时器相关属性
        _this.countdownInterval = null; // 倒计时定时器ID
        _this.currentCountdown = 0; // 当前倒计时秒数
        _this.currentRoundNumber = 0; // 当前回合编号
        // 游戏状态管理
        _this.canOperate = false; // 是否可以操作（在NoticeRoundStart和NoticeActionDisplay之间）
        _this.gameStatus = 0; // 游戏状态
        _this.hasOperatedThisRound = false; // 本回合是否已经操作过
        // 游戏数据
        _this.currentMapType = 0; // 当前地图类型 0-方形地图，1-六边形地图
        _this.currentMineCount = 0; // 当前炸弹数量
        // 当前NoticeActionDisplay数据，用于倒计时显示逻辑
        _this.currentNoticeActionData = null;
        return _this;
        // update (dt) {}
    }
    GamePageController.prototype.onLoad = function () {
        var _this = this;
        console.log("=== GamePageController onLoad 开始 ===");
        // 如果timeLabel没有在编辑器中设置，尝试通过路径查找
        if (!this.timeLabel) {
            // 根据场景结构查找time_label节点
            var timeBgNode = cc.find('Canvas/time_bg');
            if (timeBgNode) {
                var timeLabelNode = timeBgNode.getChildByName('time_label');
                if (timeLabelNode) {
                    this.timeLabel = timeLabelNode.getComponent(cc.Label);
                }
            }
        }
        // 如果mineCountLabel没有在编辑器中设置，尝试通过路径查找
        if (!this.mineCountLabel) {
            // 根据场景结构查找mine_count_label节点
            var mineCountBgNode = cc.find('Canvas/mine_count_bg');
            if (mineCountBgNode) {
                var mineCountLabelNode = mineCountBgNode.getChildByName('mine_count_label');
                if (mineCountLabelNode) {
                    this.mineCountLabel = mineCountLabelNode.getComponent(cc.Label);
                }
            }
        }
        // 将测试方法暴露到全局，方便调试
        window.testGameReset = function () {
            _this.testReset();
        };
        // 暴露 GamePageController 实例到全局
        window.gamePageController = this;
        // 暴露测试方法到全局，方便在控制台直接调用
        window.testRoundAnimation = function () {
            console.log("全局测试方法被调用");
            _this.testRoundStartAnimation();
        };
        // 初始化游戏开始和回合开始节点
        this.initializeAnimationNodes();
        console.log("=== GamePageController onLoad 完成 ===");
    };
    GamePageController.prototype.start = function () {
        var _this = this;
        Tools_1.Tools.imageButtonClick(this.boardBtnBack, Config_1.Config.buttonRes + 'side_btn_back_normal', Config_1.Config.buttonRes + 'side_btn_back_pressed', function () {
            _this.isLeaveGameDialogShow = true;
            _this.leaveDialogController.show(1, function () {
                _this.isLeaveGameDialogShow = false;
            });
        });
        // 监听棋盘点击事件
        if (this.chessBoardController) {
            this.chessBoardController.node.on('chess-board-click', this.onChessBoardClick, this);
        }
        // 监听六边形棋盘点击事件
        if (this.hexChessBoardController) {
            this.hexChessBoardController.node.on('hex-chess-board-click', this.onHexChessBoardClick, this);
        }
    };
    /**
     * 处理棋盘点击事件
     * @param event 事件数据 {x: number, y: number, action: number}
     */
    GamePageController.prototype.onChessBoardClick = function (event) {
        var _a = event.detail || event, x = _a.x, y = _a.y, action = _a.action;
        // 检查是否可以操作（在操作时间内）
        if (!this.isCanOperate()) {
            return;
        }
        // 检查本回合是否已经操作过
        if (this.hasOperatedThisRound) {
            return;
        }
        // 发送点击操作
        this.sendClickBlock(x, y, action);
        // 操作有效，通知棋盘生成预制体
        if (this.chessBoardController) {
            if (action === 1) {
                // 挖掘操作，生成不带旗子的预制体
                this.chessBoardController.placePlayerOnGrid(x, y, false);
            }
            else if (action === 2) {
                // 标记操作，生成带旗子的预制体
                this.chessBoardController.placePlayerOnGrid(x, y, true);
            }
        }
        // 标记本回合已经操作过，禁止后续交互
        this.hasOperatedThisRound = true;
    };
    /**
     * 处理六边形棋盘点击事件
     * @param event 事件数据 {q: number, r: number, action: number}
     */
    GamePageController.prototype.onHexChessBoardClick = function (event) {
        var _a = event.detail || event, q = _a.q, r = _a.r, action = _a.action;
        // 检查是否可以操作（在操作时间内）
        if (!this.isCanOperate()) {
            return;
        }
        // 检查本回合是否已经操作过
        if (this.hasOperatedThisRound) {
            return;
        }
        // 发送六边形点击操作（需要将六边形坐标转换为服务器期望的格式）
        this.sendHexClickBlock(q, r, action);
        // 操作有效，通知六边形棋盘生成预制体
        if (this.hexChessBoardController) {
            if (action === 1) {
                // 挖掘操作，生成不带旗子的预制体
                this.hexChessBoardController.placePlayerOnHexGrid(q, r, false);
            }
            else if (action === 2) {
                // 标记操作，生成带旗子的预制体
                this.hexChessBoardController.placePlayerOnHexGrid(q, r, true);
            }
        }
        // 标记本回合已经操作过，禁止后续交互
        this.hasOperatedThisRound = true;
    };
    //结算
    GamePageController.prototype.setCongratsDialog = function (noticeSettlement) {
        var _this = this;
        this.setCongrats(noticeSettlement);
        //退出弹窗正在显示的话  就先关闭
        if (this.isLeaveGameDialogShow) {
            this.leaveDialogController.hide();
        }
        this.isCongratsDialog = true;
        //弹出结算弹窗
        this.congratsDialogController.show(noticeSettlement, function () {
            _this.isCongratsDialog = false;
        });
    };
    GamePageController.prototype.onDisable = function () {
        //退出弹窗正在显示的话  就先关闭
        if (this.isLeaveGameDialogShow) {
            this.leaveDialogController.hide();
        }
        //结算弹窗正在显示的话就先关闭掉
        if (this.isCongratsDialog) {
            this.congratsDialogController.hide();
        }
        // 清理计时器
        this.clearCountdownTimer();
    };
    //结算
    GamePageController.prototype.setCongrats = function (noticeSettlement) {
        // 获取用户列表，优先使用 finalRanking，其次使用 users
        var userList = noticeSettlement.finalRanking || noticeSettlement.users;
        // 检查用户列表是否存在
        if (!noticeSettlement || !userList || !Array.isArray(userList)) {
            console.warn('NoticeSettlement 用户数据无效:', noticeSettlement);
            AudioManager_1.AudioManager.winAudio(); // 默认播放胜利音效
            return;
        }
        var currentUserId = GlobalBean_1.GlobalBean.GetInstance().loginData.userInfo.userId;
        var index = userList.findIndex(function (item) { return item.userId === currentUserId; }); //搜索
        if (index >= 0) { //自己参与的话 才会显示正常的胜利和失败的音效，自己不参与的话 就全部显示胜利的音效
            if (userList[index].rank === 1) { //判断自己是不是第一名
                AudioManager_1.AudioManager.winAudio();
            }
            else {
                AudioManager_1.AudioManager.loseAudio();
            }
        }
        else {
            AudioManager_1.AudioManager.winAudio();
        }
    };
    // 处理游戏开始通知，获取炸弹数量和地图类型
    GamePageController.prototype.onGameStart = function (data) {
        // 保存地图类型
        this.currentMapType = data.mapType || 0;
        // 根据地图类型重置对应的棋盘控制器
        if (this.currentMapType === 0) {
            // 方形地图
            if (this.chessBoardController) {
                this.chessBoardController.resetGameScene();
            }
            else {
                console.error("❌ chessBoardController 不存在！");
            }
        }
        else if (this.currentMapType === 1) {
            // 六边形地图
            if (this.hexChessBoardController) {
                this.hexChessBoardController.resetGameScene();
                // 忽略服务器的 validHexCoords，使用前端节点坐标
                this.hexChessBoardController.setValidHexCoords([]); // 传入空数组，会被忽略
            }
            else {
                console.error("❌ hexChessBoardController 不存在！");
            }
        }
        // 重置游戏状态
        this.canOperate = false;
        this.hasOperatedThisRound = false;
        this.currentRoundNumber = 0;
        this.currentCountdown = 0;
        this.gameStatus = 0;
        // 根据地图类型获取炸弹数量
        if (data.mapType === 0 && data.mapConfig) {
            // 方形地图
            this.currentMineCount = data.mapConfig.mineCount || 13;
        }
        else if (data.mapType === 1) {
            // 六边形地图，根据前端节点数量计算炸弹数量
            if (this.hexChessBoardController) {
                this.currentMineCount = this.hexChessBoardController.getRecommendedMineCount();
            }
            else {
                this.currentMineCount = 15; // 备用固定值
            }
        }
        else {
            // 默认值
            this.currentMineCount = 13;
        }
        // 更新炸弹数UI
        this.updateMineCountDisplay(this.currentMineCount);
        // 根据地图类型控制地图节点的显示与隐藏
        this.switchMapDisplay(this.currentMapType);
        // 初始化分数界面（使用后端传回来的真实数据）
        if (this.gameScoreController) {
            this.gameScoreController.initializeScoreView();
        }
        // 显示游戏开始节点动画
        this.showGameStartAnimation();
    };
    /**
     * 测试重置功能（可以在浏览器控制台手动调用）
     */
    GamePageController.prototype.testReset = function () {
        if (this.chessBoardController) {
            this.chessBoardController.resetGameScene();
        }
        else {
            console.error("❌ chessBoardController 不存在！");
        }
    };
    // 处理扫雷回合开始通知
    GamePageController.prototype.onNoticeRoundStart = function (data) {
        this.currentRoundNumber = data.roundNumber || 1;
        this.currentCountdown = data.countDown || 25;
        this.gameStatus = data.gameStatus || 0;
        // 隐藏游戏开始节点
        this.hideGameStartAnimation();
        // 新回合开始，重置操作状态
        this.canOperate = true;
        this.hasOperatedThisRound = false;
        // 清理棋盘上的所有玩家预制体
        if (this.currentMapType === 0 && this.chessBoardController) {
            // 方形地图
            this.chessBoardController.clearAllPlayerNodes();
        }
        else if (this.currentMapType === 1 && this.hexChessBoardController) {
            // 六边形地图
            this.hexChessBoardController.clearAllPlayerNodes();
        }
        // 开始倒计时
        this.startCountdown(this.currentCountdown);
    };
    // 处理扫雷操作展示通知
    GamePageController.prototype.onNoticeActionDisplay = function (data) {
        // 保存当前NoticeActionDisplay数据，用于倒计时显示逻辑
        this.currentNoticeActionData = data;
        // 进入展示阶段，不能再操作
        this.canOperate = false;
        this.gameStatus = data.gameStatus || 0;
        // 根据countDown重置倒计时为5秒
        this.currentCountdown = data.countDown || 5;
        this.updateCountdownDisplay(this.currentCountdown);
        this.startCountdown(this.currentCountdown);
        // 更新剩余炸弹数量显示
        if (data.remainingMines !== undefined) {
            this.updateMineCountDisplay(data.remainingMines);
        }
        // 在棋盘上显示所有玩家的操作（头像）
        this.displayPlayerActions(data.playerActions, data.playerTotalScores);
        // 立即显示先手+1（如果先手不是我）
        this.showFirstChoiceBonusImmediately(data.playerActions);
    };
    /**
     * 立即显示先手+1奖励（只为其他人显示，如果我是先手则不显示）
     * @param playerActions 玩家操作列表
     */
    GamePageController.prototype.showFirstChoiceBonusImmediately = function (playerActions) {
        var _a, _b;
        // 获取当前用户ID
        var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
        if (!currentUserId) {
            console.warn("无法获取当前用户ID");
            return;
        }
        // 查找先手玩家
        var firstChoicePlayer = playerActions.find(function (action) { return action.isFirstChoice; });
        // 如果先手玩家存在且不是我，才显示+1
        if (firstChoicePlayer && firstChoicePlayer.userId !== currentUserId) {
            var firstChoiceUserIndex = this.findUserIndex(firstChoicePlayer.userId);
            if (firstChoiceUserIndex !== -1) {
                // 立即显示先手+1
                this.showScoreInScorePanel(firstChoiceUserIndex, 1);
                // 同时在player_game_pfb显示先手+1
                this.showScoreOnPlayerAvatar(firstChoicePlayer.userId, 1);
            }
        }
    };
    /**
     * 延迟更新棋盘的回调方法
     * @param data NoticeActionDisplay数据
     */
    GamePageController.prototype.delayedUpdateBoard = function (data) {
        this.updateBoardAfterActions(data);
    };
    /**
     * 更新棋盘（删除格子、生成预制体、连锁动画）
     * @param data NoticeActionDisplay数据
     */
    GamePageController.prototype.updateBoardAfterActions = function (data) {
        // 注意：分数动画和头像删除现在由倒计时逻辑控制，这里直接处理格子隐藏和数字生成
        var _this = this;
        // 立即处理每个玩家的操作结果
        // 先按位置分组，处理同一位置有多个操作的情况
        var processedPositions = new Set();
        data.playerActions.forEach(function (action) {
            var positionKey = action.x + "," + action.y;
            // 如果这个位置已经处理过，跳过
            if (processedPositions.has(positionKey)) {
                return;
            }
            // 查找同一位置的所有操作
            var samePositionActions = data.playerActions.filter(function (a) {
                return a.x === action.x && a.y === action.y;
            });
            // 处理同一位置的操作结果（格子隐藏和数字生成）
            _this.processPositionResult(action.x, action.y, samePositionActions);
            // 标记这个位置已处理
            processedPositions.add(positionKey);
        });
        // 处理连锁展开结果
        if (data.floodFillResults && data.floodFillResults.length > 0) {
            data.floodFillResults.forEach(function (floodFill) {
                _this.processFloodFillResult(floodFill);
            });
        }
    };
    /**
     * 让所有头像消失（支持方形地图和六边形地图）
     * @param playerActions 玩家操作列表
     * @param onComplete 完成回调
     */
    GamePageController.prototype.hideAllAvatars = function (playerActions, onComplete) {
        // 根据地图类型调用对应的控制器
        if (this.currentMapType === 0 && this.chessBoardController) {
            // 方形地图：直接调用一次头像删除，不区分位置
            this.chessBoardController.hideAvatarsAtPosition(0, 0, function () {
                onComplete();
            });
        }
        else if (this.currentMapType === 1 && this.hexChessBoardController) {
            // 六边形地图：直接调用方法（已经编译成功）
            this.hexChessBoardController.hideAllHexAvatars(function () {
                onComplete();
            });
        }
        else {
            // 没有可用的控制器，直接执行回调
            console.warn("没有可用的棋盘控制器，跳过头像消失动画");
            onComplete();
        }
    };
    /**
     * 处理同一位置的多个操作结果
     * @param x 格子x坐标（方形地图）或q坐标（六边形地图）
     * @param y 格子y坐标（方形地图）或r坐标（六边形地图）
     * @param actions 该位置的所有操作
     */
    GamePageController.prototype.processPositionResult = function (x, y, actions) {
        var _a, _b;
        // 根据地图类型删除该位置的格子（立即隐藏，不播放动画）
        if (this.currentMapType === 0) {
            // 方形地图
            this.chessBoardController.removeGridAt(x, y, true);
        }
        else if (this.currentMapType === 1) {
            // 六边形地图，x实际是q坐标，y实际是r坐标
            if (this.hexChessBoardController) {
                this.hexChessBoardController.hideHexGridAt(x, y, true);
            }
        }
        // 检查是否有地雷被点击（action=1且result="mine"）
        var mineClickAction = actions.find(function (action) {
            return action.action === 1 && action.result === "mine";
        });
        if (mineClickAction) {
            // 如果有地雷被点击，直接显示炸弹，不管是否有标记
            // 判断是否是当前用户点到的雷
            var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
            var isCurrentUser = mineClickAction.userId === currentUserId;
            // 根据地图类型调用对应的方法
            if (this.currentMapType === 0) {
                // 方形地图
                this.chessBoardController.createBoomPrefab(x, y, isCurrentUser);
            }
            else if (this.currentMapType === 1) {
                // 六边形地图，x实际是q坐标，y实际是r坐标
                if (this.hexChessBoardController) {
                    this.hexChessBoardController.createHexBoomPrefab(x, y, isCurrentUser);
                }
            }
            return;
        }
        // 如果没有地雷被点击，按原逻辑处理第一个操作的结果
        var firstAction = actions[0];
        var result = firstAction.result;
        if (result === "correct_mark") {
            // 正确标记：生成biaoji预制体
            if (this.currentMapType === 0) {
                // 方形地图
                this.chessBoardController.createBiaojiPrefab(x, y);
            }
            else if (this.currentMapType === 1) {
                // 六边形地图
                if (this.hexChessBoardController) {
                    this.hexChessBoardController.createHexBiaojiPrefab(x, y);
                }
            }
        }
        else if (typeof result === "number") {
            // 数字：更新neighborMines显示
            if (this.currentMapType === 0) {
                // 方形地图
                this.chessBoardController.updateNeighborMinesDisplay(x, y, result);
            }
            else if (this.currentMapType === 1) {
                // 六边形地图
                if (this.hexChessBoardController) {
                    this.hexChessBoardController.updateHexNeighborMinesDisplay(x, y, result);
                }
            }
        }
    };
    /**
     * 处理单个玩家操作结果（保留原方法以防其他地方调用）
     * @param action 玩家操作数据
     */
    GamePageController.prototype.processPlayerActionResult = function (action) {
        var _a, _b;
        var x = action.x;
        var y = action.y;
        var result = action.result;
        // 删除该位置的格子
        this.chessBoardController.removeGridAt(x, y);
        // 根据结果生成相应的预制体
        if (result === "mine") {
            // 地雷：生成boom预制体
            // 判断是否是当前用户点到的雷
            var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
            var isCurrentUser = action.userId === currentUserId;
            this.chessBoardController.createBoomPrefab(x, y, isCurrentUser);
        }
        else if (result === "correct_mark") {
            // 正确标记：生成biaoji预制体
            this.chessBoardController.createBiaojiPrefab(x, y);
        }
        else if (typeof result === "number") {
            // 数字：更新neighborMines显示
            this.chessBoardController.updateNeighborMinesDisplay(x, y, result);
        }
    };
    /**
     * 处理连锁展开结果
     * @param floodFill 连锁展开数据
     */
    GamePageController.prototype.processFloodFillResult = function (floodFill) {
        var _this = this;
        // 立即处理所有连锁格子，不再有延迟
        floodFill.revealedBlocks.forEach(function (block) {
            if (_this.currentMapType === 0) {
                // 方形地图：立即隐藏格子并显示数字
                _this.chessBoardController.removeGridAt(block.x, block.y, true);
                // 立即显示数字
                if (block.neighborMines > 0) {
                    _this.chessBoardController.updateNeighborMinesDisplay(block.x, block.y, block.neighborMines);
                }
            }
            else if (_this.currentMapType === 1) {
                // 六边形地图，x实际是q坐标，y实际是r坐标
                if (_this.hexChessBoardController) {
                    // 立即隐藏格子
                    _this.hexChessBoardController.hideHexGridAt(block.x, block.y, true);
                    // 立即显示数字（如果有的话）
                    if (block.neighborMines > 0) {
                        _this.hexChessBoardController.updateHexNeighborMinesDisplay(block.x, block.y, block.neighborMines);
                    }
                }
            }
        });
    };
    // 处理扫雷回合结束通知
    GamePageController.prototype.onNoticeRoundEnd = function (data) {
        // 进入回合结束阶段，不能再操作
        this.canOperate = false;
        this.gameStatus = data.gameStatus || 1;
        // 不再处理倒计时，让客户端自然倒计时到0，方便展示54321
        // 处理玩家分数动画和头像显示
        if (data.playerResults && data.playerResults.length > 0) {
            this.displayPlayerScoreAnimations(data.playerResults);
            // 如果本回合我没有操作，根据后端消息生成我的头像
            this.handleMyAvatarIfNotOperated(data.playerResults);
        }
        // 清理棋盘上的所有玩家预制体
        if (this.currentMapType === 0 && this.chessBoardController) {
            // 方形地图
            this.chessBoardController.clearAllPlayerNodes();
        }
        else if (this.currentMapType === 1 && this.hexChessBoardController) {
            // 六边形地图
            this.hexChessBoardController.clearAllPlayers();
        }
    };
    /**
     * 在棋盘上显示所有玩家的操作
     * @param playerActions 玩家操作列表
     * @param playerTotalScores 玩家总分数据
     */
    GamePageController.prototype.displayPlayerActions = function (playerActions, playerTotalScores) {
        var _this = this;
        var _a, _b;
        // 检查是否有可用的棋盘控制器
        var hasSquareBoard = this.chessBoardController && this.currentMapType === 0;
        var hasHexBoard = this.hexChessBoardController && this.currentMapType === 1;
        if ((!hasSquareBoard && !hasHexBoard) || !playerActions || playerActions.length === 0) {
            return;
        }
        // 获取当前用户ID
        var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
        if (!currentUserId) {
            console.warn("无法获取当前用户ID");
            return;
        }
        // 注意：分数动画已经在updateBoardAfterActions的第一步显示了，这里不再重复显示
        // 检查本回合是否进行了操作，如果没有，需要显示自己的头像
        var myAction = playerActions.find(function (action) { return action.userId === currentUserId; });
        var shouldDisplayMyAvatar = false;
        if (!this.hasOperatedThisRound && myAction) {
            shouldDisplayMyAvatar = true;
            // 生成我的头像
            var withFlag = (myAction.action === 2); // action=2表示标记操作，显示旗子
            if (this.currentMapType === 0) {
                // 方形地图
                this.chessBoardController.placePlayerOnGrid(myAction.x, myAction.y, withFlag);
            }
            else if (this.currentMapType === 1) {
                // 六边形地图，x实际是q坐标，y实际是r坐标
                if (this.hexChessBoardController) {
                    this.hexChessBoardController.placePlayerOnHexGrid(myAction.x, myAction.y, withFlag);
                }
            }
        }
        // 过滤掉自己的操作，只显示其他玩家的操作
        var otherPlayersActions = playerActions.filter(function (action) { return action.userId !== currentUserId; });
        if (otherPlayersActions.length === 0) {
            return;
        }
        // 按位置分组其他玩家的操作
        var positionGroups = this.groupActionsByPosition(otherPlayersActions);
        // 为每个位置生成预制体
        positionGroups.forEach(function (actions, positionKey) {
            var _a = positionKey.split(',').map(Number), x = _a[0], y = _a[1];
            if (_this.currentMapType === 0) {
                // 方形地图
                _this.chessBoardController.displayOtherPlayersAtPosition(x, y, actions);
            }
            else if (_this.currentMapType === 1) {
                // 六边形地图，x实际是q坐标，y实际是r坐标
                if (_this.hexChessBoardController) {
                    // 直接调用方法（已经编译成功）
                    _this.hexChessBoardController.displayOtherPlayersAtHexPosition(x, y, actions);
                }
            }
        });
    };
    /**
     * 按位置分组玩家操作
     * @param playerActions 玩家操作列表
     * @returns Map<string, PlayerActionDisplay[]> 位置为key，操作列表为value
     */
    GamePageController.prototype.groupActionsByPosition = function (playerActions) {
        var groups = new Map();
        for (var _i = 0, playerActions_1 = playerActions; _i < playerActions_1.length; _i++) {
            var action = playerActions_1[_i];
            var positionKey = action.x + "," + action.y;
            if (!groups.has(positionKey)) {
                groups.set(positionKey, []);
            }
            groups.get(positionKey).push(action);
        }
        return groups;
    };
    /**
     * 显示玩家分数动画
     * @param playerResults 玩家回合结果列表
     */
    GamePageController.prototype.displayPlayerScoreAnimations = function (playerResults) {
        var _this = this;
        var _a, _b;
        // 获取当前用户ID
        var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
        if (!currentUserId) {
            console.warn("无法获取当前用户ID");
            return;
        }
        // 为每个玩家显示分数动画
        playerResults.forEach(function (result, index) {
            // 延迟显示，让动画错开
            _this.scheduleOnce(function () {
                _this.showPlayerScoreAnimation(result, currentUserId);
            }, index * 0.2);
        });
    };
    /**
     * 显示单个玩家的分数动画
     * @param result 玩家回合结果
     * @param currentUserId 当前用户ID
     */
    GamePageController.prototype.showPlayerScoreAnimation = function (result, currentUserId) {
        var isMyself = result.userId === currentUserId;
        if (isMyself) {
            // 自己的分数动画：在player_game_pfb里只显示本回合得分
            this.showMyScoreAnimation(result);
        }
        else {
            // 其他人的分数动画：根据isFirstChoice决定显示逻辑
            this.showOtherPlayerScoreAnimation(result);
        }
    };
    /**
     * 显示自己的分数动画
     * @param result 玩家回合结果
     */
    GamePageController.prototype.showMyScoreAnimation = function (result) {
        // 在棋盘上的头像预制体中显示本回合得分
        if (this.currentMapType === 0 && this.chessBoardController) {
            // 方形地图
            this.chessBoardController.showScoreOnPlayerNode(result.x, result.y, result.score, false);
        }
        else if (this.currentMapType === 1 && this.hexChessBoardController) {
            // 六边形地图，x实际是q坐标，y实际是r坐标
            this.hexChessBoardController.showScoreOnHexPlayerNode(result.x, result.y, result.score, false);
        }
        // 在player_score_pfb中显示分数动画
        this.showScoreAnimationInScorePanel(result.userId, result.score, result.isFirstChoice);
    };
    /**
     * 显示其他玩家的分数动画
     * @param result 玩家回合结果
     */
    GamePageController.prototype.showOtherPlayerScoreAnimation = function (result) {
        if (result.isFirstChoice) {
            // 其他人为先手：player_game_pfb里不显示+1，只显示本回合得分
            if (this.currentMapType === 0 && this.chessBoardController) {
                // 方形地图
                this.chessBoardController.showScoreOnPlayerNode(result.x, result.y, result.score, false);
            }
            else if (this.currentMapType === 1 && this.hexChessBoardController) {
                // 六边形地图
                this.hexChessBoardController.showScoreOnHexPlayerNode(result.x, result.y, result.score, false);
            }
            // 在player_score_pfb里先显示+1，再显示本回合得分，然后更新总分
            this.showFirstChoiceScoreAnimation(result.userId, result.score);
        }
        else {
            // 其他人非先手：正常显示本回合得分
            if (this.currentMapType === 0 && this.chessBoardController) {
                // 方形地图
                this.chessBoardController.showScoreOnPlayerNode(result.x, result.y, result.score, false);
            }
            else if (this.currentMapType === 1 && this.hexChessBoardController) {
                // 六边形地图
                this.hexChessBoardController.showScoreOnHexPlayerNode(result.x, result.y, result.score, false);
            }
            // 在player_score_pfb中显示分数动画
            this.showScoreAnimationInScorePanel(result.userId, result.score, false);
        }
    };
    /**
     * 在分数面板中显示分数动画
     * @param userId 用户ID
     * @param score 本回合得分
     * @param isFirstChoice 是否为先手
     */
    GamePageController.prototype.showScoreAnimationInScorePanel = function (userId, score, isFirstChoice) {
        // 这里需要找到对应的PlayerScoreController并调用分数动画
        // 由于没有直接的引用，这里先用日志记录
        // TODO: 实现在player_score_pfb中显示分数动画的逻辑
        // 需要找到对应用户的PlayerScoreController实例并调用showAddScore方法
    };
    /**
     * 显示先手玩家的分数动画（先显示+1，再显示本回合得分）
     * @param userId 用户ID
     * @param score 本回合得分
     */
    GamePageController.prototype.showFirstChoiceScoreAnimation = function (userId, score) {
        var _this = this;
        // 先显示+1的先手奖励
        this.scheduleOnce(function () {
            _this.showScoreAnimationInScorePanel(userId, 1, true);
        }, 0.1);
        // 再显示本回合得分
        this.scheduleOnce(function () {
            _this.showScoreAnimationInScorePanel(userId, score, false);
        }, 1.2);
        // 最后更新总分
        this.scheduleOnce(function () {
            _this.updatePlayerTotalScore(userId, score + 1);
        }, 2.4);
    };
    /**
     * 更新玩家总分
     * @param userId 用户ID
     * @param totalScore 新的总分
     */
    GamePageController.prototype.updatePlayerTotalScore = function (userId, totalScore) {
        // TODO: 实现更新玩家总分的逻辑
        // 需要更新GlobalBean中的用户数据，并刷新UI显示
    };
    /**
     * 如果本回合我没有操作，根据后端消息生成我的头像
     * @param playerResults 玩家回合结果列表
     */
    GamePageController.prototype.handleMyAvatarIfNotOperated = function (playerResults) {
        var _a, _b;
        // 获取当前用户ID
        var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
        if (!currentUserId) {
            console.warn("无法获取当前用户ID");
            return;
        }
        // 检查本回合是否进行了操作
        if (this.hasOperatedThisRound) {
            return;
        }
        // 查找我的操作结果
        var myResult = playerResults.find(function (result) { return result.userId === currentUserId; });
        if (!myResult) {
            return;
        }
        // 根据后端消息生成我的头像
        if (this.chessBoardController) {
            // 根据操作类型决定是否显示旗子
            var withFlag = (myResult.action === 2); // action=2表示标记操作，显示旗子
            // 生成我的头像预制体
            this.chessBoardController.placePlayerOnGrid(myResult.x, myResult.y, withFlag);
        }
    };
    // 发送点击方块消息
    GamePageController.prototype.sendClickBlock = function (x, y, action) {
        if (!this.canOperate) {
            return;
        }
        // 检查本回合是否已经操作过
        if (this.hasOperatedThisRound) {
            return;
        }
        var clickData = {
            x: x,
            y: y,
            action: action // 1=挖掘方块，2=标记/取消标记地雷
        };
        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeClickBlock, clickData);
        // 标记本回合已经操作过，防止重复操作
        this.hasOperatedThisRound = true;
    };
    // 发送六边形点击方块消息
    GamePageController.prototype.sendHexClickBlock = function (q, r, action) {
        if (!this.canOperate) {
            return;
        }
        // 检查本回合是否已经操作过
        if (this.hasOperatedThisRound) {
            return;
        }
        // 根据当前地图类型决定发送格式
        if (this.currentMapType === 1) {
            // 六边形地图：使用六边形坐标格式
            var hexClickData = {
                q: q,
                r: r,
                action: action // 1=挖掘方块，2=标记/取消标记地雷
            };
            // 注意：这里仍然使用 MsgTypeClickBlock，但数据格式不同
            // 后端应该根据当前房间的 mapType 来解析不同的坐标格式
            WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeClickBlock, hexClickData);
        }
        else {
            // 方形地图：转换为x,y坐标（备用方案）
            var clickData = {
                x: q,
                y: r,
                action: action
            };
            WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeClickBlock, clickData);
        }
        // 标记本回合已经操作过，防止重复操作
        this.hasOperatedThisRound = true;
    };
    // 检查是否可以操作
    GamePageController.prototype.isCanOperate = function () {
        return this.canOperate && !this.hasOperatedThisRound;
    };
    /**
     * 处理首选玩家奖励通知
     * @param data NoticeFirstChoiceBonus 消息数据
     */
    GamePageController.prototype.onNoticeFirstChoiceBonus = function (data) {
        var _a, _b;
        // 转发给GameScoreController处理所有玩家的分数更新和加分动画
        if (this.gameScoreController) {
            this.gameScoreController.onNoticeFirstChoiceBonus(data);
        }
        // 判断是否为当前用户，如果是则同时更新player_game_pfb中的change_score
        var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
        var isMyself = (data.userId === currentUserId);
        if (isMyself) {
            // 更新player_game_pfb中的change_score显示
            this.updatePlayerGameScore(data.userId, data.bonusScore);
        }
    };
    /**
     * 更新player_game_pfb中的change_score显示
     * @param userId 用户ID
     * @param bonusScore 奖励分数
     */
    GamePageController.prototype.updatePlayerGameScore = function (userId, bonusScore) {
        // 根据地图类型调用对应的控制器显示加分效果
        if (this.currentMapType === 0 && this.chessBoardController) {
            // 方形地图
            this.chessBoardController.showPlayerGameScore(userId, bonusScore);
        }
        else if (this.currentMapType === 1 && this.hexChessBoardController) {
            // 六边形地图
            this.hexChessBoardController.showHexPlayerGameScore(userId, bonusScore);
        }
        else {
            console.warn("\u5730\u56FE\u7C7B\u578B" + this.currentMapType + "\u7684\u68CB\u76D8\u63A7\u5236\u5668\u672A\u8BBE\u7F6E\uFF0C\u65E0\u6CD5\u663E\u793Aplayer_game_pfb\u52A0\u5206\u6548\u679C");
        }
    };
    // 获取当前地图类型
    GamePageController.prototype.getCurrentMapType = function () {
        return this.currentMapType;
    };
    // 获取当前炸弹数量
    GamePageController.prototype.getCurrentMineCount = function () {
        return this.currentMineCount;
    };
    // 获取当前回合操作状态（用于调试）
    GamePageController.prototype.getCurrentRoundStatus = function () {
        return {
            roundNumber: this.currentRoundNumber,
            canOperate: this.canOperate,
            hasOperated: this.hasOperatedThisRound
        };
    };
    // 开始倒计时
    GamePageController.prototype.startCountdown = function (seconds) {
        var _this = this;
        // 清除之前的计时器
        this.clearCountdownTimer();
        var remainingSeconds = seconds;
        this.updateCountdownDisplay(remainingSeconds);
        this.countdownInterval = setInterval(function () {
            remainingSeconds--;
            _this.updateCountdownDisplay(remainingSeconds);
            // 在NoticeActionDisplay阶段，根据倒计时执行不同的显示逻辑
            if (_this.gameStatus === 0 && _this.currentNoticeActionData) {
                _this.handleCountdownBasedDisplay(remainingSeconds);
            }
            if (remainingSeconds <= 0) {
                _this.clearCountdownTimer();
            }
        }, 1000);
    };
    // 更新倒计时显示
    GamePageController.prototype.updateCountdownDisplay = function (seconds) {
        if (this.timeLabel) {
            this.timeLabel.string = seconds + "s"; // 显示数字加s：5s, 4s, 3s, 2s, 1s, 0s
        }
        this.currentCountdown = seconds;
    };
    /**
     * 根据倒计时处理不同时机的显示逻辑
     * @param remainingSeconds 剩余秒数
     */
    GamePageController.prototype.handleCountdownBasedDisplay = function (remainingSeconds) {
        if (!this.currentNoticeActionData) {
            return;
        }
        var data = this.currentNoticeActionData;
        if (remainingSeconds === 4) {
            // 4s时：同时展示本回合加减分
            this.showCurrentRoundScores(data.playerActions, data.playerTotalScores);
        }
        else if (remainingSeconds === 3) {
            // 3s时：隐藏加减分并删除头像预制体
            this.hideScoreEffectsAndAvatars(data.playerActions);
            // 3s时：立即执行格子隐藏和生成数字预制体等操作
            this.updateBoardAfterActions(data);
        }
        else if (remainingSeconds === 2) {
            // 2s时：显示回合开始节点动画
            console.log("倒计时剩余2秒，准备显示回合开始动画");
            this.showRoundStartAnimation();
        }
        else if (remainingSeconds === 1) {
            // 1s时：清空数据，避免重复执行
            this.currentNoticeActionData = null;
        }
    };
    /**
     * 显示本回合所有玩家的加减分
     * @param playerActions 玩家操作列表
     * @param playerTotalScores 玩家总分数据
     */
    GamePageController.prototype.showCurrentRoundScores = function (playerActions, playerTotalScores) {
        var _this = this;
        var _a, _b;
        // 获取当前用户ID
        var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
        if (!currentUserId) {
            console.warn("无法获取当前用户ID");
            return;
        }
        // 为每个玩家显示本回合的加减分
        playerActions.forEach(function (action) {
            // 在player_game_pfb中显示本回合的加减分
            _this.showScoreOnPlayerAvatar(action.userId, action.score);
            // 在分数面板显示本回合的加减分
            var userIndex = _this.findUserIndex(action.userId);
            if (userIndex !== -1) {
                _this.showScoreInScorePanel(userIndex, action.score);
            }
        });
        // 延迟更新总分，让加减分动画先显示
        this.scheduleOnce(function () {
            // 更新所有玩家的总分
            playerActions.forEach(function (action) {
                var totalScore = playerTotalScores[action.userId] || 0;
                if (_this.gameScoreController) {
                    _this.gameScoreController.updatePlayerScore(action.userId, totalScore);
                    // 更新全局数据中的总分
                    _this.updatePlayerTotalScoreInGlobalData(action.userId, totalScore);
                }
            });
        }, 1.2);
    };
    /**
     * 隐藏加减分效果并删除头像预制体
     * @param playerActions 玩家操作列表
     */
    GamePageController.prototype.hideScoreEffectsAndAvatars = function (playerActions) {
        // 隐藏所有加减分效果
        this.hideAllScoreEffects();
        // 删除头像预制体（不等待完成回调）
        this.hideAllAvatars(playerActions, function () {
            console.log("所有头像已隐藏完成");
        });
    };
    /**
     * 隐藏所有加减分效果
     */
    GamePageController.prototype.hideAllScoreEffects = function () {
        // 隐藏分数面板的加减分效果
        // 注意：这里暂时不处理分数面板的隐藏，因为PlayerScoreController的hideScoreEffects会在1秒后自动隐藏
        // 隐藏棋盘上所有头像的加减分效果
        this.hideAllPlayerGameScoreEffects();
    };
    /**
     * 隐藏棋盘上所有头像的加减分效果
     */
    GamePageController.prototype.hideAllPlayerGameScoreEffects = function () {
        // 遍历棋盘上的所有PlayerGameController，调用hideScoreEffects方法
        if (this.currentMapType === 0 && this.chessBoardController && this.chessBoardController.boardNode) {
            // 方形地图
            var children = this.chessBoardController.boardNode.children;
            for (var i = 0; i < children.length; i++) {
                var child = children[i];
                var playerController = child.getComponent(PlayerGameController_1.default);
                if (playerController) {
                    playerController.hideScoreEffects();
                }
            }
        }
        else if (this.currentMapType === 1 && this.hexChessBoardController && this.hexChessBoardController.boardNode) {
            // 六边形地图
            var children = this.hexChessBoardController.boardNode.children;
            for (var i = 0; i < children.length; i++) {
                var child = children[i];
                var playerController = child.getComponent(PlayerGameController_1.default);
                if (playerController) {
                    playerController.hideScoreEffects();
                }
            }
        }
    };
    // 更新炸弹数显示
    GamePageController.prototype.updateMineCountDisplay = function (mineCount) {
        if (this.mineCountLabel) {
            this.mineCountLabel.string = "" + mineCount;
        }
    };
    // 根据地图类型切换地图显示
    GamePageController.prototype.switchMapDisplay = function (mapType) {
        // 先隐藏所有地图
        this.hideAllMaps();
        // 根据地图类型显示对应的地图
        if (mapType === 0) {
            this.showSquareMap();
        }
        else if (mapType === 1) {
            this.showHexMap();
        }
        else {
            console.warn("\u672A\u77E5\u7684\u5730\u56FE\u7C7B\u578B: " + mapType + "\uFF0C\u9ED8\u8BA4\u663E\u793A\u65B9\u5F62\u5730\u56FE");
            this.showSquareMap();
        }
    };
    // 显示方形地图
    GamePageController.prototype.showSquareMap = function () {
        if (this.squareMapNode) {
            this.squareMapNode.active = true;
        }
        else {
            console.warn('方形地图节点未挂载');
        }
    };
    // 显示六边形地图
    GamePageController.prototype.showHexMap = function () {
        if (this.hexMapNode) {
            this.hexMapNode.active = true;
        }
        else {
            console.warn('六边形地图节点未挂载');
        }
    };
    // 隐藏所有地图
    GamePageController.prototype.hideAllMaps = function () {
        if (this.squareMapNode) {
            this.squareMapNode.active = false;
        }
        if (this.hexMapNode) {
            this.hexMapNode.active = false;
        }
    };
    // 清除倒计时定时器
    GamePageController.prototype.clearCountdownTimer = function () {
        if (this.countdownInterval) {
            clearInterval(this.countdownInterval);
            this.countdownInterval = null;
        }
    };
    /**
     * 显示所有玩家的分数动画和更新总分（参考先手加分逻辑）
     * @param playerActions 玩家操作列表
     * @param playerTotalScores 玩家总分数据
     */
    GamePageController.prototype.displayPlayerScoreAnimationsAndUpdateTotalScores = function (playerActions, playerTotalScores) {
        var _this = this;
        var _a, _b;
        // 获取当前用户ID
        var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
        if (!currentUserId) {
            console.warn("无法获取当前用户ID");
            return;
        }
        // 查找先手玩家
        var firstChoicePlayer = playerActions.find(function (action) { return action.isFirstChoice; });
        var isCurrentUserFirstChoice = firstChoicePlayer && firstChoicePlayer.userId === currentUserId;
        // 如果我不是先手，先为先手玩家在分数面板显示+1
        if (!isCurrentUserFirstChoice && firstChoicePlayer) {
            var firstChoiceUserIndex_1 = this.findUserIndex(firstChoicePlayer.userId);
            if (firstChoiceUserIndex_1 !== -1) {
                // 0.1秒后显示先手+1
                this.scheduleOnce(function () {
                    _this.showScoreInScorePanel(firstChoiceUserIndex_1, 1);
                }, 0.1);
            }
        }
        // 为每个玩家显示分数动画和更新总分
        playerActions.forEach(function (action, index) {
            var totalScore = playerTotalScores[action.userId] || 0;
            var isFirstChoice = action.isFirstChoice;
            // 延迟显示，让动画错开
            _this.scheduleOnce(function () {
                if (isFirstChoice) {
                    // 先手玩家：特殊处理（先显示+1，再显示本回合分数）
                    _this.showFirstChoicePlayerScoreAnimation(action, currentUserId, totalScore);
                }
                else {
                    // 非先手玩家：直接显示本回合分数
                    _this.showPlayerScoreAnimationAndUpdateTotal(action, currentUserId, totalScore);
                }
            }, index * 0.2);
        });
    };
    /**
     * 显示单个玩家的分数动画和更新总分（参考先手加分逻辑）
     * @param action 玩家操作数据
     * @param currentUserId 当前用户ID
     * @param totalScore 玩家总分
     */
    GamePageController.prototype.showPlayerScoreAnimationAndUpdateTotal = function (action, currentUserId, totalScore) {
        var _this = this;
        var isMyself = action.userId === currentUserId;
        // 1. 在分数面板显示加减分动画（参考先手加分的逻辑）
        if (this.gameScoreController) {
            // 找到用户索引
            var userIndex = this.findUserIndex(action.userId);
            if (userIndex !== -1) {
                // 在分数面板显示加减分效果
                this.showScoreInScorePanel(userIndex, action.score);
            }
        }
        // 2. 更新总分（参考先手加分的updatePlayerScore）
        this.scheduleOnce(function () {
            if (_this.gameScoreController) {
                _this.gameScoreController.updatePlayerScore(action.userId, totalScore);
                // 更新全局数据中的总分
                _this.updatePlayerTotalScoreInGlobalData(action.userId, totalScore);
            }
        }, 1.2);
        // 3. 在所有玩家头像上显示加减分（不仅仅是自己）
        this.scheduleOnce(function () {
            _this.showScoreOnPlayerAvatar(action.userId, action.score);
        }, 0.1);
    };
    /**
     * 更新全局数据中的玩家总分
     * @param userId 用户ID
     * @param totalScore 新的总分
     */
    GamePageController.prototype.updatePlayerTotalScoreInGlobalData = function (userId, totalScore) {
        if (!GlobalBean_1.GlobalBean.GetInstance().noticeStartGame || !GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users) {
            console.warn("没有游戏数据，无法更新玩家总分");
            return;
        }
        var users = GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users;
        var userIndex = users.findIndex(function (user) { return user.userId === userId; });
        if (userIndex !== -1) {
            users[userIndex].score = totalScore;
        }
        else {
            console.warn("\u627E\u4E0D\u5230\u73A9\u5BB6: userId=" + userId);
        }
    };
    /**
     * 查找用户索引
     * @param userId 用户ID
     * @returns 用户索引，找不到返回-1
     */
    GamePageController.prototype.findUserIndex = function (userId) {
        if (!GlobalBean_1.GlobalBean.GetInstance().noticeStartGame || !GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users) {
            console.warn("没有游戏数据，无法查找用户索引");
            return -1;
        }
        var users = GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users;
        return users.findIndex(function (user) { return user.userId === userId; });
    };
    /**
     * 在玩家头像上显示加减分
     * @param userId 用户ID
     * @param score 分数变化
     */
    GamePageController.prototype.showScoreOnPlayerAvatar = function (userId, score) {
        // 根据地图类型调用对应的控制器
        if (this.currentMapType === 0 && this.chessBoardController) {
            // 方形地图
            this.chessBoardController.showPlayerGameScore(userId, score);
        }
        else if (this.currentMapType === 1 && this.hexChessBoardController) {
            // 六边形地图
            this.hexChessBoardController.showHexPlayerGameScore(userId, score);
        }
        else {
            console.warn("没有可用的棋盘控制器，无法显示头像分数");
        }
    };
    /**
     * 在分数面板显示加减分效果
     * @param userIndex 用户索引
     * @param score 分数变化
     */
    GamePageController.prototype.showScoreInScorePanel = function (userIndex, score) {
        if (!this.gameScoreController) {
            console.warn("gameScoreController 不存在，无法在分数面板显示分数");
            return;
        }
        // 获取对应的PlayerScoreController
        var playerScoreController = this.gameScoreController.getPlayerScoreController(userIndex);
        if (playerScoreController) {
            // 显示加减分效果
            if (score > 0) {
                playerScoreController.showAddScore(score);
            }
            else if (score < 0) {
                playerScoreController.showSubScore(Math.abs(score));
            }
        }
        else {
            console.warn("\u627E\u4E0D\u5230\u7528\u6237\u7D22\u5F15 " + userIndex + " \u5BF9\u5E94\u7684PlayerScoreController");
        }
    };
    /**
     * 显示先手玩家的分数动画（在分数面板先显示+1，再显示本回合分数）
     * @param action 玩家操作数据
     * @param currentUserId 当前用户ID
     * @param totalScore 玩家总分
     */
    GamePageController.prototype.showFirstChoicePlayerScoreAnimation = function (action, currentUserId, totalScore) {
        var _this = this;
        var userIndex = this.findUserIndex(action.userId);
        // 第一步：在分数面板显示+1先手奖励（1.2秒，与非先手玩家同步）
        this.scheduleOnce(function () {
            // 分数面板显示本回合分数（+1已经在前面显示过了）
            if (userIndex !== -1) {
                _this.showScoreInScorePanel(userIndex, action.score);
            }
        }, 1.2);
        // 第二步：更新总分（2.4秒）
        this.scheduleOnce(function () {
            if (_this.gameScoreController) {
                _this.gameScoreController.updatePlayerScore(action.userId, totalScore);
                _this.updatePlayerTotalScoreInGlobalData(action.userId, totalScore);
            }
        }, 2.4);
        // 第三步：在player_game_pfb中显示本回合的加减分（与非先手玩家同步）
        this.scheduleOnce(function () {
            _this.showScoreOnPlayerAvatar(action.userId, action.score);
        }, 0.1);
    };
    /**
     * 初始化动画节点
     */
    GamePageController.prototype.initializeAnimationNodes = function () {
        console.log("开始初始化动画节点");
        // 如果节点没有在编辑器中设置，尝试通过路径查找或创建
        if (!this.gameStartNode) {
            this.gameStartNode = cc.find('Canvas/game_start_node');
            if (!this.gameStartNode) {
                // 创建游戏开始节点
                this.gameStartNode = this.createGameStartNode();
            }
        }
        if (!this.roundStartNode) {
            console.log("roundStartNode不存在，尝试查找");
            this.roundStartNode = cc.find('Canvas/round_start_node');
            if (!this.roundStartNode) {
                console.log("未找到现有的回合开始节点，创建新的");
                // 创建回合开始节点
                this.roundStartNode = this.createRoundStartNode();
            }
            else {
                console.log("找到现有的回合开始节点");
            }
        }
        else {
            console.log("roundStartNode已存在");
        }
        // 初始状态设为隐藏
        if (this.gameStartNode) {
            this.gameStartNode.active = false;
        }
        if (this.roundStartNode) {
            this.roundStartNode.active = false;
            console.log("回合开始节点初始化完成，设为隐藏");
        }
    };
    /**
     * 创建游戏开始节点
     */
    GamePageController.prototype.createGameStartNode = function () {
        var node = new cc.Node('game_start_node');
        var canvas = cc.find('Canvas');
        if (canvas) {
            canvas.addChild(node);
        }
        // 设置节点位置和层级
        node.setPosition(0, 0);
        node.zIndex = 1000;
        // 添加Sprite组件并加载图片
        var sprite = node.addComponent(cc.Sprite);
        cc.resources.load('开始游戏@2x-2', cc.SpriteFrame, function (err, spriteFrame) {
            if (!err && spriteFrame) {
                sprite.spriteFrame = spriteFrame;
            }
            else {
                console.warn("无法加载游戏开始图片资源");
            }
        });
        return node;
    };
    /**
     * 创建回合开始节点
     */
    GamePageController.prototype.createRoundStartNode = function () {
        console.log("开始创建回合开始节点");
        var node = new cc.Node('round_start_node');
        var canvas = cc.find('Canvas');
        if (canvas) {
            canvas.addChild(node);
            console.log("回合开始节点已添加到Canvas");
        }
        else {
            console.error("找不到Canvas节点");
        }
        // 设置节点位置和层级
        node.setPosition(-750, -1); // 修改初始位置为-750
        node.zIndex = 1000;
        node.width = 200; // 设置节点宽度
        node.height = 100; // 设置节点高度
        // 添加Sprite组件并加载图片
        var sprite = node.addComponent(cc.Sprite);
        // 设置Sprite的SizeMode为CUSTOM，这样可以控制显示大小
        sprite.sizeMode = cc.Sprite.SizeMode.CUSTOM;
        cc.resources.load('huihe@2x-2', cc.SpriteFrame, function (err, spriteFrame) {
            if (!err && spriteFrame) {
                sprite.spriteFrame = spriteFrame;
                // 设置节点大小为图片的原始大小
                node.width = spriteFrame.getRect().width;
                node.height = spriteFrame.getRect().height;
                console.log("回合开始图片资源加载成功，大小:", node.width, "x", node.height);
            }
            else {
                console.warn("无法加载回合开始图片资源:", err);
                // 如果图片加载失败，创建一个简单的文本标签作为替代
                var label = node.addComponent(cc.Label);
                label.string = "回合开始";
                label.fontSize = 48;
                label.node.color = cc.Color.WHITE;
                console.log("使用文本标签作为回合开始节点");
            }
        });
        console.log("回合开始节点创建完成:", node);
        return node;
    };
    /**
     * 显示游戏开始节点动画（放大展示）
     */
    GamePageController.prototype.showGameStartAnimation = function () {
        if (!this.gameStartNode) {
            console.warn("游戏开始节点不存在");
            return;
        }
        // 初始化节点状态
        this.gameStartNode.active = true;
        this.gameStartNode.scale = 0;
        this.gameStartNode.opacity = 255;
        // 放大展示动画
        cc.tween(this.gameStartNode)
            .to(0.3, { scale: 1.2 }, { easing: 'backOut' })
            .to(0.2, { scale: 1.0 }, { easing: 'backOut' })
            .start();
    };
    /**
     * 隐藏游戏开始节点动画（缩小隐藏）
     */
    GamePageController.prototype.hideGameStartAnimation = function () {
        var _this = this;
        if (!this.gameStartNode || !this.gameStartNode.active) {
            return;
        }
        // 缩小隐藏动画
        cc.tween(this.gameStartNode)
            .to(0.3, { scale: 0, opacity: 0 }, { easing: 'backIn' })
            .call(function () {
            _this.gameStartNode.active = false;
        })
            .start();
    };
    /**
     * 显示回合开始节点动画（从左边移入到中间）
     */
    GamePageController.prototype.showRoundStartAnimation = function () {
        var _this = this;
        console.log("showRoundStartAnimation 被调用");
        if (!this.roundStartNode) {
            console.warn("回合开始节点不存在，尝试重新初始化");
            this.initializeAnimationNodes();
            if (!this.roundStartNode) {
                console.error("重新初始化后回合开始节点仍然不存在");
                return;
            }
        }
        console.log("回合开始节点存在，开始动画", this.roundStartNode);
        // 初始化节点状态：在(-750, -1)位置
        this.roundStartNode.active = true;
        this.roundStartNode.setPosition(-750, -1);
        this.roundStartNode.opacity = 255;
        this.roundStartNode.scale = 1;
        console.log("节点初始化完成，位置:", this.roundStartNode.position, "可见性:", this.roundStartNode.active);
        // 0.5秒移动到(0, -1)，1秒展示，0.5秒移动到(750, -1)，然后恢复到(-750, -1)
        cc.tween(this.roundStartNode)
            .to(0.5, { x: 0 }, { easing: 'quartOut' })
            .delay(1.0)
            .to(0.5, { x: 750 }, { easing: 'quartIn' })
            .call(function () {
            console.log("回合开始动画完成");
            // 动画完成后，恢复到初始位置(-750, -1)为下一次回合开始做准备
            _this.roundStartNode.setPosition(-750, -1);
            _this.roundStartNode.active = false;
        })
            .start();
    };
    /**
     * 测试方法：手动触发回合开始动画
     */
    GamePageController.prototype.testRoundStartAnimation = function () {
        console.log("手动测试回合开始动画");
        this.showRoundStartAnimation();
    };
    GamePageController.prototype.onDestroy = function () {
        // 移除棋盘点击事件监听
        if (this.chessBoardController) {
            this.chessBoardController.node.off('chess-board-click', this.onChessBoardClick, this);
        }
    };
    __decorate([
        property(cc.Node)
    ], GamePageController.prototype, "boardBtnBack", void 0);
    __decorate([
        property(cc.Label)
    ], GamePageController.prototype, "timeLabel", void 0);
    __decorate([
        property(cc.Label)
    ], GamePageController.prototype, "mineCountLabel", void 0);
    __decorate([
        property(cc.Node)
    ], GamePageController.prototype, "squareMapNode", void 0);
    __decorate([
        property(cc.Node)
    ], GamePageController.prototype, "hexMapNode", void 0);
    __decorate([
        property(LeaveDialogController_1.default)
    ], GamePageController.prototype, "leaveDialogController", void 0);
    __decorate([
        property(CongratsDialogController_1.default)
    ], GamePageController.prototype, "congratsDialogController", void 0);
    __decorate([
        property(GameScoreController_1.default)
    ], GamePageController.prototype, "gameScoreController", void 0);
    __decorate([
        property(ChessBoardController_1.default)
    ], GamePageController.prototype, "chessBoardController", void 0);
    __decorate([
        property(HexChessBoardController_1.default)
    ], GamePageController.prototype, "hexChessBoardController", void 0);
    __decorate([
        property(cc.Node)
    ], GamePageController.prototype, "gameStartNode", void 0);
    __decorate([
        property(cc.Node)
    ], GamePageController.prototype, "roundStartNode", void 0);
    GamePageController = __decorate([
        ccclass
    ], GamePageController);
    return GamePageController;
}(cc.Component));
exports.default = GamePageController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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