
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/game/GamePageInitializer.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'c27f8F0/5lGDbDLReB1H1g7', 'GamePageInitializer');
// scripts/game/GamePageInitializer.ts

"use strict";
// 游戏页面初始化器 - 确保GamePageController被正确初始化
// 这个脚本可以附加到任何节点上，它会自动创建和初始化GamePageController
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var GamePageController_1 = require("./GamePageController");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var GamePageInitializer = /** @class */ (function (_super) {
    __extends(GamePageInitializer, _super);
    function GamePageInitializer() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    GamePageInitializer.prototype.onLoad = function () {
        console.log("=== GamePageInitializer onLoad 开始 ===");
        // 检查是否已经有GamePageController实例
        if (window.gamePageController) {
            console.log("GamePageController 实例已存在");
            return;
        }
        // 查找或创建GamePageController
        this.initializeGamePageController();
        console.log("=== GamePageInitializer onLoad 完成 ===");
    };
    GamePageInitializer.prototype.initializeGamePageController = function () {
        // 首先尝试在场景中查找现有的GamePageController
        var existingController = cc.find("Canvas").getComponent(GamePageController_1.default);
        if (existingController) {
            console.log("找到现有的GamePageController");
            window.gamePageController = existingController;
            return;
        }
        // 如果没有找到，创建一个新的节点并添加GamePageController
        console.log("创建新的GamePageController");
        var gamePageNode = new cc.Node("GamePageController");
        var canvas = cc.find("Canvas");
        if (canvas) {
            canvas.addChild(gamePageNode);
            var controller_1 = gamePageNode.addComponent(GamePageController_1.default);
            window.gamePageController = controller_1;
            console.log("GamePageController 创建并初始化完成");
            // 暴露测试方法
            window.testRoundAnimation = function () {
                console.log("全局测试方法被调用");
                controller_1.testRoundStartAnimation();
            };
        }
        else {
            console.error("找不到Canvas节点");
        }
    };
    GamePageInitializer = __decorate([
        ccclass
    ], GamePageInitializer);
    return GamePageInitializer;
}(cc.Component));
exports.default = GamePageInitializer;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0cy9zY3JpcHRzL2dhbWUvR2FtZVBhZ2VJbml0aWFsaXplci50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsd0NBQXdDO0FBQ3hDLDhDQUE4Qzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRTlDLDJEQUFzRDtBQUVoRCxJQUFBLEtBQXdCLEVBQUUsQ0FBQyxVQUFVLEVBQW5DLE9BQU8sYUFBQSxFQUFFLFFBQVEsY0FBa0IsQ0FBQztBQUc1QztJQUFpRCx1Q0FBWTtJQUE3RDs7SUE2Q0EsQ0FBQztJQTNDRyxvQ0FBTSxHQUFOO1FBQ0ksT0FBTyxDQUFDLEdBQUcsQ0FBQyx1Q0FBdUMsQ0FBQyxDQUFDO1FBRXJELDhCQUE4QjtRQUM5QixJQUFLLE1BQWMsQ0FBQyxrQkFBa0IsRUFBRTtZQUNwQyxPQUFPLENBQUMsR0FBRyxDQUFDLDBCQUEwQixDQUFDLENBQUM7WUFDeEMsT0FBTztTQUNWO1FBRUQsMEJBQTBCO1FBQzFCLElBQUksQ0FBQyw0QkFBNEIsRUFBRSxDQUFDO1FBRXBDLE9BQU8sQ0FBQyxHQUFHLENBQUMsdUNBQXVDLENBQUMsQ0FBQztJQUN6RCxDQUFDO0lBRU8sMERBQTRCLEdBQXBDO1FBQ0ksa0NBQWtDO1FBQ2xDLElBQU0sa0JBQWtCLEdBQUcsRUFBRSxDQUFDLElBQUksQ0FBQyxRQUFRLENBQUMsQ0FBQyxZQUFZLENBQUMsNEJBQWtCLENBQUMsQ0FBQztRQUM5RSxJQUFJLGtCQUFrQixFQUFFO1lBQ3BCLE9BQU8sQ0FBQyxHQUFHLENBQUMseUJBQXlCLENBQUMsQ0FBQztZQUN0QyxNQUFjLENBQUMsa0JBQWtCLEdBQUcsa0JBQWtCLENBQUM7WUFDeEQsT0FBTztTQUNWO1FBRUQsdUNBQXVDO1FBQ3ZDLE9BQU8sQ0FBQyxHQUFHLENBQUMsd0JBQXdCLENBQUMsQ0FBQztRQUN0QyxJQUFNLFlBQVksR0FBRyxJQUFJLEVBQUUsQ0FBQyxJQUFJLENBQUMsb0JBQW9CLENBQUMsQ0FBQztRQUN2RCxJQUFNLE1BQU0sR0FBRyxFQUFFLENBQUMsSUFBSSxDQUFDLFFBQVEsQ0FBQyxDQUFDO1FBQ2pDLElBQUksTUFBTSxFQUFFO1lBQ1IsTUFBTSxDQUFDLFFBQVEsQ0FBQyxZQUFZLENBQUMsQ0FBQztZQUM5QixJQUFNLFlBQVUsR0FBRyxZQUFZLENBQUMsWUFBWSxDQUFDLDRCQUFrQixDQUFDLENBQUM7WUFDaEUsTUFBYyxDQUFDLGtCQUFrQixHQUFHLFlBQVUsQ0FBQztZQUNoRCxPQUFPLENBQUMsR0FBRyxDQUFDLDZCQUE2QixDQUFDLENBQUM7WUFFM0MsU0FBUztZQUNSLE1BQWMsQ0FBQyxrQkFBa0IsR0FBRztnQkFDakMsT0FBTyxDQUFDLEdBQUcsQ0FBQyxXQUFXLENBQUMsQ0FBQztnQkFDekIsWUFBVSxDQUFDLHVCQUF1QixFQUFFLENBQUM7WUFDekMsQ0FBQyxDQUFDO1NBQ0w7YUFBTTtZQUNILE9BQU8sQ0FBQyxLQUFLLENBQUMsYUFBYSxDQUFDLENBQUM7U0FDaEM7SUFDTCxDQUFDO0lBNUNnQixtQkFBbUI7UUFEdkMsT0FBTztPQUNhLG1CQUFtQixDQTZDdkM7SUFBRCwwQkFBQztDQTdDRCxBQTZDQyxDQTdDZ0QsRUFBRSxDQUFDLFNBQVMsR0E2QzVEO2tCQTdDb0IsbUJBQW1CIiwiZmlsZSI6IiIsInNvdXJjZVJvb3QiOiIvIiwic291cmNlc0NvbnRlbnQiOlsiLy8g5ri45oiP6aG16Z2i5Yid5aeL5YyW5ZmoIC0g56Gu5L+dR2FtZVBhZ2VDb250cm9sbGVy6KKr5q2j56Gu5Yid5aeL5YyWXG4vLyDov5nkuKrohJrmnKzlj6/ku6XpmYTliqDliLDku7vkvZXoioLngrnkuIrvvIzlroPkvJroh6rliqjliJvlu7rlkozliJ3lp4vljJZHYW1lUGFnZUNvbnRyb2xsZXJcblxuaW1wb3J0IEdhbWVQYWdlQ29udHJvbGxlciBmcm9tIFwiLi9HYW1lUGFnZUNvbnRyb2xsZXJcIjtcblxuY29uc3QgeyBjY2NsYXNzLCBwcm9wZXJ0eSB9ID0gY2MuX2RlY29yYXRvcjtcblxuQGNjY2xhc3NcbmV4cG9ydCBkZWZhdWx0IGNsYXNzIEdhbWVQYWdlSW5pdGlhbGl6ZXIgZXh0ZW5kcyBjYy5Db21wb25lbnQge1xuXG4gICAgb25Mb2FkKCkge1xuICAgICAgICBjb25zb2xlLmxvZyhcIj09PSBHYW1lUGFnZUluaXRpYWxpemVyIG9uTG9hZCDlvIDlp4sgPT09XCIpO1xuICAgICAgICBcbiAgICAgICAgLy8g5qOA5p+l5piv5ZCm5bey57uP5pyJR2FtZVBhZ2VDb250cm9sbGVy5a6e5L6LXG4gICAgICAgIGlmICgod2luZG93IGFzIGFueSkuZ2FtZVBhZ2VDb250cm9sbGVyKSB7XG4gICAgICAgICAgICBjb25zb2xlLmxvZyhcIkdhbWVQYWdlQ29udHJvbGxlciDlrp7kvovlt7LlrZjlnKhcIik7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cblxuICAgICAgICAvLyDmn6Xmib7miJbliJvlu7pHYW1lUGFnZUNvbnRyb2xsZXJcbiAgICAgICAgdGhpcy5pbml0aWFsaXplR2FtZVBhZ2VDb250cm9sbGVyKCk7XG4gICAgICAgIFxuICAgICAgICBjb25zb2xlLmxvZyhcIj09PSBHYW1lUGFnZUluaXRpYWxpemVyIG9uTG9hZCDlrozmiJAgPT09XCIpO1xuICAgIH1cblxuICAgIHByaXZhdGUgaW5pdGlhbGl6ZUdhbWVQYWdlQ29udHJvbGxlcigpIHtcbiAgICAgICAgLy8g6aaW5YWI5bCd6K+V5Zyo5Zy65pmv5Lit5p+l5om+546w5pyJ55qER2FtZVBhZ2VDb250cm9sbGVyXG4gICAgICAgIGNvbnN0IGV4aXN0aW5nQ29udHJvbGxlciA9IGNjLmZpbmQoXCJDYW52YXNcIikuZ2V0Q29tcG9uZW50KEdhbWVQYWdlQ29udHJvbGxlcik7XG4gICAgICAgIGlmIChleGlzdGluZ0NvbnRyb2xsZXIpIHtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKFwi5om+5Yiw546w5pyJ55qER2FtZVBhZ2VDb250cm9sbGVyXCIpO1xuICAgICAgICAgICAgKHdpbmRvdyBhcyBhbnkpLmdhbWVQYWdlQ29udHJvbGxlciA9IGV4aXN0aW5nQ29udHJvbGxlcjtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIOWmguaenOayoeacieaJvuWIsO+8jOWIm+W7uuS4gOS4quaWsOeahOiKgueCueW5tua3u+WKoEdhbWVQYWdlQ29udHJvbGxlclxuICAgICAgICBjb25zb2xlLmxvZyhcIuWIm+W7uuaWsOeahEdhbWVQYWdlQ29udHJvbGxlclwiKTtcbiAgICAgICAgY29uc3QgZ2FtZVBhZ2VOb2RlID0gbmV3IGNjLk5vZGUoXCJHYW1lUGFnZUNvbnRyb2xsZXJcIik7XG4gICAgICAgIGNvbnN0IGNhbnZhcyA9IGNjLmZpbmQoXCJDYW52YXNcIik7XG4gICAgICAgIGlmIChjYW52YXMpIHtcbiAgICAgICAgICAgIGNhbnZhcy5hZGRDaGlsZChnYW1lUGFnZU5vZGUpO1xuICAgICAgICAgICAgY29uc3QgY29udHJvbGxlciA9IGdhbWVQYWdlTm9kZS5hZGRDb21wb25lbnQoR2FtZVBhZ2VDb250cm9sbGVyKTtcbiAgICAgICAgICAgICh3aW5kb3cgYXMgYW55KS5nYW1lUGFnZUNvbnRyb2xsZXIgPSBjb250cm9sbGVyO1xuICAgICAgICAgICAgY29uc29sZS5sb2coXCJHYW1lUGFnZUNvbnRyb2xsZXIg5Yib5bu65bm25Yid5aeL5YyW5a6M5oiQXCIpO1xuICAgICAgICAgICAgXG4gICAgICAgICAgICAvLyDmmrTpnLLmtYvor5Xmlrnms5VcbiAgICAgICAgICAgICh3aW5kb3cgYXMgYW55KS50ZXN0Um91bmRBbmltYXRpb24gPSAoKSA9PiB7XG4gICAgICAgICAgICAgICAgY29uc29sZS5sb2coXCLlhajlsYDmtYvor5Xmlrnms5XooqvosIPnlKhcIik7XG4gICAgICAgICAgICAgICAgY29udHJvbGxlci50ZXN0Um91bmRTdGFydEFuaW1hdGlvbigpO1xuICAgICAgICAgICAgfTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXCLmib7kuI3liLBDYW52YXPoioLngrlcIik7XG4gICAgICAgIH1cbiAgICB9XG59XG4iXX0=