
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/game/Chess/SingleChessBoardController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '04af7LUaqhJFrIS/DTERlBy', 'SingleChessBoardController');
// scripts/game/Chess/SingleChessBoardController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var WebSocketManager_1 = require("../../net/WebSocketManager");
var MessageId_1 = require("../../net/MessageId");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
// 五种棋盘配置
var BOARD_CONFIGS = {
    "8x8": {
        width: 752,
        height: 752,
        rows: 8,
        cols: 8,
        gridWidth: 88,
        gridHeight: 88
    },
    "8x9": {
        width: 752,
        height: 845,
        rows: 9,
        cols: 8,
        gridWidth: 88,
        gridHeight: 88
    },
    "9x9": {
        width: 752,
        height: 747,
        rows: 9,
        cols: 9,
        gridWidth: 76,
        gridHeight: 76
    },
    "9x10": {
        width: 752,
        height: 830,
        rows: 10,
        cols: 9,
        gridWidth: 78,
        gridHeight: 78
    },
    "10x10": {
        width: 752,
        height: 745,
        rows: 10,
        cols: 10,
        gridWidth: 69,
        gridHeight: 69
    }
};
var SingleChessBoardController = /** @class */ (function (_super) {
    __extends(SingleChessBoardController, _super);
    function SingleChessBoardController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.boomPrefab = null; // boom预制体
        _this.biaojiPrefab = null; // biaoji预制体
        _this.boom1Prefab = null; // 数字1预制体
        _this.boom2Prefab = null; // 数字2预制体
        _this.boom3Prefab = null; // 数字3预制体
        _this.boom4Prefab = null; // 数字4预制体
        _this.boom5Prefab = null; // 数字5预制体
        _this.boom6Prefab = null; // 数字6预制体
        _this.boom7Prefab = null; // 数字7预制体
        _this.boom8Prefab = null; // 数字8预制体
        // 五个棋盘节点
        _this.qipan8x8Node = null; // 8x8棋盘节点
        _this.qipan8x9Node = null; // 8x9棋盘节点
        _this.qipan9x9Node = null; // 9x9棋盘节点
        _this.qipan9x10Node = null; // 9x10棋盘节点
        _this.qipan10x10Node = null; // 10x10棋盘节点
        // 当前使用的棋盘节点
        _this.currentBoardNode = null;
        // 当前棋盘配置
        _this.currentBoardConfig = null;
        _this.currentBoardType = "8x8"; // 默认8x8棋盘
        // 格子数据存储
        _this.gridData = []; // 二维数组存储格子数据
        _this.gridNodes = []; // 二维数组存储格子节点
        // 防重复发送消息
        _this.lastClickTime = 0;
        _this.lastClickPosition = "";
        _this.CLICK_COOLDOWN = 200; // 200毫秒冷却时间
        return _this;
    }
    SingleChessBoardController.prototype.onLoad = function () {
        // 不进行默认初始化，等待外部调用initBoard
    };
    SingleChessBoardController.prototype.start = function () {
        // start方法不再自动启用触摸事件，避免与initBoard重复
        // 触摸事件的启用由initBoard方法负责
    };
    /**
     * 根据棋盘类型获取对应的棋盘节点
     * @param boardType 棋盘类型
     */
    SingleChessBoardController.prototype.getBoardNodeByType = function (boardType) {
        switch (boardType) {
            case "8x8":
                return this.qipan8x8Node;
            case "8x9":
                return this.qipan8x9Node;
            case "9x9":
                return this.qipan9x9Node;
            case "9x10":
                return this.qipan9x10Node;
            case "10x10":
                return this.qipan10x10Node;
            default:
                return null;
        }
    };
    /**
     * 初始化指定类型的棋盘
     * @param boardType 棋盘类型 ("8x8", "8x9", "9x9", "9x10", "10x10")
     */
    SingleChessBoardController.prototype.initBoard = function (boardType) {
        if (!BOARD_CONFIGS[boardType]) {
            console.error("\u4E0D\u652F\u6301\u7684\u68CB\u76D8\u7C7B\u578B: " + boardType);
            return;
        }
        // 根据棋盘类型获取对应的节点
        this.currentBoardNode = this.getBoardNodeByType(boardType);
        if (!this.currentBoardNode) {
            console.error("\u68CB\u76D8\u8282\u70B9\u672A\u8BBE\u7F6E\uFF01\u68CB\u76D8\u7C7B\u578B: " + boardType);
            return;
        }
        this.currentBoardType = boardType;
        this.currentBoardConfig = BOARD_CONFIGS[boardType];
        // 清空现有数据
        this.gridData = [];
        this.gridNodes = [];
        // 初始化数据数组
        for (var x = 0; x < this.currentBoardConfig.cols; x++) {
            this.gridData[x] = [];
            this.gridNodes[x] = [];
            for (var y = 0; y < this.currentBoardConfig.rows; y++) {
                this.gridData[x][y] = {
                    x: x,
                    y: y,
                    worldPos: this.getGridWorldPosition(x, y),
                    hasPlayer: false
                };
            }
        }
        this.createGridNodes();
    };
    // 启用现有格子的触摸事件
    SingleChessBoardController.prototype.createGridNodes = function () {
        if (!this.currentBoardNode) {
            console.error("棋盘节点未设置！");
            return;
        }
        // 如果格子已经存在，直接启用触摸事件
        this.enableTouchForExistingGrids();
    };
    // 为现有格子启用触摸事件
    SingleChessBoardController.prototype.enableTouchForExistingGrids = function () {
        // 检查棋盘节点是否存在
        if (!this.currentBoardNode) {
            console.error("棋盘节点未设置，无法启用触摸事件！");
            return;
        }
        // 遍历棋盘节点的所有子节点
        var children = this.currentBoardNode.children;
        for (var i = 0; i < children.length; i++) {
            var child = children[i];
            // 尝试从节点名称解析坐标
            var coords = this.parseGridCoordinateFromName(child.name);
            if (coords) {
                this.setupGridTouchEvents(child, coords.x, coords.y);
                this.gridNodes[coords.x] = this.gridNodes[coords.x] || [];
                this.gridNodes[coords.x][coords.y] = child;
            }
            else {
                // 如果无法从名称解析，尝试从位置计算
                var pos = child.getPosition();
                var coords_1 = this.getGridCoordinateFromPosition(pos);
                if (coords_1) {
                    this.setupGridTouchEvents(child, coords_1.x, coords_1.y);
                    this.gridNodes[coords_1.x] = this.gridNodes[coords_1.x] || [];
                    this.gridNodes[coords_1.x][coords_1.y] = child;
                }
            }
        }
    };
    // 从节点名称解析格子坐标
    SingleChessBoardController.prototype.parseGridCoordinateFromName = function (nodeName) {
        // 尝试匹配 Grid_x_y 格式
        var match = nodeName.match(/Grid_(\d+)_(\d+)/);
        if (match) {
            return { x: parseInt(match[1]), y: parseInt(match[2]) };
        }
        return null;
    };
    // 从位置计算格子坐标（需要考虑不同棋盘类型的边距）
    SingleChessBoardController.prototype.getGridCoordinateFromPosition = function (pos) {
        if (!this.currentBoardConfig)
            return null;
        // 根据不同棋盘类型使用不同的计算方式
        switch (this.currentBoardType) {
            case "8x9":
                return this.getGridCoordinateFromPositionFor8x9(pos);
            case "9x9":
                return this.getGridCoordinateFromPositionFor9x9(pos);
            case "9x10":
                return this.getGridCoordinateFromPositionFor9x10(pos);
            case "10x10":
                return this.getGridCoordinateFromPositionFor10x10(pos);
            default:
                // 默认计算方式（适用于其他棋盘类型）
                var x = Math.floor((pos.x + this.currentBoardConfig.width / 2) / this.currentBoardConfig.gridWidth);
                var y = Math.floor((pos.y + this.currentBoardConfig.height / 2) / this.currentBoardConfig.gridHeight);
                if (this.isValidCoordinate(x, y)) {
                    return { x: x, y: y };
                }
                return null;
        }
    };
    // 8x9棋盘的位置到坐标转换（基于精确坐标，左下角为(0,0)）
    SingleChessBoardController.prototype.getGridCoordinateFromPositionFor8x9 = function (pos) {
        // 使用与预制体位置计算相同的精确参数
        var startX = -321; // 左下角X坐标
        var startY = -364; // 左下角Y坐标
        var stepX = 91.14; // X方向精确步长
        var stepY = 91.125; // Y方向精确步长
        // 从位置反推坐标
        var x = Math.round((pos.x - startX) / stepX);
        var y = Math.round((pos.y - startY) / stepY);
        if (this.isValidCoordinate(x, y)) {
            return { x: x, y: y };
        }
        return null;
    };
    // 9x9棋盘的位置到坐标转换（基于精确坐标，左下角为(0,0)）
    SingleChessBoardController.prototype.getGridCoordinateFromPositionFor9x9 = function (pos) {
        // 使用与预制体位置计算相同的精确参数
        var startX = -322; // 左下角X坐标
        var startY = -320; // 左下角Y坐标
        var stepX = 80.25; // X方向精确步长
        var stepY = 80.375; // Y方向精确步长
        // 从位置反推坐标
        var x = Math.round((pos.x - startX) / stepX);
        var y = Math.round((pos.y - startY) / stepY);
        if (this.isValidCoordinate(x, y)) {
            return { x: x, y: y };
        }
        return null;
    };
    // 9x10棋盘的位置到坐标转换（基于精确坐标，左下角为(0,0)）
    SingleChessBoardController.prototype.getGridCoordinateFromPositionFor9x10 = function (pos) {
        // 使用与预制体位置计算相同的精确参数
        var startX = -320; // 左下角X坐标
        var startY = -361; // 左下角Y坐标
        var stepX = 80; // X方向精确步长
        var stepY = 80.33; // Y方向精确步长
        // 从位置反推坐标
        var x = Math.round((pos.x - startX) / stepX);
        var y = Math.round((pos.y - startY) / stepY);
        if (this.isValidCoordinate(x, y)) {
            return { x: x, y: y };
        }
        return null;
    };
    // 10x10棋盘的位置到坐标转换（基于精确坐标，左下角为(0,0)）
    SingleChessBoardController.prototype.getGridCoordinateFromPositionFor10x10 = function (pos) {
        // 使用与预制体位置计算相同的精确参数
        var startX = -328; // 左下角X坐标
        var startY = -322; // 左下角Y坐标
        var stepX = 72.56; // X方向精确步长
        var stepY = 72; // Y方向精确步长
        // 从位置反推坐标
        var x = Math.round((pos.x - startX) / stepX);
        var y = Math.round((pos.y - startY) / stepY);
        if (this.isValidCoordinate(x, y)) {
            return { x: x, y: y };
        }
        else {
            return null;
        }
    };
    // 为格子设置触摸事件
    SingleChessBoardController.prototype.setupGridTouchEvents = function (gridNode, x, y) {
        var _this = this;
        // 先清除已有的触摸事件，防止重复绑定
        gridNode.off(cc.Node.EventType.TOUCH_START);
        gridNode.off(cc.Node.EventType.TOUCH_END);
        gridNode.off(cc.Node.EventType.TOUCH_CANCEL);
        // 长按相关变量
        var isLongPressing = false;
        var longPressTimer = 0;
        var longPressCallback = null;
        var hasTriggeredLongPress = false; // 标记是否已触发长按
        var LONG_PRESS_TIME = 1.0; // 1秒长按时间
        // 触摸开始事件
        gridNode.on(cc.Node.EventType.TOUCH_START, function (_event) {
            isLongPressing = true;
            longPressTimer = 0;
            hasTriggeredLongPress = false; // 重置长按标记
            // 开始长按检测
            longPressCallback = function () {
                if (isLongPressing) {
                    longPressTimer += 0.1;
                    if (longPressTimer >= LONG_PRESS_TIME && !hasTriggeredLongPress) {
                        hasTriggeredLongPress = true; // 标记已触发长按
                        isLongPressing = false; // 立即停止长按状态，防止触摸结束时执行点击
                        // 执行长按事件
                        _this.onGridLongPress(x, y);
                        // 停止长按检测
                        if (longPressCallback) {
                            _this.unschedule(longPressCallback);
                            longPressCallback = null;
                        }
                    }
                }
            };
            _this.schedule(longPressCallback, 0.1);
        }, this);
        // 触摸结束事件
        gridNode.on(cc.Node.EventType.TOUCH_END, function (event) {
            // 停止长按检测
            if (longPressCallback) {
                _this.unschedule(longPressCallback);
                longPressCallback = null;
            }
            // 严格检查：只有在所有条件都满足的情况下才执行点击事件
            var shouldExecuteClick = isLongPressing &&
                longPressTimer < LONG_PRESS_TIME &&
                !hasTriggeredLongPress;
            if (shouldExecuteClick) {
                _this.onGridClick(x, y, event);
            }
            else {
            }
            // 清理长按检测
            isLongPressing = false;
            hasTriggeredLongPress = false;
        }, this);
        // 触摸取消事件
        gridNode.on(cc.Node.EventType.TOUCH_CANCEL, function (_event) {
            // 清理长按检测
            isLongPressing = false;
            hasTriggeredLongPress = false;
            if (longPressCallback) {
                _this.unschedule(longPressCallback);
                longPressCallback = null;
            }
        }, this);
    };
    // 计算格子的世界坐标位置（左下角为(0,0)）
    SingleChessBoardController.prototype.getGridWorldPosition = function (x, y) {
        if (!this.currentBoardConfig)
            return cc.v2(0, 0);
        // 计算格子中心点位置
        // 左下角为(0,0)，所以y坐标需要从下往上计算
        var posX = (x * this.currentBoardConfig.gridWidth) + (this.currentBoardConfig.gridWidth / 2) - (this.currentBoardConfig.width / 2);
        var posY = (y * this.currentBoardConfig.gridHeight) + (this.currentBoardConfig.gridHeight / 2) - (this.currentBoardConfig.height / 2);
        return cc.v2(posX, posY);
    };
    // 格子点击事件 - 发送挖掘操作
    SingleChessBoardController.prototype.onGridClick = function (x, y, _event) {
        // 检查坐标是否有效
        if (!this.isValidCoordinate(x, y)) {
            return;
        }
        // 检查该位置是否已经有任何预制体（包括biaoji）
        if (this.gridData[x][y].hasPlayer) {
            return;
        }
        // 发送LevelClickBlock消息
        this.sendLevelClickBlock(x, y, 1);
    };
    // 格子长按事件 - 标记/取消标记操作（参考联机版逻辑）
    SingleChessBoardController.prototype.onGridLongPress = function (x, y) {
        // 检查坐标是否有效
        if (!this.isValidCoordinate(x, y)) {
            return;
        }
        // 检查该位置是否已经有biaoji预制体
        if (this.hasBiaojiAt(x, y)) {
            // 如果已经有biaoji，则删除它（本地立即处理）
            this.removeBiaojiAt(x, y);
            // 发送取消标记消息（但不等待响应，因为已经本地处理了）
            this.sendLevelClickBlock(x, y, 2);
        }
        else if (!this.gridData[x][y].hasPlayer) {
            // 如果没有任何预制体，则生成biaoji（本地立即处理）
            this.createBiaojiPrefab(x, y);
            // 发送标记消息（但不等待响应，因为已经本地处理了）
            this.sendLevelClickBlock(x, y, 2);
        }
        else {
            // 如果有其他类型的预制体（如数字、boom），则不处理
        }
    };
    // 发送LevelClickBlock消息
    SingleChessBoardController.prototype.sendLevelClickBlock = function (x, y, action) {
        // 防重复发送检查
        var currentTime = Date.now();
        var positionKey = x + "," + y + "," + action;
        if (currentTime - this.lastClickTime < this.CLICK_COOLDOWN && this.lastClickPosition === positionKey) {
            return;
        }
        this.lastClickTime = currentTime;
        this.lastClickPosition = positionKey;
        var clickData = {
            x: x,
            y: y,
            action: action // 1=挖掘方块，2=标记/取消标记地雷
        };
        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeLevelClickBlock, clickData);
    };
    // 检查坐标是否有效
    SingleChessBoardController.prototype.isValidCoordinate = function (x, y) {
        if (!this.currentBoardConfig)
            return false;
        return x >= 0 && x < this.currentBoardConfig.cols && y >= 0 && y < this.currentBoardConfig.rows;
    };
    /**
     * 在指定位置创建biaoji预制体
     * @param x 格子x坐标
     * @param y 格子y坐标
     */
    SingleChessBoardController.prototype.createBiaojiPrefab = function (x, y) {
        if (!this.biaojiPrefab) {
            console.error("biaojiPrefab 预制体未设置，请在编辑器中挂载");
            return;
        }
        // 实例化biaoji预制体
        var biaojiNode = cc.instantiate(this.biaojiPrefab);
        biaojiNode.name = "Biaoji";
        // 设置位置
        var position = this.calculatePrefabPosition(x, y);
        biaojiNode.setPosition(position);
        // 添加到棋盘
        this.currentBoardNode.addChild(biaojiNode);
        // 播放出现动画，10x10棋盘使用0.8缩放
        var targetScale = this.currentBoardType === "10x10" ? 0.8 : 1.0;
        biaojiNode.setScale(0);
        cc.tween(biaojiNode)
            .to(0.2, { scaleX: targetScale, scaleY: targetScale }, { easing: 'backOut' })
            .start();
        // 更新格子数据
        this.gridData[x][y].hasPlayer = true;
        this.gridData[x][y].playerNode = biaojiNode;
    };
    /**
     * 在指定位置创建boom预制体
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param isCurrentUser 是否是当前用户点到的雷（可选，默认为true以保持向后兼容）
     */
    SingleChessBoardController.prototype.createBoomPrefab = function (x, y, isCurrentUser) {
        if (isCurrentUser === void 0) { isCurrentUser = true; }
        if (!this.boomPrefab) {
            console.error("SingleChessBoardController: boomPrefab 预制体未设置，请在编辑器中挂载");
            return;
        }
        // 实例化boom预制体
        var boomNode = cc.instantiate(this.boomPrefab);
        boomNode.name = "Boom";
        // 设置位置
        var position = this.calculatePrefabPosition(x, y);
        boomNode.setPosition(position);
        // 添加到棋盘
        this.currentBoardNode.addChild(boomNode);
        // 播放出现动画，10x10棋盘使用0.8缩放
        var targetScale = this.currentBoardType === "10x10" ? 0.8 : 1.0;
        var bounceScale = targetScale * 1.2; // 弹跳效果比目标缩放大20%
        boomNode.setScale(0);
        cc.tween(boomNode)
            .to(0.3, { scaleX: bounceScale, scaleY: bounceScale }, { easing: 'backOut' })
            .to(0.1, { scaleX: targetScale, scaleY: targetScale })
            .start();
        // 只有当前用户点到雷时才播放棋盘震动效果
        if (isCurrentUser) {
            this.playBoardShakeAnimation();
        }
        // 更新格子数据
        this.gridData[x][y].hasPlayer = true;
        this.gridData[x][y].playerNode = boomNode;
    };
    /**
     * 在指定位置创建数字预制体
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param number 数字(1-8)
     */
    SingleChessBoardController.prototype.createNumberPrefab = function (x, y, number) {
        if (number < 1 || number > 8) {
            console.error("\u65E0\u6548\u7684\u6570\u5B57: " + number);
            return;
        }
        // 获取对应的数字预制体
        var prefab = this.getNumberPrefab(number);
        if (!prefab) {
            console.error("\u6570\u5B57" + number + "\u9884\u5236\u4F53\u672A\u8BBE\u7F6E");
            return;
        }
        // 实例化数字预制体
        var numberNode = cc.instantiate(prefab);
        numberNode.name = "Boom" + number;
        // 设置位置
        var position = this.calculatePrefabPosition(x, y);
        numberNode.setPosition(position);
        // 添加到棋盘
        this.currentBoardNode.addChild(numberNode);
        // 播放出现动画，10x10棋盘使用0.8缩放
        var targetScale = this.currentBoardType === "10x10" ? 0.8 : 1.0;
        numberNode.setScale(0);
        cc.tween(numberNode)
            .to(0.2, { scaleX: targetScale, scaleY: targetScale }, { easing: 'backOut' })
            .start();
    };
    // 获取数字预制体
    SingleChessBoardController.prototype.getNumberPrefab = function (number) {
        switch (number) {
            case 1: return this.boom1Prefab;
            case 2: return this.boom2Prefab;
            case 3: return this.boom3Prefab;
            case 4: return this.boom4Prefab;
            case 5: return this.boom5Prefab;
            case 6: return this.boom6Prefab;
            case 7: return this.boom7Prefab;
            case 8: return this.boom8Prefab;
            default: return null;
        }
    };
    /**
     * 计算预制体的精确位置（参考联机版ChessBoardController）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @returns 预制体应该放置的精确位置
     */
    SingleChessBoardController.prototype.calculatePrefabPosition = function (x, y) {
        if (!this.currentBoardConfig) {
            return cc.v2(0, 0);
        }
        // 根据不同棋盘类型使用不同的计算方式
        switch (this.currentBoardType) {
            case "8x8":
                return this.calculatePrefabPositionFor8x8(x, y);
            case "8x9":
                return this.calculatePrefabPositionFor8x9(x, y);
            case "9x9":
                return this.calculatePrefabPositionFor9x9(x, y);
            case "9x10":
                return this.calculatePrefabPositionFor9x10(x, y);
            case "10x10":
                return this.calculatePrefabPositionFor10x10(x, y);
            default:
                return this.getGridWorldPosition(x, y);
        }
    };
    // 8x8棋盘的预制体位置计算（参考联机版）
    SingleChessBoardController.prototype.calculatePrefabPositionFor8x8 = function (x, y) {
        // 根据联机版的坐标规律计算：
        // (0,0) → (-314, -310)
        // (1,0) → (-224, -310)  // x增加90
        // (0,1) → (-314, -222)  // y增加88
        // (7,7) → (310, 312)
        var startX = -314; // 起始X坐标
        var startY = -310; // 起始Y坐标
        var stepX = 90; // X方向步长
        var stepY = 88; // Y方向步长
        var finalX = startX + (x * stepX);
        var finalY = startY + (y * stepY);
        return cc.v2(finalX, finalY);
    };
    // 8x9棋盘的预制体位置计算（基于精确坐标，左下角为(0,0)）
    SingleChessBoardController.prototype.calculatePrefabPositionFor8x9 = function (x, y) {
        // 根据提供的精确坐标：
        // 左下角(0,0)：(-321, -364)
        // 右下角(7,0)：(317, -364)
        // 左上角(0,8)：(-321, 365)
        // 右上角(7,8)：(317, 365)
        // 计算步长：
        // X方向步长：(317 - (-321)) / 7 = 638 / 7 ≈ 91.14
        // Y方向步长：(365 - (-364)) / 8 = 729 / 8 ≈ 91.125
        var startX = -321; // 左下角X坐标
        var startY = -364; // 左下角Y坐标
        var stepX = 91.14; // X方向精确步长
        var stepY = 91.125; // Y方向精确步长
        var finalX = startX + (x * stepX);
        var finalY = startY + (y * stepY);
        return cc.v2(finalX, finalY);
    };
    // 9x9棋盘的预制体位置计算（基于精确坐标，左下角为(0,0)）
    SingleChessBoardController.prototype.calculatePrefabPositionFor9x9 = function (x, y) {
        // 根据提供的精确坐标：
        // 左下角(0,0)：(-322, -320)
        // 右下角(8,0)：(320, -320)
        // 左上角(0,8)：(-322, 365)
        // 右上角(8,8)：(320, 323)
        // 计算步长：
        // X方向步长：(320 - (-322)) / 8 = 642 / 8 = 80.25
        // Y方向步长：(323 - (-320)) / 8 = 643 / 8 = 80.375
        var startX = -322; // 左下角X坐标
        var startY = -320; // 左下角Y坐标
        var stepX = 80.25; // X方向精确步长
        var stepY = 80.375; // Y方向精确步长
        var finalX = startX + (x * stepX);
        var finalY = startY + (y * stepY);
        return cc.v2(finalX, finalY);
    };
    // 9x10棋盘的预制体位置计算（基于精确坐标，左下角为(0,0)）
    SingleChessBoardController.prototype.calculatePrefabPositionFor9x10 = function (x, y) {
        // 根据提供的精确坐标：
        // 左下角(0,0)：(-320, -361)
        // 右下角(8,0)：(320, -361)
        // 左上角(0,9)：(-320, 362)
        // 右上角(8,9)：(320, 362)
        // 计算步长：
        // X方向步长：(320 - (-320)) / 8 = 640 / 8 = 80
        // Y方向步长：(362 - (-361)) / 9 = 723 / 9 = 80.33
        var startX = -320; // 左下角X坐标
        var startY = -361; // 左下角Y坐标
        var stepX = 80; // X方向精确步长
        var stepY = 80.33; // Y方向精确步长
        var finalX = startX + (x * stepX);
        var finalY = startY + (y * stepY);
        return cc.v2(finalX, finalY);
    };
    // 10x10棋盘的预制体位置计算（基于精确坐标，左下角为(0,0)）
    SingleChessBoardController.prototype.calculatePrefabPositionFor10x10 = function (x, y) {
        // 根据提供的精确坐标：
        // 左下角(0,0)：(-328, -322)
        // 右下角(9,0)：(325, -322)
        // 左上角(0,9)：(-328, 326)
        // 右上角(9,9)：(325, 326)
        // 计算步长：
        // X方向步长：(325 - (-328)) / 9 = 653 / 9 = 72.56
        // Y方向步长：(326 - (-322)) / 9 = 648 / 9 = 72
        var startX = -328; // 左下角X坐标
        var startY = -322; // 左下角Y坐标
        var stepX = 72.56; // X方向精确步长
        var stepY = 72; // Y方向精确步长
        var finalX = startX + (x * stepX);
        var finalY = startY + (y * stepY);
        return cc.v2(finalX, finalY);
    };
    // 播放棋盘震动动画
    SingleChessBoardController.prototype.playBoardShakeAnimation = function () {
        if (!this.currentBoardNode)
            return;
        var originalPosition = this.currentBoardNode.getPosition();
        var shakeIntensity = 10;
        cc.tween(this.currentBoardNode)
            .repeat(5, cc.tween()
            .to(0.05, { position: cc.v3(originalPosition.x + shakeIntensity, originalPosition.y, 0) })
            .to(0.05, { position: cc.v3(originalPosition.x - shakeIntensity, originalPosition.y, 0) }))
            .to(0.1, { position: cc.v3(originalPosition.x, originalPosition.y, 0) })
            .start();
    };
    /**
     * 显示所有隐藏的格子（游戏结束时调用）
     */
    SingleChessBoardController.prototype.showAllHiddenGrids = function () {
        if (!this.currentBoardNode) {
            console.error("棋盘节点未设置，无法显示隐藏格子！");
            return;
        }
        // 遍历棋盘的所有子节点，找到小格子并显示
        for (var i = 0; i < this.currentBoardNode.children.length; i++) {
            var child = this.currentBoardNode.children[i];
            // 如果是小格子节点
            if (child.name.startsWith("Grid_") || child.name === "block") {
                // 停止所有可能正在进行的动画
                child.stopAllActions();
                // 恢复显示状态
                child.active = true;
                child.opacity = 255;
                child.scaleX = 1;
                child.scaleY = 1;
                // 确保格子可以交互
                var button = child.getComponent(cc.Button);
                if (button) {
                    button.enabled = true;
                }
            }
        }
    };
    /**
     * 清除所有预制体（游戏结束时调用）
     */
    SingleChessBoardController.prototype.clearAllPrefabs = function () {
        if (!this.currentBoardNode) {
            console.error("棋盘节点未设置，无法清除预制体！");
            return;
        }
        var childrenToRemove = [];
        // 遍历棋盘的所有子节点
        for (var i = 0; i < this.currentBoardNode.children.length; i++) {
            var child = this.currentBoardNode.children[i];
            // 检查是否是预制体（通过名称判断）
            if (child.name === "Biaoji" || child.name === "Boom" || child.name.startsWith("Boom")) {
                childrenToRemove.push(child);
            }
        }
        // 移除找到的预制体
        childrenToRemove.forEach(function (child) {
            child.removeFromParent();
        });
        // 重置格子数据
        this.reinitializeBoardData();
    };
    /**
     * 重新初始化棋盘数据（仅在开始新游戏时调用）
     */
    SingleChessBoardController.prototype.reinitializeBoardData = function () {
        if (!this.currentBoardConfig)
            return;
        // 重置gridData中的预制体状态
        for (var x = 0; x < this.currentBoardConfig.cols; x++) {
            for (var y = 0; y < this.currentBoardConfig.rows; y++) {
                if (this.gridData[x] && this.gridData[x][y]) {
                    this.gridData[x][y].hasPlayer = false;
                    this.gridData[x][y].playerNode = null;
                }
            }
        }
    };
    /**
     * 处理ExtendLevelInfo消息（游戏结束时调用）
     */
    SingleChessBoardController.prototype.onExtendLevelInfo = function () {
        this.showAllHiddenGrids();
        this.clearAllPrefabs();
    };
    /**
     * 处理LevelGameEnd消息（游戏结束时调用）
     * 注意：不清理任何数据，保持玩家的游玩痕迹
     */
    SingleChessBoardController.prototype.onLevelGameEnd = function () {
        // 不显示隐藏的格子，保持当前状态
        // 不清理预制体，不重置格子状态，保持游戏结果显示
        // 让玩家可以看到自己的标记（biaoji）、挖掘结果（数字、boom）等
    };
    /**
     * 隐藏指定位置的格子（点击时调用）
     */
    SingleChessBoardController.prototype.hideGridAt = function (x, y) {
        if (!this.isValidCoordinate(x, y)) {
            console.warn("\u9690\u85CF\u683C\u5B50\u5931\u8D25\uFF1A\u5750\u6807(" + x + ", " + y + ")\u65E0\u6548");
            return;
        }
        // 获取格子节点
        var gridNode = this.gridNodes[x] && this.gridNodes[x][y];
        if (gridNode) {
            // 使用动画隐藏格子
            cc.tween(gridNode)
                .to(0.3, { opacity: 0, scaleX: 0, scaleY: 0 }, { easing: 'sineIn' })
                .call(function () {
                gridNode.active = false;
            })
                .start();
        }
    };
    /**
     * 获取当前棋盘类型
     */
    SingleChessBoardController.prototype.getCurrentBoardType = function () {
        return this.currentBoardType;
    };
    /**
     * 获取当前棋盘配置
     */
    SingleChessBoardController.prototype.getCurrentBoardConfig = function () {
        return this.currentBoardConfig;
    };
    /**
     * 处理点击响应，根据服务器返回的结果更新棋盘 - 参考联机版的地图更新逻辑
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param result 点击结果 ("boom" | "safe" | number)
     */
    SingleChessBoardController.prototype.handleClickResponse = function (x, y, result) {
        if (!this.isValidCoordinate(x, y)) {
            console.warn("\u5904\u7406\u70B9\u51FB\u54CD\u5E94\u5931\u8D25\uFF1A\u5750\u6807(" + x + ", " + y + ")\u65E0\u6548");
            return;
        }
        // 如果格子上有biaoji预制体，先移除它
        if (this.hasBiaojiAt(x, y)) {
            // 直接移除，不播放动画
            var gridData = this.gridData[x][y];
            if (gridData.playerNode) {
                gridData.playerNode.removeFromParent();
                gridData.playerNode = null;
            }
        }
        // 标记格子已被处理，防止重复点击
        this.gridData[x][y].hasPlayer = true;
        // 使用连锁动画的方式处理单个格子，保持一致性
        this.playGridDisappearAnimation(x, y, result);
    };
    /**
     * 处理连锁展开结果
     * @param floodFillResults 连锁展开数据数组
     */
    SingleChessBoardController.prototype.handleFloodFillResults = function (floodFillResults) {
        var _this = this;
        // 按延迟时间分组处理，创建连锁展开动画效果
        floodFillResults.forEach(function (gridResult, index) {
            var x = gridResult.x, y = gridResult.y, neighborMines = gridResult.neighborMines;
            if (!_this.isValidCoordinate(x, y)) {
                console.warn("\u8FDE\u9501\u5C55\u5F00\u8DF3\u8FC7\u65E0\u6548\u5750\u6807: (" + x + ", " + y + ")");
                return;
            }
            // 计算延迟时间，创建波浪式展开效果
            var delay = index * 0.05; // 每个格子延迟50毫秒
            _this.scheduleOnce(function () {
                // 使用连锁动画的方式处理格子，保持一致性
                _this.playGridDisappearAnimation(x, y, neighborMines);
            }, delay);
        });
    };
    /**
     * 批量处理连锁反应的格子（参考联机版的processFloodFillResult）
     * @param revealedGrids 被揭开的格子列表 {x: number, y: number, neighborMines: number}[]
     */
    SingleChessBoardController.prototype.handleChainReaction = function (revealedGrids) {
        var _this = this;
        if (!revealedGrids || revealedGrids.length === 0) {
            return;
        }
        // 为每个连锁格子播放消失动画，参考联机版的逻辑
        revealedGrids.forEach(function (block, index) {
            // 延迟播放动画，创造连锁效果
            _this.scheduleOnce(function () {
                _this.playGridDisappearAnimation(block.x, block.y, block.neighborMines);
            }, index * 0.1); // 每个格子间隔0.1秒
        });
    };
    /**
     * 播放格子消失动画（连锁效果）- 参考联机版ChessBoardController
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param neighborMines 周围地雷数量或结果类型（可以是数字、"mine"、"boom"等）
     */
    SingleChessBoardController.prototype.playGridDisappearAnimation = function (x, y, neighborMines) {
        var _this = this;
        // 如果格子上有biaoji预制体，先移除它（连锁展开时）
        if (this.hasBiaojiAt(x, y)) {
            var gridData = this.gridData[x][y];
            if (gridData.playerNode) {
                gridData.playerNode.removeFromParent();
                gridData.playerNode = null;
            }
        }
        // 标记格子已被处理（对于连锁格子）
        if (this.isValidCoordinate(x, y)) {
            this.gridData[x][y].hasPlayer = true;
        }
        // 先删除格子
        this.removeGridAt(x, y);
        // 延迟0.3秒后显示数字（等格子消失动画完成）
        // 使用带标识的延迟任务，方便重置时清理
        var delayCallback = function () {
            _this.updateNeighborMinesDisplay(x, y, neighborMines);
        };
        this.scheduleOnce(delayCallback, 0.3);
    };
    /**
     * 隐藏指定位置的格子（不销毁，以便重置时可以重新显示）- 参考联机版
     * @param x 格子x坐标
     * @param y 格子y坐标
     */
    SingleChessBoardController.prototype.removeGridAt = function (x, y) {
        if (!this.isValidCoordinate(x, y)) {
            console.warn("\u9690\u85CF\u683C\u5B50\u5931\u8D25\uFF1A\u5750\u6807(" + x + ", " + y + ")\u65E0\u6548");
            return;
        }
        // 获取格子节点
        var gridNode = this.gridNodes[x] && this.gridNodes[x][y];
        if (gridNode) {
            // 使用cc.Tween播放消失动画后隐藏（不销毁）
            cc.tween(gridNode)
                .to(0.3, { opacity: 0, scaleX: 0, scaleY: 0 }, { easing: 'sineIn' })
                .call(function () {
                gridNode.active = false; // 隐藏而不是销毁
            })
                .start();
        }
    };
    /**
     * 更新指定位置的neighborMines显示（使用boom数字预制体）- 参考联机版
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param neighborMines 周围地雷数量或结果类型
     */
    SingleChessBoardController.prototype.updateNeighborMinesDisplay = function (x, y, neighborMines) {
        if (neighborMines === "boom" || neighborMines === "mine") {
            // 踩到地雷，生成boom预制体并触发震动
            this.createBoomPrefab(x, y, true); // true表示是当前用户踩到的雷，需要震动
        }
        else if (typeof neighborMines === "number" && neighborMines > 0) {
            // 显示数字
            this.createNumberPrefab(x, y, neighborMines);
        }
        else {
            // 如果是0、"safe"或其他，则不显示任何预制体
        }
    };
    /**
     * 移除指定位置的biaoji预制体
     * @param x 格子x坐标
     * @param y 格子y坐标
     */
    SingleChessBoardController.prototype.removeBiaojiAt = function (x, y) {
        if (!this.isValidCoordinate(x, y)) {
            return;
        }
        var gridData = this.gridData[x][y];
        if (gridData.hasPlayer && gridData.playerNode && gridData.playerNode.name === "Biaoji") {
            // 播放消失动画
            cc.tween(gridData.playerNode)
                .to(0.2, { scaleX: 0, scaleY: 0 }, { easing: 'sineIn' })
                .call(function () {
                gridData.playerNode.removeFromParent();
                gridData.hasPlayer = false;
                gridData.playerNode = null;
            })
                .start();
        }
    };
    /**
     * 检查指定位置是否有biaoji预制体
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @returns 是否有biaoji预制体
     */
    SingleChessBoardController.prototype.hasBiaojiAt = function (x, y) {
        if (!this.isValidCoordinate(x, y)) {
            return false;
        }
        var gridData = this.gridData[x][y];
        return gridData.hasPlayer && gridData.playerNode && gridData.playerNode.name === "Biaoji";
    };
    /**
     * 获取所有biaoji的位置
     * @returns biaoji位置数组
     */
    SingleChessBoardController.prototype.getAllBiaojiPositions = function () {
        var positions = [];
        if (!this.currentBoardConfig)
            return positions;
        for (var x = 0; x < this.currentBoardConfig.cols; x++) {
            for (var y = 0; y < this.currentBoardConfig.rows; y++) {
                if (this.hasBiaojiAt(x, y)) {
                    positions.push({ x: x, y: y });
                }
            }
        }
        return positions;
    };
    /**
     * 重置棋盘到初始状态
     */
    SingleChessBoardController.prototype.resetBoard = function () {
        var _this = this;
        // 清理所有延迟任务（重要：防止上一局的连锁动画影响新游戏）
        this.unscheduleAllCallbacks();
        // 清除所有预制体
        this.clearAllPrefabs();
        // 重新初始化棋盘数据
        this.reinitializeBoardData();
        // 显示所有格子
        this.showAllHiddenGrids();
        // 重新启用触摸事件
        this.scheduleOnce(function () {
            _this.enableTouchForExistingGrids();
        }, 0.1);
    };
    /**
     * 禁用所有格子的触摸事件（游戏结束时调用）
     */
    SingleChessBoardController.prototype.disableAllGridTouch = function () {
        if (!this.currentBoardConfig)
            return;
        for (var x = 0; x < this.currentBoardConfig.cols; x++) {
            for (var y = 0; y < this.currentBoardConfig.rows; y++) {
                var gridNode = this.gridNodes[x] && this.gridNodes[x][y];
                if (gridNode) {
                    gridNode.off(cc.Node.EventType.TOUCH_START);
                    gridNode.off(cc.Node.EventType.TOUCH_END);
                    gridNode.off(cc.Node.EventType.TOUCH_CANCEL);
                }
            }
        }
    };
    /**
     * 启用所有格子的触摸事件
     */
    SingleChessBoardController.prototype.enableAllGridTouch = function () {
        this.enableTouchForExistingGrids();
    };
    __decorate([
        property(cc.Prefab)
    ], SingleChessBoardController.prototype, "boomPrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SingleChessBoardController.prototype, "biaojiPrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SingleChessBoardController.prototype, "boom1Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SingleChessBoardController.prototype, "boom2Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SingleChessBoardController.prototype, "boom3Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SingleChessBoardController.prototype, "boom4Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SingleChessBoardController.prototype, "boom5Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SingleChessBoardController.prototype, "boom6Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SingleChessBoardController.prototype, "boom7Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SingleChessBoardController.prototype, "boom8Prefab", void 0);
    __decorate([
        property(cc.Node)
    ], SingleChessBoardController.prototype, "qipan8x8Node", void 0);
    __decorate([
        property(cc.Node)
    ], SingleChessBoardController.prototype, "qipan8x9Node", void 0);
    __decorate([
        property(cc.Node)
    ], SingleChessBoardController.prototype, "qipan9x9Node", void 0);
    __decorate([
        property(cc.Node)
    ], SingleChessBoardController.prototype, "qipan9x10Node", void 0);
    __decorate([
        property(cc.Node)
    ], SingleChessBoardController.prototype, "qipan10x10Node", void 0);
    SingleChessBoardController = __decorate([
        ccclass
    ], SingleChessBoardController);
    return SingleChessBoardController;
}(cc.Component));
exports.default = SingleChessBoardController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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