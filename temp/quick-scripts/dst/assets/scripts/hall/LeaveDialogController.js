
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/hall/LeaveDialogController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'dae83aDtN5Oe6VqQSxT8XBO', 'LeaveDialogController');
// scripts/hall/LeaveDialogController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var GameMgr_1 = require("../common/GameMgr");
var MessageId_1 = require("../net/MessageId");
var WebSocketManager_1 = require("../net/WebSocketManager");
var Config_1 = require("../util/Config");
var Tools_1 = require("../util/Tools");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var LeaveDialogController = /** @class */ (function (_super) {
    __extends(LeaveDialogController, _super);
    function LeaveDialogController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.boardBg = null;
        _this.boardBtnClose = null;
        _this.contentLay = null;
        _this.cancelBtn = null;
        _this.leaveBtn = null;
        _this.tipContent = null;
        _this.type = 0; //0是完全退出这个游戏 1是退出本局游戏
        _this.roomId = 0; // 房间ID，用于退出时发送给服务器
        _this.backCallback = null; //隐藏弹窗的回调
        return _this;
        // update (dt) {}
    }
    // onLoad () {}
    LeaveDialogController.prototype.start = function () {
        var _this = this;
        Tools_1.Tools.imageButtonClick(this.boardBtnClose, Config_1.Config.buttonRes + 'board_btn_close_normal', Config_1.Config.buttonRes + 'board_btn_close_pressed', function () {
            _this.hide();
        });
        //cancel 按钮点击事件
        Tools_1.Tools.yellowButton(this.cancelBtn, function () {
            _this.hide();
        });
        //leave 按钮点击事件
        Tools_1.Tools.redButton(this.leaveBtn, function () {
            if (_this.type === 0) {
                GameMgr_1.GameMgr.H5SDK.CloseWebView(); //这个是退出游戏的
            }
            else {
                //这个是退出本局游戏的
                _this.hide();
                //退出当前房间游戏
                var leaveRoomData = { 'isConfirmLeave': true };
                if (_this.roomId > 0) {
                    leaveRoomData.roomId = _this.roomId;
                }
                WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeLeaveRoom, leaveRoomData);
            }
        });
    };
    LeaveDialogController.prototype.show = function (type, backCallback, roomId) {
        if (roomId === void 0) { roomId = 0; }
        this.type = type;
        this.roomId = roomId;
        this.backCallback = backCallback;
        if (type === 0) {
            this.tipContent.string = window.getLocalizedStr('ExitApplication'); //显示完全退出的文案退出
        }
        else {
            this.tipContent.string = window.getLocalizedStr('QuitTheGame'); //显示退出本局游戏的文案
        }
        this.node.active = true;
        this.boardBg.scale = 0;
        // 执行缩放动画
        cc.tween(this.boardBg)
            .to(Config_1.Config.dialogScaleTime, { scale: 1 })
            .start();
    };
    LeaveDialogController.prototype.hide = function () {
        var _this = this;
        if (this.backCallback) {
            this.backCallback();
        }
        // 执行缩放动画
        cc.tween(this.boardBg)
            .to(Config_1.Config.dialogScaleTime, { scale: 0 })
            .call(function () {
            _this.node.active = false;
        })
            .start();
    };
    __decorate([
        property(cc.Node)
    ], LeaveDialogController.prototype, "boardBg", void 0);
    __decorate([
        property(cc.Node)
    ], LeaveDialogController.prototype, "boardBtnClose", void 0);
    __decorate([
        property(cc.Node)
    ], LeaveDialogController.prototype, "contentLay", void 0);
    __decorate([
        property(cc.Node)
    ], LeaveDialogController.prototype, "cancelBtn", void 0);
    __decorate([
        property(cc.Node)
    ], LeaveDialogController.prototype, "leaveBtn", void 0);
    __decorate([
        property(cc.Label)
    ], LeaveDialogController.prototype, "tipContent", void 0);
    LeaveDialogController = __decorate([
        ccclass
    ], LeaveDialogController);
    return LeaveDialogController;
}(cc.Component));
exports.default = LeaveDialogController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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