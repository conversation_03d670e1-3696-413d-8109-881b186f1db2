
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/hall/Level/LevelItemController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '5e3930KM/5Lr5e2Eq/8nGah', 'LevelItemController');
// scripts/hall/Level/LevelItemController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var LevelSelectController_1 = require("./LevelSelectController");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var LevelItemController = /** @class */ (function (_super) {
    __extends(LevelItemController, _super);
    function LevelItemController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.levelSprite = null;
        _this.levelLabel = null;
        _this.levelButton = null;
        _this.levelNumber = 1;
        _this.levelStatus = LevelSelectController_1.LevelStatus.LOCKED;
        _this.isSelected = false;
        _this.currentImagePath = ""; // 记录当前使用的图片路径
        return _this;
    }
    LevelItemController_1 = LevelItemController;
    LevelItemController.prototype.onLoad = function () {
        // 初始化组件引用
        if (!this.levelSprite) {
            this.levelSprite = this.getComponent(cc.Sprite);
        }
        if (!this.levelLabel) {
            this.levelLabel = this.getComponentInChildren(cc.Label);
        }
        if (!this.levelButton) {
            this.levelButton = this.getComponent(cc.Button);
        }
    };
    LevelItemController.prototype.start = function () {
        // 确保在start时更新外观
        this.updateAppearance();
    };
    /**
     * 设置关卡数据
     */
    LevelItemController.prototype.setLevelData = function (levelNumber, status, selected) {
        if (selected === void 0) { selected = false; }
        this.levelNumber = levelNumber;
        this.levelStatus = status;
        this.isSelected = selected;
        // 更新标签文本
        if (this.levelLabel) {
            this.levelLabel.string = levelNumber.toString();
        }
        this.updateAppearance();
    };
    /**
     * 检查是否为特殊关卡（第5、10、15、20、25关）
     */
    LevelItemController.prototype.isSpecialLevel = function (levelNumber) {
        return levelNumber % 5 === 0;
    };
    /**
     * 更新外观
     */
    LevelItemController.prototype.updateAppearance = function () {
        if (!this.levelSprite)
            return;
        var imagePath = "";
        var size = cc.size(46, 46);
        // 检查是否为特殊关卡（第5、10、15、20、25关）
        var isSpecialLevel = this.isSpecialLevel(this.levelNumber);
        var uiSuffix = isSpecialLevel ? "01" : "";
        // 根据状态和是否选中确定图片路径和大小
        if (this.isSelected) {
            size = cc.size(86, 86);
            switch (this.levelStatus) {
                case LevelSelectController_1.LevelStatus.LOCKED:
                    imagePath = "hall_page_res/Level_Btn/pop_gray" + uiSuffix + "_choose";
                    break;
                case LevelSelectController_1.LevelStatus.CURRENT:
                    imagePath = "hall_page_res/Level_Btn/pop_yellow" + uiSuffix + "_choose";
                    break;
                case LevelSelectController_1.LevelStatus.COMPLETED:
                    imagePath = "hall_page_res/Level_Btn/pop_green" + uiSuffix + "_choose";
                    break;
            }
        }
        else {
            switch (this.levelStatus) {
                case LevelSelectController_1.LevelStatus.LOCKED:
                    imagePath = "hall_page_res/Level_Btn/pop_gray" + uiSuffix;
                    break;
                case LevelSelectController_1.LevelStatus.CURRENT:
                    imagePath = "hall_page_res/Level_Btn/pop_yellow" + uiSuffix;
                    break;
                case LevelSelectController_1.LevelStatus.COMPLETED:
                    imagePath = "hall_page_res/Level_Btn/pop_green" + uiSuffix;
                    break;
            }
        }
        // 设置节点大小
        this.node.setContentSize(size);
        // 优化的图片加载：只有当图片路径改变时才重新加载
        if (this.currentImagePath !== imagePath) {
            this.loadSpriteFrameOptimized(imagePath);
            this.currentImagePath = imagePath;
        }
        // 设置按钮交互状态
        if (this.levelButton) {
            this.levelButton.interactable = (this.levelStatus !== LevelSelectController_1.LevelStatus.LOCKED);
        }
        // 设置标签样式
        this.setupLabelStyle();
    };
    /**
     * 设置选中状态
     */
    LevelItemController.prototype.setSelected = function (selected) {
        if (this.isSelected !== selected) {
            this.isSelected = selected;
            this.updateAppearance();
        }
    };
    /**
     * 获取关卡号
     */
    LevelItemController.prototype.getLevelNumber = function () {
        return this.levelNumber;
    };
    /**
     * 获取关卡状态
     */
    LevelItemController.prototype.getLevelStatus = function () {
        return this.levelStatus;
    };
    /**
     * 是否选中
     */
    LevelItemController.prototype.getIsSelected = function () {
        return this.isSelected;
    };
    /**
     * 设置标签样式
     */
    LevelItemController.prototype.setupLabelStyle = function () {
        if (!this.levelLabel) {
            cc.warn("LevelItemController: levelLabel is null");
            return;
        }
        // 设置字体大小为30
        this.levelLabel.fontSize = 30;
        // 设置颜色为白色 #FFFFFF
        this.levelLabel.node.color = cc.color(255, 255, 255);
        // 设置居中对齐
        this.levelLabel.horizontalAlign = cc.Label.HorizontalAlign.CENTER;
        this.levelLabel.verticalAlign = cc.Label.VerticalAlign.CENTER;
        // 注意：Cocos Creator的Label组件不支持直接设置加粗
        // 如需加粗效果，需要使用加粗字体文件
        // 添加外边框 LabelOutline
        var outline = this.levelLabel.getComponent(cc.LabelOutline);
        if (!outline) {
            outline = this.levelLabel.addComponent(cc.LabelOutline);
        }
        // 根据关卡状态设置边框颜色
        var outlineColor;
        switch (this.levelStatus) {
            case LevelSelectController_1.LevelStatus.LOCKED:
                // 未解锁边框为 #7B7B7B
                outlineColor = cc.color(123, 123, 123);
                break;
            case LevelSelectController_1.LevelStatus.CURRENT:
                // 当前玩到的关卡边框 #CF5800
                outlineColor = cc.color(207, 88, 0);
                break;
            case LevelSelectController_1.LevelStatus.COMPLETED:
                // 已解锁边框为 #119C0F
                outlineColor = cc.color(17, 156, 15);
                break;
            default:
                outlineColor = cc.color(123, 123, 123);
                break;
        }
        outline.color = outlineColor;
        outline.width = 1;
        // 确保标签位置居中
        this.levelLabel.node.setPosition(0, 0);
    };
    /**
     * 优化的图片加载方法：使用缓存避免重复加载
     * @param imagePath 图片路径
     */
    LevelItemController.prototype.loadSpriteFrameOptimized = function (imagePath) {
        var _this = this;
        // 先检查缓存
        var cachedSpriteFrame = LevelItemController_1.spriteFrameCache.get(imagePath);
        if (cachedSpriteFrame && this.levelSprite) {
            this.levelSprite.spriteFrame = cachedSpriteFrame;
            return;
        }
        // 缓存中没有，异步加载
        cc.resources.load(imagePath, cc.SpriteFrame, function (err, spriteFrame) {
            if (!err && spriteFrame && _this.levelSprite) {
                // 设置图片
                _this.levelSprite.spriteFrame = spriteFrame;
                // 缓存图片
                LevelItemController_1.spriteFrameCache.set(imagePath, spriteFrame);
            }
            else if (err) {
                cc.error("Failed to load sprite: " + imagePath, err);
            }
        });
    };
    /**
     * 关卡点击事件
     */
    LevelItemController.prototype.onLevelClick = function () {
        if (this.levelStatus === LevelSelectController_1.LevelStatus.LOCKED) {
            return;
        }
        // 发送关卡选择事件
        this.node.emit('level-selected', this.levelNumber);
    };
    var LevelItemController_1;
    // 性能优化：缓存已加载的资源
    LevelItemController.spriteFrameCache = new Map();
    __decorate([
        property(cc.Sprite)
    ], LevelItemController.prototype, "levelSprite", void 0);
    __decorate([
        property(cc.Label)
    ], LevelItemController.prototype, "levelLabel", void 0);
    __decorate([
        property(cc.Button)
    ], LevelItemController.prototype, "levelButton", void 0);
    LevelItemController = LevelItemController_1 = __decorate([
        ccclass
    ], LevelItemController);
    return LevelItemController;
}(cc.Component));
exports.default = LevelItemController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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