
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/hall/Level/LevelSelectExample.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'a8190B83eBMQKXGXtajqzw/', 'LevelSelectExample');
// scripts/hall/Level/LevelSelectExample.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var LevelSelectController_1 = require("./LevelSelectController");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
/**
 * 关卡选择使用示例
 * 这个脚本展示了如何快速创建一个关卡选择界面
 */
var LevelSelectExample = /** @class */ (function (_super) {
    __extends(LevelSelectExample, _super);
    function LevelSelectExample() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        // UI节点
        _this.scrollView = null;
        _this.content = null;
        _this.infoLabel = null;
        // 关卡数据
        _this.levelDataList = [];
        _this.currentSelectedLevel = 1;
        _this.totalLevels = 30;
        _this.levelItemWidth = 150;
        // 关卡节点列表
        _this.levelNodes = [];
        return _this;
    }
    LevelSelectExample.prototype.onLoad = function () {
        this.createUI();
        this.initLevelData();
        this.createLevelSelectUI();
    };
    LevelSelectExample.prototype.start = function () {
        this.scrollToLevel(this.currentSelectedLevel);
        this.updateInfoDisplay();
    };
    /**
     * 创建UI结构
     */
    LevelSelectExample.prototype.createUI = function () {
        // 创建背景
        var bg = new cc.Node("Background");
        bg.addComponent(cc.Sprite);
        bg.setContentSize(750, 1334);
        bg.color = cc.Color.BLACK;
        bg.parent = this.node;
        // 创建标题
        var titleNode = new cc.Node("Title");
        var titleLabel = titleNode.addComponent(cc.Label);
        titleLabel.string = "选择关卡";
        titleLabel.fontSize = 36;
        titleLabel.node.color = cc.Color.WHITE;
        titleNode.setPosition(0, 500);
        titleNode.parent = this.node;
        // 创建ScrollView
        var scrollViewNode = new cc.Node("ScrollView");
        this.scrollView = scrollViewNode.addComponent(cc.ScrollView);
        scrollViewNode.setContentSize(650, 150);
        scrollViewNode.setPosition(0, 0);
        scrollViewNode.parent = this.node;
        // 创建Viewport
        var viewport = new cc.Node("Viewport");
        viewport.addComponent(cc.Mask);
        viewport.setContentSize(650, 150);
        viewport.parent = scrollViewNode;
        // 创建Content
        this.content = new cc.Node("Content");
        this.content.setContentSize(650, 150);
        this.content.parent = viewport;
        // 设置ScrollView属性
        this.scrollView.content = this.content;
        this.scrollView.horizontal = true;
        this.scrollView.vertical = false;
        this.scrollView.inertia = true;
        this.scrollView.elastic = true;
        // 修复Scrollbar问题
        this.scrollView.horizontalScrollBar = null;
        this.scrollView.verticalScrollBar = null;
        // 创建信息标签
        var infoNode = new cc.Node("InfoLabel");
        this.infoLabel = infoNode.addComponent(cc.Label);
        this.infoLabel.string = "当前选中: 关卡1";
        this.infoLabel.fontSize = 24;
        this.infoLabel.node.color = cc.Color.WHITE;
        infoNode.setPosition(0, -200);
        infoNode.parent = this.node;
        // 创建测试按钮
        this.createTestButtons();
    };
    /**
     * 创建测试按钮
     */
    LevelSelectExample.prototype.createTestButtons = function () {
        var _this = this;
        // 完成关卡按钮
        var completeBtn = this.createButton("完成当前关卡", cc.v2(-150, -300), function () {
            _this.completeCurrentLevel();
        });
        // 重置按钮
        var resetBtn = this.createButton("重置关卡", cc.v2(0, -300), function () {
            _this.resetLevels();
        });
        // 随机进度按钮
        var randomBtn = this.createButton("随机进度", cc.v2(150, -300), function () {
            _this.setRandomProgress();
        });
    };
    /**
     * 创建按钮
     */
    LevelSelectExample.prototype.createButton = function (text, position, callback) {
        var btnNode = new cc.Node("Button");
        var button = btnNode.addComponent(cc.Button);
        var sprite = btnNode.addComponent(cc.Sprite);
        // 设置按钮外观
        btnNode.setContentSize(120, 40);
        btnNode.color = cc.Color.BLUE;
        btnNode.setPosition(position);
        btnNode.parent = this.node;
        // 添加文字
        var labelNode = new cc.Node("Label");
        var label = labelNode.addComponent(cc.Label);
        label.string = text;
        label.fontSize = 16;
        label.node.color = cc.Color.WHITE;
        labelNode.parent = btnNode;
        // 设置点击事件
        btnNode.on('click', callback, this);
        return btnNode;
    };
    /**
     * 初始化关卡数据
     */
    LevelSelectExample.prototype.initLevelData = function () {
        this.levelDataList = [];
        for (var i = 1; i <= this.totalLevels; i++) {
            var status = void 0;
            if (i === 1) {
                status = LevelSelectController_1.LevelStatus.CURRENT;
            }
            else if (i <= 3) {
                status = LevelSelectController_1.LevelStatus.COMPLETED;
            }
            else {
                status = LevelSelectController_1.LevelStatus.LOCKED;
            }
            this.levelDataList.push({
                levelNumber: i,
                status: status
            });
        }
    };
    /**
     * 创建关卡选择UI
     */
    LevelSelectExample.prototype.createLevelSelectUI = function () {
        if (!this.content)
            return;
        // 清空现有内容
        this.content.removeAllChildren();
        this.levelNodes = [];
        // 计算总宽度
        var totalWidth = (this.totalLevels - 1) * this.levelItemWidth + 650;
        this.content.width = totalWidth;
        for (var i = 0; i < this.totalLevels; i++) {
            var levelData = this.levelDataList[i];
            // 创建关卡节点
            var levelNode = this.createLevelNode(levelData);
            this.content.addChild(levelNode);
            this.levelNodes.push(levelNode);
            // 设置位置
            var posX = i * this.levelItemWidth - totalWidth / 2 + 650 / 2;
            levelNode.setPosition(posX, 0);
            // 创建连接线（除了最后一个关卡）
            if (i < this.totalLevels - 1) {
                var lineNode = this.createLineNode();
                this.content.addChild(lineNode);
                lineNode.setPosition(posX + this.levelItemWidth / 2, 0);
            }
        }
    };
    /**
     * 创建关卡节点
     */
    LevelSelectExample.prototype.createLevelNode = function (levelData) {
        var _this = this;
        var node = new cc.Node("Level_" + levelData.levelNumber);
        // 添加Sprite组件
        var sprite = node.addComponent(cc.Sprite);
        // 添加Label组件显示关卡数字
        var labelNode = new cc.Node("LevelLabel");
        var label = labelNode.addComponent(cc.Label);
        label.string = levelData.levelNumber.toString();
        label.fontSize = 20;
        label.node.color = cc.Color.WHITE;
        labelNode.parent = node;
        // 添加Button组件
        var button = node.addComponent(cc.Button);
        button.target = node;
        // 设置点击事件
        node.on('click', function () {
            _this.onLevelClicked(levelData.levelNumber);
        }, this);
        // 更新关卡外观
        this.updateLevelNodeAppearance(node, levelData, false);
        return node;
    };
    /**
     * 创建连接线节点
     */
    LevelSelectExample.prototype.createLineNode = function () {
        var node = new cc.Node("Line");
        var sprite = node.addComponent(cc.Sprite);
        node.setContentSize(6, 6);
        node.color = cc.Color.WHITE;
        return node;
    };
    /**
     * 更新关卡节点外观
     */
    LevelSelectExample.prototype.updateLevelNodeAppearance = function (node, levelData, isSelected) {
        var size = cc.size(46, 46);
        var color = cc.Color.GRAY;
        // 根据状态确定颜色和大小
        if (isSelected) {
            size = cc.size(86, 86);
        }
        switch (levelData.status) {
            case LevelSelectController_1.LevelStatus.LOCKED:
                color = cc.Color.GRAY;
                break;
            case LevelSelectController_1.LevelStatus.CURRENT:
                color = cc.Color.YELLOW;
                break;
            case LevelSelectController_1.LevelStatus.COMPLETED:
                color = cc.Color.GREEN;
                break;
        }
        // 设置节点大小和颜色
        node.setContentSize(size);
        node.color = color;
    };
    /**
     * 更新所有关卡显示
     */
    LevelSelectExample.prototype.updateAllLevelsDisplay = function () {
        for (var i = 0; i < this.levelNodes.length; i++) {
            var node = this.levelNodes[i];
            var levelData = this.levelDataList[i];
            var isSelected = (levelData.levelNumber === this.currentSelectedLevel);
            this.updateLevelNodeAppearance(node, levelData, isSelected);
        }
    };
    /**
     * 滚动到指定关卡
     */
    LevelSelectExample.prototype.scrollToLevel = function (levelNumber) {
        if (levelNumber < 1 || levelNumber > this.totalLevels)
            return;
        var targetIndex = levelNumber - 1;
        var contentWidth = this.content.width;
        var scrollViewWidth = this.scrollView.node.width;
        var targetOffset = (targetIndex * this.levelItemWidth) / (contentWidth - scrollViewWidth);
        var clampedOffset = cc.misc.clampf(targetOffset, 0, 1);
        this.scrollView.scrollToPercentHorizontal(clampedOffset, 0.3);
    };
    /**
     * 关卡点击事件处理
     */
    LevelSelectExample.prototype.onLevelClicked = function (levelNumber) {
        var levelData = this.levelDataList[levelNumber - 1];
        if (levelData.status === LevelSelectController_1.LevelStatus.LOCKED) {
            return;
        }
        this.currentSelectedLevel = levelNumber;
        this.updateAllLevelsDisplay();
        this.scrollToLevel(levelNumber);
        this.updateInfoDisplay();
    };
    /**
     * 更新信息显示
     */
    LevelSelectExample.prototype.updateInfoDisplay = function () {
        if (this.infoLabel) {
            var levelData = this.levelDataList[this.currentSelectedLevel - 1];
            var statusText = "";
            switch (levelData.status) {
                case LevelSelectController_1.LevelStatus.LOCKED:
                    statusText = "未解锁";
                    break;
                case LevelSelectController_1.LevelStatus.CURRENT:
                    statusText = "进行中";
                    break;
                case LevelSelectController_1.LevelStatus.COMPLETED:
                    statusText = "已通关";
                    break;
            }
            this.infoLabel.string = "\u5F53\u524D\u9009\u4E2D: \u5173\u5361" + this.currentSelectedLevel + " (" + statusText + ")";
        }
    };
    /**
     * 完成当前关卡
     */
    LevelSelectExample.prototype.completeCurrentLevel = function () {
        var levelData = this.levelDataList[this.currentSelectedLevel - 1];
        if (levelData.status === LevelSelectController_1.LevelStatus.CURRENT) {
            levelData.status = LevelSelectController_1.LevelStatus.COMPLETED;
            // 解锁下一关
            if (this.currentSelectedLevel < this.totalLevels) {
                var nextLevelData = this.levelDataList[this.currentSelectedLevel];
                if (nextLevelData.status === LevelSelectController_1.LevelStatus.LOCKED) {
                    nextLevelData.status = LevelSelectController_1.LevelStatus.CURRENT;
                }
            }
            this.updateAllLevelsDisplay();
            this.updateInfoDisplay();
        }
    };
    /**
     * 重置关卡
     */
    LevelSelectExample.prototype.resetLevels = function () {
        for (var i = 0; i < this.levelDataList.length; i++) {
            if (i === 0) {
                this.levelDataList[i].status = LevelSelectController_1.LevelStatus.CURRENT;
            }
            else {
                this.levelDataList[i].status = LevelSelectController_1.LevelStatus.LOCKED;
            }
        }
        this.currentSelectedLevel = 1;
        this.updateAllLevelsDisplay();
        this.scrollToLevel(1);
        this.updateInfoDisplay();
    };
    /**
     * 设置随机进度
     */
    LevelSelectExample.prototype.setRandomProgress = function () {
        var completedLevels = Math.floor(Math.random() * 10) + 1;
        var currentLevel = Math.min(completedLevels + 1, this.totalLevels);
        for (var i = 0; i < this.levelDataList.length; i++) {
            if (i < completedLevels) {
                this.levelDataList[i].status = LevelSelectController_1.LevelStatus.COMPLETED;
            }
            else if (i === completedLevels) {
                this.levelDataList[i].status = LevelSelectController_1.LevelStatus.CURRENT;
            }
            else {
                this.levelDataList[i].status = LevelSelectController_1.LevelStatus.LOCKED;
            }
        }
        this.currentSelectedLevel = currentLevel;
        this.updateAllLevelsDisplay();
        this.scrollToLevel(currentLevel);
        this.updateInfoDisplay();
    };
    LevelSelectExample = __decorate([
        ccclass
    ], LevelSelectExample);
    return LevelSelectExample;
}(cc.Component));
exports.default = LevelSelectExample;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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