
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/hall/Level/LevelSelectController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '40c9e3ykUFClLHG7s1cQEKm', 'LevelSelectController');
// scripts/hall/Level/LevelSelectController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LevelStatus = void 0;
var ScrollViewHelper_1 = require("./ScrollViewHelper");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
// 关卡状态枚举
var LevelStatus;
(function (LevelStatus) {
    LevelStatus[LevelStatus["LOCKED"] = 0] = "LOCKED";
    LevelStatus[LevelStatus["CURRENT"] = 1] = "CURRENT";
    LevelStatus[LevelStatus["COMPLETED"] = 2] = "COMPLETED"; // 已通关（绿色）
})(LevelStatus = exports.LevelStatus || (exports.LevelStatus = {}));
var LevelSelectController = /** @class */ (function (_super) {
    __extends(LevelSelectController, _super);
    function LevelSelectController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.scrollView = null;
        _this.content = null;
        // 关卡数据
        _this.levelDataList = [];
        _this.currentSelectedLevel = 1;
        _this.totalLevels = 30;
        _this.screenWidth = 650;
        _this.levelItemWidth = 150; // 关卡项的宽度（包括间距）
        _this.visibleLevels = 3; // 可见关卡数量
        // 连接线配置（公开，方便调试）
        _this.lineCount = 9; // 每两个关卡之间的连接线数量
        _this.lineSpacing = 16; // 连接线之间的间距
        _this.levelToLineDistance = 8; // 关卡到连接线的距离
        // 关卡节点列表
        _this.levelNodes = [];
        // 滑动状态标志
        _this.isAutoScrolling = false;
        // 关卡选择变化回调
        _this.onLevelSelectionChanged = null;
        // 性能优化相关
        _this.updateTimer = 0; // 防抖定时器
        _this.isUpdatingDisplay = false; // 防止重复更新显示
        return _this;
    }
    LevelSelectController.prototype.onLoad = function () {
        // 修复ScrollView的Scrollbar问题
        this.fixScrollViewScrollbar();
        this.initLevelData();
        this.createLevelItems();
        this.updateLevelDisplay();
        this.setupScrollEvents();
        // 设置初始滚动位置为第1关（最左边）
        if (this.scrollView) {
            this.scrollView.scrollToPercentHorizontal(0, 0);
        }
    };
    LevelSelectController.prototype.start = function () {
        var _this = this;
        // 延迟滚动到当前选中关卡，确保界面完全初始化
        this.scheduleOnce(function () {
            // 滚动到当前选中的关卡（可能已经通过ExtendLevelProgress设置了）
            _this.scrollToLevel(_this.currentSelectedLevel);
        }, 0.1);
    };
    LevelSelectController.prototype.onDestroy = function () {
        // 移除事件监听器
        if (this.scrollView) {
            this.scrollView.node.off('scrolling', this.onScrolling, this);
            this.scrollView.node.off('scroll-ended', this.onScrollEnded, this);
        }
    };
    /**
     * 初始化关卡数据
     */
    LevelSelectController.prototype.initLevelData = function () {
        this.levelDataList = [];
        // 初始化关卡状态：第1关为当前可玩关卡，其他为锁定状态
        for (var i = 1; i <= this.totalLevels; i++) {
            var status = void 0;
            if (i === 1) {
                status = LevelStatus.CURRENT; // 第1关默认为当前可玩关卡（显示开始游戏按钮）
            }
            else {
                status = LevelStatus.LOCKED; // 其他关卡锁定
            }
            this.levelDataList.push({
                levelNumber: i,
                status: status
            });
        }
        // 默认选中第一关
        this.currentSelectedLevel = 1;
    };
    /**
     * 创建关卡项目
     */
    LevelSelectController.prototype.createLevelItems = function () {
        if (!this.content) {
            cc.error("Content node is not assigned!");
            return;
        }
        // 清空现有内容
        this.content.removeAllChildren();
        this.levelNodes = [];
        // 计算总宽度
        var totalWidth = (this.totalLevels - 1) * this.levelItemWidth + this.screenWidth;
        this.content.width = totalWidth;
        for (var i = 0; i < this.totalLevels; i++) {
            var levelData = this.levelDataList[i];
            // 创建关卡节点
            var levelNode = this.createLevelNode(levelData);
            this.content.addChild(levelNode);
            this.levelNodes.push(levelNode);
            // 设置位置
            var posX = i * this.levelItemWidth - totalWidth / 2 + this.screenWidth / 2;
            levelNode.setPosition(posX, 0);
            // 创建连接线（除了最后一个关卡）
            if (i < this.totalLevels - 1) {
                this.createConnectionLines(i, posX);
            }
        }
    };
    /**
     * 创建关卡节点
     */
    LevelSelectController.prototype.createLevelNode = function (levelData) {
        var node = new cc.Node("Level_" + levelData.levelNumber);
        // 添加Sprite组件
        var sprite = node.addComponent(cc.Sprite);
        // 添加Label组件显示关卡数字
        var labelNode = new cc.Node("LevelLabel");
        var label = labelNode.addComponent(cc.Label);
        label.string = levelData.levelNumber.toString();
        // 设置字体样式
        label.fontSize = 30;
        label.node.color = cc.color(255, 255, 255); // #FFFFFF
        label.horizontalAlign = cc.Label.HorizontalAlign.CENTER;
        label.verticalAlign = cc.Label.VerticalAlign.CENTER;
        // 添加外边框
        var outline = label.addComponent(cc.LabelOutline);
        this.updateLabelOutline(outline, levelData.status);
        labelNode.parent = node;
        labelNode.setPosition(0, 0); // 居中对齐
        // 添加Button组件
        var button = node.addComponent(cc.Button);
        button.target = node;
        // 设置点击事件
        var eventHandler = new cc.Component.EventHandler();
        eventHandler.target = this.node;
        eventHandler.component = "LevelSelectController";
        eventHandler.handler = "onLevelClicked";
        eventHandler.customEventData = levelData.levelNumber.toString();
        button.clickEvents.push(eventHandler);
        // 更新关卡外观
        this.updateLevelNodeAppearance(node, levelData, false);
        return node;
    };
    /**
     * 创建连接线组（9个连接线）
     */
    LevelSelectController.prototype.createConnectionLines = function (levelIndex, levelPosX) {
        var startX = levelPosX + 46 / 2 + this.levelToLineDistance; // 关卡右边缘 + 距离
        var totalLineWidth = (this.lineCount - 1) * this.lineSpacing;
        var endX = levelPosX + this.levelItemWidth - 46 / 2 - this.levelToLineDistance; // 下一个关卡左边缘 - 距离
        var availableWidth = endX - startX;
        // 如果可用宽度小于需要的宽度，调整间距
        var actualSpacing = this.lineSpacing;
        if (totalLineWidth > availableWidth) {
            actualSpacing = availableWidth / (this.lineCount - 1);
        }
        for (var i = 0; i < this.lineCount; i++) {
            var lineNode = this.createSingleLineNode();
            this.content.addChild(lineNode);
            var lineX = startX + i * actualSpacing;
            lineNode.setPosition(lineX, 0);
        }
    };
    /**
     * 创建单个连接线节点
     */
    LevelSelectController.prototype.createSingleLineNode = function () {
        var node = new cc.Node("Line");
        var sprite = node.addComponent(cc.Sprite);
        // 设置连接线大小为6*6
        node.setContentSize(6, 6);
        // 加载连接线图片
        cc.resources.load("hall_page_res/Level_Btn/pop_line", cc.SpriteFrame, function (err, spriteFrame) {
            if (!err && spriteFrame) {
                sprite.spriteFrame = spriteFrame;
                // 图片加载后重新设置大小为6x6，确保不被图片原始大小覆盖
                node.setContentSize(6, 6);
            }
        });
        return node;
    };
    /**
     * 检查是否为特殊关卡（第5、10、15、20、25关）
     */
    LevelSelectController.prototype.isSpecialLevel = function (levelNumber) {
        return levelNumber % 5 === 0;
    };
    /**
     * 更新关卡节点外观
     */
    LevelSelectController.prototype.updateLevelNodeAppearance = function (node, levelData, isSelected) {
        var sprite = node.getComponent(cc.Sprite);
        if (!sprite)
            return;
        var imagePath = "";
        var size = cc.size(46, 46);
        // 检查是否为特殊关卡（第5、10、15、20、25关）
        var isSpecialLevel = this.isSpecialLevel(levelData.levelNumber);
        var uiSuffix = isSpecialLevel ? "01" : "";
        // 根据状态和是否选中确定图片路径
        if (isSelected) {
            size = cc.size(86, 86);
            switch (levelData.status) {
                case LevelStatus.LOCKED:
                    imagePath = "hall_page_res/Level_Btn/pop_gray" + uiSuffix + "_choose";
                    break;
                case LevelStatus.CURRENT:
                    imagePath = "hall_page_res/Level_Btn/pop_yellow" + uiSuffix + "_choose";
                    break;
                case LevelStatus.COMPLETED:
                    imagePath = "hall_page_res/Level_Btn/pop_green" + uiSuffix + "_choose";
                    break;
            }
        }
        else {
            switch (levelData.status) {
                case LevelStatus.LOCKED:
                    imagePath = "hall_page_res/Level_Btn/pop_gray" + uiSuffix;
                    break;
                case LevelStatus.CURRENT:
                    imagePath = "hall_page_res/Level_Btn/pop_yellow" + uiSuffix;
                    break;
                case LevelStatus.COMPLETED:
                    imagePath = "hall_page_res/Level_Btn/pop_green" + uiSuffix;
                    break;
            }
        }
        // 设置节点大小
        node.setContentSize(size);
        // 加载并设置图片
        cc.resources.load(imagePath, cc.SpriteFrame, function (err, spriteFrame) {
            if (!err && spriteFrame) {
                sprite.spriteFrame = spriteFrame;
                // 图片加载后强制设置正确的大小，覆盖图片原始大小
                node.setContentSize(size);
            }
        });
        // 更新标签外边框
        var labelNode = node.getChildByName("LevelLabel");
        if (labelNode) {
            var label = labelNode.getComponent(cc.Label);
            if (label) {
                var outline = label.getComponent(cc.LabelOutline);
                if (outline) {
                    this.updateLabelOutline(outline, levelData.status);
                }
            }
        }
    };
    /**
     * 更新关卡显示（优化版本）
     */
    LevelSelectController.prototype.updateLevelDisplay = function () {
        var _this = this;
        // 防止重复更新
        if (this.isUpdatingDisplay) {
            return;
        }
        this.isUpdatingDisplay = true;
        // 使用requestAnimationFrame优化渲染
        cc.director.getScheduler().schedule(function () {
            for (var i = 0; i < _this.levelNodes.length; i++) {
                var node = _this.levelNodes[i];
                var levelData = _this.levelDataList[i];
                var isSelected = (levelData.levelNumber === _this.currentSelectedLevel);
                _this.updateLevelNodeAppearance(node, levelData, isSelected);
            }
            _this.isUpdatingDisplay = false;
        }, this, 0, 0, 0, false);
    };
    /**
     * 滚动到指定关卡
     */
    LevelSelectController.prototype.scrollToLevel = function (levelNumber) {
        if (levelNumber < 1 || levelNumber > this.totalLevels)
            return;
        // 设置自动滚动标志，防止滚动过程中触发位置更新
        this.isAutoScrolling = true;
        var targetIndex = levelNumber - 1;
        var contentWidth = this.content.width;
        var scrollViewWidth = this.scrollView.node.width;
        // 计算目标位置的偏移量
        var targetOffset = (targetIndex * this.levelItemWidth) / (contentWidth - scrollViewWidth);
        // 限制偏移量在有效范围内
        var clampedOffset = cc.misc.clampf(targetOffset, 0, 1);
        // 设置滚动位置
        this.scrollView.scrollToPercentHorizontal(clampedOffset, 0.3);
    };
    /**
     * 关卡点击事件处理（优化版本）
     */
    LevelSelectController.prototype.onLevelClicked = function (event, customEventData) {
        var _this = this;
        var levelNumber = parseInt(customEventData);
        // 如果点击的是当前已选中的关卡，直接返回
        if (this.currentSelectedLevel === levelNumber) {
            return;
        }
        // 清除之前的防抖定时器
        if (this.updateTimer > 0) {
            clearTimeout(this.updateTimer);
        }
        // 使用防抖优化，避免频繁点击造成的性能问题
        this.updateTimer = setTimeout(function () {
            // 更新当前选中关卡
            _this.currentSelectedLevel = levelNumber;
            _this.updateLevelDisplay();
            // 滚动到选中关卡
            _this.isAutoScrolling = true;
            _this.scrollToLevel(levelNumber);
            // 通知关卡选择变化
            if (_this.onLevelSelectionChanged) {
                _this.onLevelSelectionChanged(levelNumber);
            }
        }, 50); // 50ms防抖延迟
    };
    /**
     * 设置关卡状态
     */
    LevelSelectController.prototype.setLevelStatus = function (levelNumber, status) {
        if (levelNumber < 1 || levelNumber > this.totalLevels)
            return;
        this.levelDataList[levelNumber - 1].status = status;
        this.updateLevelDisplay();
    };
    /**
     * 获取当前选中的关卡
     */
    LevelSelectController.prototype.getCurrentSelectedLevel = function () {
        return this.currentSelectedLevel;
    };
    /**
     * 获取指定关卡的数据
     */
    LevelSelectController.prototype.getLevelData = function (levelNumber) {
        if (levelNumber < 1 || levelNumber > this.totalLevels)
            return null;
        return this.levelDataList[levelNumber - 1];
    };
    /**
     * 设置关卡进度（从外部调用，比如从后端获取数据后）
     * @param levelProgressData 关卡进度数据，包含clearedLevels, currentLevel, totalLevels
     */
    LevelSelectController.prototype.setLevelProgress = function (levelProgressData) {
        if (!levelProgressData) {
            cc.warn("❌ levelProgressData 为空");
            return;
        }
        var clearedLevels = levelProgressData.clearedLevels, currentLevel = levelProgressData.currentLevel, totalLevels = levelProgressData.totalLevels;
        // 验证数据有效性
        if (clearedLevels < 0 || currentLevel < 1 || currentLevel > totalLevels) {
            cc.warn("\u274C \u6570\u636E\u9A8C\u8BC1\u5931\u8D25: clearedLevels=" + clearedLevels + ", currentLevel=" + currentLevel + ", totalLevels=" + totalLevels);
            return;
        }
        // 更新总关卡数（如果后端传了的话）
        if (totalLevels && totalLevels > 0) {
            this.totalLevels = totalLevels;
        }
        // 重新设置所有关卡状态
        for (var i = 1; i <= this.totalLevels; i++) {
            var status = void 0;
            if (i < currentLevel) {
                status = LevelStatus.COMPLETED; // 当前关卡之前的都是已完成（绿色）
            }
            else if (i === currentLevel) {
                status = LevelStatus.CURRENT; // 当前关卡（黄色选择UI）
            }
            else {
                status = LevelStatus.LOCKED; // 当前关卡之后的都是未解锁（灰色）
            }
            this.levelDataList[i - 1].status = status;
        }
        // 更新当前选中关卡为后端指定的currentLevel
        this.currentSelectedLevel = currentLevel;
        this.updateLevelDisplay();
        // scrollToLevel 方法内部会设置 isAutoScrolling = true
        this.scrollToLevel(currentLevel);
        // 通知关卡选择变化
        if (this.onLevelSelectionChanged) {
            this.onLevelSelectionChanged(currentLevel);
        }
    };
    /**
     * 设置关卡进度（兼容旧接口）
     * @param completedLevels 已完成的关卡数
     */
    LevelSelectController.prototype.setLevelProgressLegacy = function (completedLevels) {
        // 转换为新的数据格式
        var levelProgressData = {
            clearedLevels: completedLevels,
            currentLevel: Math.min(completedLevels + 1, this.totalLevels),
            totalLevels: this.totalLevels
        };
        this.setLevelProgress(levelProgressData);
    };
    /**
     * 解锁下一关
     */
    LevelSelectController.prototype.unlockNextLevel = function () {
        for (var i = 0; i < this.levelDataList.length; i++) {
            if (this.levelDataList[i].status === LevelStatus.LOCKED) {
                this.levelDataList[i].status = LevelStatus.CURRENT;
                this.updateLevelDisplay();
                break;
            }
        }
    };
    /**
     * 完成当前关卡
     */
    LevelSelectController.prototype.completeCurrentLevel = function () {
        var currentLevelData = this.levelDataList[this.currentSelectedLevel - 1];
        if (currentLevelData.status === LevelStatus.CURRENT) {
            currentLevelData.status = LevelStatus.COMPLETED;
            this.unlockNextLevel();
            this.updateLevelDisplay();
        }
    };
    /**
     * 设置滑动事件监听
     */
    LevelSelectController.prototype.setupScrollEvents = function () {
        if (!this.scrollView)
            return;
        // 监听滑动中事件
        this.scrollView.node.on('scrolling', this.onScrolling, this);
        // 监听滑动结束事件
        this.scrollView.node.on('scroll-ended', this.onScrollEnded, this);
    };
    /**
     * 滑动中事件处理
     */
    LevelSelectController.prototype.onScrolling = function () {
        // 如果是自动滚动，不要更新选中关卡
        if (this.isAutoScrolling) {
            return;
        }
        this.updateSelectedLevelByPosition();
    };
    /**
     * 滑动结束事件处理
     */
    LevelSelectController.prototype.onScrollEnded = function () {
        // 如果是自动滚动触发的事件，忽略
        if (this.isAutoScrolling) {
            this.isAutoScrolling = false;
            return;
        }
        this.updateSelectedLevelByPosition();
        // 滑动结束后，将选中的关卡固定居中
        this.isAutoScrolling = true;
        this.scrollToLevel(this.currentSelectedLevel);
    };
    /**
     * 根据当前位置更新选中的关卡
     */
    LevelSelectController.prototype.updateSelectedLevelByPosition = function () {
        if (!this.scrollView || !this.content)
            return;
        // 获取ScrollView的中心位置（相对于ScrollView节点）
        var scrollViewCenterX = 0; // ScrollView的中心就是x=0
        // 找到最接近ScrollView中心位置的关卡
        var closestLevel = 1;
        var minDistance = Number.MAX_VALUE;
        for (var i = 0; i < this.levelNodes.length; i++) {
            var levelNode = this.levelNodes[i];
            // 将关卡节点的本地坐标转换为ScrollView坐标系
            var levelWorldPos = this.content.convertToWorldSpaceAR(levelNode.position);
            var levelScrollViewPos = this.scrollView.node.convertToNodeSpaceAR(levelWorldPos);
            // 计算关卡与ScrollView中心的距离
            var distance = Math.abs(levelScrollViewPos.x - scrollViewCenterX);
            if (distance < minDistance) {
                minDistance = distance;
                closestLevel = i + 1;
            }
        }
        // 如果选中的关卡发生变化，更新显示
        if (closestLevel !== this.currentSelectedLevel) {
            this.currentSelectedLevel = closestLevel;
            this.updateLevelDisplay();
            // 通知关卡选择变化
            if (this.onLevelSelectionChanged) {
                this.onLevelSelectionChanged(closestLevel);
            }
        }
    };
    /**
     * 修复ScrollView的Scrollbar问题
     */
    LevelSelectController.prototype.fixScrollViewScrollbar = function () {
        if (this.scrollView && this.content) {
            ScrollViewHelper_1.ScrollViewHelper.setupHorizontalScrollView(this.scrollView, this.content);
            ScrollViewHelper_1.ScrollViewHelper.debugScrollView(this.scrollView, "LevelSelectController");
        }
    };
    /**
     * 调试参数信息
     */
    LevelSelectController.prototype.debugParameters = function () {
        // 计算连接线总宽度
        var totalLineWidth = (this.lineCount - 1) * this.lineSpacing;
        // 计算可用宽度
        var availableWidth = this.levelItemWidth - 46 - (this.levelToLineDistance * 2);
        if (totalLineWidth > availableWidth) {
            var adjustedSpacing = availableWidth / (this.lineCount - 1);
        }
        else {
        }
    };
    /**
     * 更新标签外边框
     */
    LevelSelectController.prototype.updateLabelOutline = function (outline, status) {
        var outlineColor;
        switch (status) {
            case LevelStatus.LOCKED:
                // 未解锁边框为 #7B7B7B
                outlineColor = cc.color(123, 123, 123);
                break;
            case LevelStatus.CURRENT:
                // 当前玩到的关卡边框 #CF5800
                outlineColor = cc.color(207, 88, 0);
                break;
            case LevelStatus.COMPLETED:
                // 已解锁边框为 #119C0F
                outlineColor = cc.color(17, 156, 15);
                break;
            default:
                outlineColor = cc.color(123, 123, 123);
                break;
        }
        outline.color = outlineColor;
        outline.width = 1;
    };
    /**
     * 重新创建关卡项目（用于参数调整后刷新）
     */
    LevelSelectController.prototype.refreshLevelItems = function () {
        this.createLevelItems();
        this.updateLevelDisplay();
        this.scrollToLevel(this.currentSelectedLevel);
    };
    __decorate([
        property(cc.ScrollView)
    ], LevelSelectController.prototype, "scrollView", void 0);
    __decorate([
        property(cc.Node)
    ], LevelSelectController.prototype, "content", void 0);
    LevelSelectController = __decorate([
        ccclass
    ], LevelSelectController);
    return LevelSelectController;
}(cc.Component));
exports.default = LevelSelectController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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