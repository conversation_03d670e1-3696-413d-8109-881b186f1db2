
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/hall/Level/LevelSelectPageController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'eed91BOnAtLKYkInYtuL76o', 'LevelSelectPageController');
// scripts/hall/Level/LevelSelectPageController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var GlobalManagerController_1 = require("../../GlobalManagerController");
var LevelSelectController_1 = require("./LevelSelectController");
var MessageId_1 = require("../../net/MessageId");
var WebSocketManager_1 = require("../../net/WebSocketManager");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var LevelSelectPageController = /** @class */ (function (_super) {
    __extends(LevelSelectPageController, _super);
    function LevelSelectPageController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.levelSelectController = null;
        _this.scrollView = null;
        // 新增的游戏按钮
        _this.startGameButton = null;
        _this.lockedButton = null;
        return _this;
    }
    LevelSelectPageController.prototype.onLoad = function () {
        // 修复ScrollView的Scrollbar问题
        this.fixScrollViewScrollbar();
        // 设置按钮初始状态 - 默认显示开始游戏按钮，避免状态切换闪烁
        if (this.startGameButton) {
            this.startGameButton.node.active = true;
            this.startGameButton.node.on('click', this.onStartGameButtonClick, this);
        }
        if (this.lockedButton) {
            this.lockedButton.node.active = false;
            this.lockedButton.node.on('click', this.onLockedButtonClick, this);
        }
    };
    LevelSelectPageController.prototype.start = function () {
        var _this = this;
        // 设置关卡选择变化回调
        if (this.levelSelectController) {
            this.levelSelectController.onLevelSelectionChanged = function (levelNumber) {
                _this.onLevelSelectionChanged(levelNumber);
            };
        }
        // 延迟更新UI，确保在关卡数据加载后再更新按钮状态
        this.scheduleOnce(function () {
            _this.updateUIDisplay();
        }, 0.1);
    };
    /**
     * 更新UI显示状态
     */
    LevelSelectPageController.prototype.updateUIDisplay = function () {
        // 更新游戏按钮状态
        this.updateGameButtons();
    };
    /**
     * 进入选中的关卡
     */
    LevelSelectPageController.prototype.enterSelectedLevel = function () {
        if (this.levelSelectController) {
            var selectedLevel_1 = this.levelSelectController.getCurrentSelectedLevel();
            // 尝试多种方式获取全局管理器
            var globalManager_1 = null;
            // 方法1: 尝试查找 global_node 节点（根目录）
            var globalManagerNode = cc.find("global_node");
            if (globalManagerNode) {
                globalManager_1 = globalManagerNode.getComponent(GlobalManagerController_1.default);
            }
            // 方法1.1: 尝试查找 Canvas/global_node 节点
            if (!globalManager_1) {
                globalManagerNode = cc.find("Canvas/global_node");
                if (globalManagerNode) {
                    globalManager_1 = globalManagerNode.getComponent(GlobalManagerController_1.default);
                    if (globalManager_1) {
                    }
                    else {
                        // 列出节点上的所有组件
                        var components = globalManagerNode.getComponents(cc.Component);
                    }
                }
            }
            if (globalManager_1) {
                // 先切换到关卡页面
                globalManager_1.setCurrentPage(GlobalManagerController_1.PageType.LEVEL_PAGE);
                // 延迟设置关卡，确保页面切换完成后再设置
                this.scheduleOnce(function () {
                    if (globalManager_1.levelPageController) {
                        globalManager_1.levelPageController.setCurrentLevel(selectedLevel_1);
                        // 发送ExtendLevelInfo消息到后端获取关卡详细信息
                        var request = {
                            levelId: selectedLevel_1
                        };
                        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeExtendLevelInfo, request);
                    }
                }, 0.1);
            }
            else {
                cc.error("无法找到 GlobalManagerController 组件！请检查场景配置。");
                cc.error("请确保场景中有节点挂载了 GlobalManagerController 组件。");
            }
        }
    };
    /**
     * 设置关卡进度（从外部调用）
     * @param levelProgressData 关卡进度数据，包含clearedLevels, currentLevel, totalLevels
     */
    LevelSelectPageController.prototype.setLevelProgress = function (levelProgressData) {
        var _this = this;
        if (!this.levelSelectController)
            return;
        // 使用 LevelSelectController 的新方法来设置关卡进度
        this.levelSelectController.setLevelProgress(levelProgressData);
        // 立即更新UI显示，确保按钮状态正确
        this.scheduleOnce(function () {
            _this.updateUIDisplay();
        }, 0.05); // 很短的延迟，确保关卡数据已经更新
    };
    /**
     * 修复ScrollView的Scrollbar问题
     */
    LevelSelectPageController.prototype.fixScrollViewScrollbar = function () {
        // 如果有ScrollView引用，清除Scrollbar引用以避免错误
        if (this.scrollView) {
            this.scrollView.horizontalScrollBar = null;
            this.scrollView.verticalScrollBar = null;
        }
        // 如果levelSelectController有ScrollView，也进行修复
        if (this.levelSelectController && this.levelSelectController.scrollView) {
            this.levelSelectController.scrollView.horizontalScrollBar = null;
            this.levelSelectController.scrollView.verticalScrollBar = null;
        }
    };
    /**
     * 更新游戏按钮显示状态
     */
    LevelSelectPageController.prototype.updateGameButtons = function () {
        if (!this.levelSelectController)
            return;
        var currentLevel = this.levelSelectController.getCurrentSelectedLevel();
        var levelData = this.levelSelectController.getLevelData(currentLevel);
        if (!levelData)
            return;
        // 根据关卡状态显示不同的按钮
        var isLocked = levelData.status === LevelSelectController_1.LevelStatus.LOCKED;
        if (this.startGameButton) {
            this.startGameButton.node.active = !isLocked;
            // 设置按钮文本 - 统一显示"开始游戏"
            var buttonLabel = this.startGameButton.getComponentInChildren(cc.Label);
            if (buttonLabel) {
                buttonLabel.string = "开始游戏";
            }
        }
        if (this.lockedButton) {
            this.lockedButton.node.active = isLocked;
        }
    };
    /**
     * 开始游戏按钮点击事件
     */
    LevelSelectPageController.prototype.onStartGameButtonClick = function () {
        if (!this.levelSelectController)
            return;
        var currentLevel = this.levelSelectController.getCurrentSelectedLevel();
        var levelData = this.levelSelectController.getLevelData(currentLevel);
        if (levelData && levelData.status !== LevelSelectController_1.LevelStatus.LOCKED) {
            // 这里添加进入游戏的逻辑
            this.enterSelectedLevel();
        }
    };
    /**
     * 未解锁按钮点击事件
     */
    LevelSelectPageController.prototype.onLockedButtonClick = function () {
        var currentLevel = this.levelSelectController.getCurrentSelectedLevel();
        // 这里可以添加提示用户关卡未解锁的逻辑
        // 例如显示提示弹窗等
    };
    /**
     * 关卡选择变化回调
     */
    LevelSelectPageController.prototype.onLevelSelectionChanged = function (levelNumber) {
        this.updateUIDisplay();
    };
    __decorate([
        property(LevelSelectController_1.default)
    ], LevelSelectPageController.prototype, "levelSelectController", void 0);
    __decorate([
        property(cc.ScrollView)
    ], LevelSelectPageController.prototype, "scrollView", void 0);
    __decorate([
        property(cc.Button)
    ], LevelSelectPageController.prototype, "startGameButton", void 0);
    __decorate([
        property(cc.Button)
    ], LevelSelectPageController.prototype, "lockedButton", void 0);
    LevelSelectPageController = __decorate([
        ccclass
    ], LevelSelectPageController);
    return LevelSelectPageController;
}(cc.Component));
exports.default = LevelSelectPageController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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