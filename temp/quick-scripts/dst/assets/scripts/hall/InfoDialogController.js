
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/hall/InfoDialogController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '46d58hnp4tLf7B+lcQOkRXG', 'InfoDialogController');
// scripts/hall/InfoDialogController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Config_1 = require("../util/Config");
var Tools_1 = require("../util/Tools");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
//游戏道具介绍页面
var InfoDialogController = /** @class */ (function (_super) {
    __extends(InfoDialogController, _super);
    function InfoDialogController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.boardBg = null;
        _this.boardBtnClose = null;
        _this.contentLay = null;
        // 新增：单机规则和联机规则文字按钮
        _this.danjiLabel = null;
        _this.lianjiLabel = null;
        // 新增：可移动的视觉效果按钮（不需要点击）
        _this.switchButton = null;
        // 新增：单机和联机规则的ScrollView
        _this.danjiScrollView = null;
        _this.duorenScrollView = null;
        _this.infoItem = null;
        _this.infoItem1 = null;
        _this.infoImage1 = null;
        _this.infoImage2 = null;
        _this.infoImage3 = null;
        _this.infoImage4 = null;
        _this.infoImage5 = null;
        _this.infoImage6 = null;
        _this.infoImage7 = null;
        _this.infoImage8 = null;
        _this.infoImage9 = null;
        _this.infoImage10 = null;
        _this.infoImage11 = null;
        _this.titleList = []; //title 的列表
        _this.tipsList = [];
        _this.generationMethodList = [];
        _this.permanentList = [];
        _this.randomList = [];
        _this.chainsList = [];
        _this.iceList = [];
        _this.scoringDetails = [];
        _this.infoImageList = [];
        _this.backCallback = null; //隐藏弹窗的回调
        // 新增：当前选中的规则类型（0: 单机规则, 1: 联机规则）
        _this.currentRuleType = 0;
        // 新增：动画持续时间
        _this.animationDuration = 0.3;
        return _this;
        // update (dt) {}
    }
    InfoDialogController.prototype.onLoad = function () {
        //     this.infoImageList = [
        //         this.infoImage3,
        //         this.infoImage4,
        //         this.infoImage5,
        //         this.infoImage6,
        //         this.infoImage7,
        //         this.infoImage8,
        //         this.infoImage9,
        //         this.infoImage10,
        //         this.infoImage11,
        //     ]
        //     this.titleList = [
        //         window.getLocalizedStr('Tips'),
        //         window.getLocalizedStr('Generation_Method'),
        //         window.getLocalizedStr('Permanent_Task'),
        //         window.getLocalizedStr('Random_Task'),
        //         window.getLocalizedStr('Chains'),
        //         window.getLocalizedStr('Ice_Blocks'),
        //         window.getLocalizedStr('Scoring_Details'),
        //     ]//title 的列表
        //     this. tipsList = [
        //         window.getLocalizedStr('Tips1'),
        //         window.getLocalizedStr('Tips2'),
        //         window.getLocalizedStr('Tips3'),
        //         window.getLocalizedStr('Tips4'),
        //         window.getLocalizedStr('Tips5'),
        //     ]
        //     this.generationMethodList = [
        //         window.getLocalizedStr('Generation_Method1'),
        //         window.getLocalizedStr('Generation_Method2'),
        //         window.getLocalizedStr('Generation_Method3'),
        //         window.getLocalizedStr('Generation_Method4'),
        //         window.getLocalizedStr('Generation_Method5'),
        //         window.getLocalizedStr('Generation_Method6'),
        //         window.getLocalizedStr('Generation_Method7'),
        //         window.getLocalizedStr('Generation_Method8'),
        //         window.getLocalizedStr('Generation_Method9'),
        //     ]
        //     this.permanentList = [
        //         window.getLocalizedStr('Permanent_Task1'),
        //     ]
        //     this.randomList = [
        //         window.getLocalizedStr('Random_Task1'),
        //         window.getLocalizedStr('Random_Task2'),
        //         window.getLocalizedStr('Random_Task3'),
        //         window.getLocalizedStr('Random_Task4'),
        //         window.getLocalizedStr('Random_Task5'),
        //         window.getLocalizedStr('Random_Task6'),
        //         window.getLocalizedStr('Random_Task7'),
        //     ]
        //     this.chainsList = [
        //         window.getLocalizedStr('Chains1'),
        //         window.getLocalizedStr('Chains2'),
        //         window.getLocalizedStr('Chains3'),
        //     ]
        //     this.iceList = [
        //         window.getLocalizedStr('Ice_Blocks1'),
        //         window.getLocalizedStr('Ice_Blocks2'),
        //         window.getLocalizedStr('Ice_Blocks3'),
        //     ]
        //     this.scoringDetails = [
        //         window.getLocalizedStr('Scoring_Details1'),
        //         window.getLocalizedStr('Scoring_Details2'),
        //         window.getLocalizedStr('Scoring_Details3'),
        //         window.getLocalizedStr('Scoring_Details4'),
        //         window.getLocalizedStr('Scoring_Details5'),
        //         window.getLocalizedStr('Scoring_Details6'),
        //         window.getLocalizedStr('Scoring_Details7'),
        //         window.getLocalizedStr('Scoring_Details8'),
        //         window.getLocalizedStr('Scoring_Details9'),
        //         window.getLocalizedStr('Scoring_Details10'),
        //         window.getLocalizedStr('Scoring_Details11'),
        //     ]
    };
    InfoDialogController.prototype.start = function () {
        var _this = this;
        Tools_1.Tools.imageButtonClick(this.boardBtnClose, Config_1.Config.buttonRes + 'board_btn_close_normal', Config_1.Config.buttonRes + 'board_btn_close_pressed', function () {
            _this.hide();
        });
        // 新增：设置单机规则文字点击事件
        if (this.danjiLabel) {
            Tools_1.Tools.setTouchEvent(this.danjiLabel, function () {
                _this.switchToRuleType(0); // 切换到单机规则
            });
        }
        // 新增：设置联机规则文字点击事件
        if (this.lianjiLabel) {
            Tools_1.Tools.setTouchEvent(this.lianjiLabel, function () {
                _this.switchToRuleType(1); // 切换到联机规则
            });
        }
        // 新增：初始化显示状态（默认显示单机规则）
        this.initializeRuleDisplay();
        //     this.contentLay.removeAllChildren()
        //     this.getTitleNode(this.titleList[0])
        //     this.tipsList.forEach((title, index) => {
        //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体
        //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)
        //         infoItemOneController.setData(title)
        //         this.contentLay.addChild(infoItem)
        //     })
        //     this.getTitleNode(this.titleList[1])
        //     this.generationMethodList.forEach((title, index) => {
        //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体
        //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)
        //         infoItemOneController.setData(title)
        //         let infoImg = cc.instantiate(this.infoImageList[index])
        //         infoItemOneController.setimgNode(infoImg)
        //         this.contentLay.addChild(infoItem)
        //     })
        //     this.getTitleNode(this.titleList[2])
        //     this.permanentList.forEach((title, index) => {
        //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体
        //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)
        //         infoItemOneController.setData(title)
        //         this.contentLay.addChild(infoItem)
        //     })
        //     let infoImg1 = cc.instantiate(this.infoImage1)
        //     this.contentLay.addChild(infoImg1)
        //     this.getTitleNode(this.titleList[3])
        //     this.randomList.forEach((title, index) => {
        //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体
        //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)
        //         infoItemOneController.setData(title)
        //         this.contentLay.addChild(infoItem)
        //     })
        //     let infoImg2 = cc.instantiate(this.infoImage2)
        //     this.contentLay.addChild(infoImg2)
        //     this.getTitleNode(this.titleList[4])
        //     this.chainsList.forEach((title, index) => {
        //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体
        //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)
        //         infoItemOneController.setData(title)
        //         this.contentLay.addChild(infoItem)
        //     })
        //     this.getTitleNode(this.titleList[5])
        //     this.iceList.forEach((title, index) => {
        //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体
        //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)
        //         infoItemOneController.setData(title)
        //         this.contentLay.addChild(infoItem)
        //     })
        //     this.getTitleNode(this.titleList[6])
        //     this.scoringDetails.forEach((title, index) => {
        //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体
        //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)
        //         infoItemOneController.setData(title)
        //         this.contentLay.addChild(infoItem)
        //     })
        // }
        // getTitleNode(title: string) {
        //     let infoItem = cc.instantiate(this.infoItem);//初始化一个预制体
        //     let infoItemController = infoItem.getComponent(InfoItemController)
        //     infoItemController.setContent(title)
        //     this.contentLay.addChild(infoItem)
        // }
    };
    /**
     * 新增：初始化规则显示状态
     */
    InfoDialogController.prototype.initializeRuleDisplay = function () {
        if (this.danjiScrollView && this.duorenScrollView) {
            // 默认显示单机规则，隐藏联机规则
            this.danjiScrollView.active = true;
            this.duorenScrollView.active = false;
            this.currentRuleType = 0;
            // 设置按钮初始位置（左边位置）
            if (this.switchButton) {
                this.switchButton.position = cc.v3(-150, -2, 0);
            }
        }
    };
    /**
     * 新增：切换到指定规则类型
     * @param ruleType 0: 单机规则, 1: 联机规则
     */
    InfoDialogController.prototype.switchToRuleType = function (ruleType) {
        if (this.currentRuleType === ruleType) {
            return; // 如果已经是当前类型，不需要切换
        }
        this.currentRuleType = ruleType;
        // 移动按钮位置（视觉效果）
        this.moveButtonToPosition();
        // 切换ScrollView显示
        this.switchScrollViewDisplay();
    };
    /**
     * 新增：移动按钮到指定位置
     */
    InfoDialogController.prototype.moveButtonToPosition = function () {
        if (!this.switchButton) {
            return;
        }
        // 按钮位置：左边（-150，-2）右边（142，-2）
        var leftPosition = cc.v3(-150, -2, 0);
        var rightPosition = cc.v3(142, -2, 0);
        var targetPosition = this.currentRuleType === 0 ? leftPosition : rightPosition;
        // 使用动画移动按钮
        cc.tween(this.switchButton)
            .to(0.3, { position: targetPosition }, { easing: 'quartOut' })
            .start();
    };
    /**
     * 新增：切换ScrollView显示
     */
    InfoDialogController.prototype.switchScrollViewDisplay = function () {
        if (!this.danjiScrollView || !this.duorenScrollView) {
            return;
        }
        if (this.currentRuleType === 0) {
            // 显示单机规则
            this.danjiScrollView.active = true;
            this.duorenScrollView.active = false;
        }
        else {
            // 显示联机规则
            this.danjiScrollView.active = false;
            this.duorenScrollView.active = true;
        }
    };
    InfoDialogController.prototype.show = function (backCallback) {
        this.backCallback = backCallback;
        this.node.active = true;
        this.boardBg.scale = 0;
        // 执行缩放动画
        cc.tween(this.boardBg)
            .to(Config_1.Config.dialogScaleTime, { scale: 1 })
            .start();
    };
    InfoDialogController.prototype.hide = function () {
        var _this = this;
        if (this.backCallback) {
            this.backCallback();
        }
        // 执行缩放动画
        cc.tween(this.boardBg)
            .to(Config_1.Config.dialogScaleTime, { scale: 0 })
            .call(function () {
            _this.node.active = false;
        })
            .start();
    };
    __decorate([
        property(cc.Node)
    ], InfoDialogController.prototype, "boardBg", void 0);
    __decorate([
        property(cc.Node)
    ], InfoDialogController.prototype, "boardBtnClose", void 0);
    __decorate([
        property(cc.Node)
    ], InfoDialogController.prototype, "contentLay", void 0);
    __decorate([
        property(cc.Node)
    ], InfoDialogController.prototype, "danjiLabel", void 0);
    __decorate([
        property(cc.Node)
    ], InfoDialogController.prototype, "lianjiLabel", void 0);
    __decorate([
        property(cc.Node)
    ], InfoDialogController.prototype, "switchButton", void 0);
    __decorate([
        property(cc.Node)
    ], InfoDialogController.prototype, "danjiScrollView", void 0);
    __decorate([
        property(cc.Node)
    ], InfoDialogController.prototype, "duorenScrollView", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoItem", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoItem1", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage1", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage2", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage3", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage4", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage5", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage6", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage7", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage8", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage9", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage10", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage11", void 0);
    InfoDialogController = __decorate([
        ccclass
    ], InfoDialogController);
    return InfoDialogController;
}(cc.Component));
exports.default = InfoDialogController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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