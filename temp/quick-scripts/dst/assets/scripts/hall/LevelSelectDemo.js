
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/hall/LevelSelectDemo.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'fb002PTGrFERKCYkhthVy56', 'LevelSelectDemo');
// scripts/hall/LevelSelectDemo.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var LevelSelectController_1 = require("./Level/LevelSelectController");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
/**
 * 关卡选择演示控制器
 * 这个脚本展示了如何创建一个完整的关卡选择界面
 */
var LevelSelectDemo = /** @class */ (function (_super) {
    __extends(LevelSelectDemo, _super);
    function LevelSelectDemo() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.scrollView = null;
        _this.content = null;
        _this.infoLabel = null;
        // 关卡数据
        _this.levelDataList = [];
        _this.currentSelectedLevel = 1;
        _this.totalLevels = 30;
        _this.levelItemWidth = 150;
        // 关卡节点列表
        _this.levelNodes = [];
        return _this;
    }
    LevelSelectDemo.prototype.onLoad = function () {
        this.initLevelData();
        this.createLevelSelectUI();
    };
    LevelSelectDemo.prototype.start = function () {
        this.scrollToLevel(this.currentSelectedLevel);
        this.updateInfoDisplay();
    };
    /**
     * 初始化关卡数据
     */
    LevelSelectDemo.prototype.initLevelData = function () {
        this.levelDataList = [];
        for (var i = 1; i <= this.totalLevels; i++) {
            var status = void 0;
            if (i === 1) {
                status = LevelSelectController_1.LevelStatus.CURRENT; // 第一关为当前关卡
            }
            else if (i <= 3) {
                status = LevelSelectController_1.LevelStatus.COMPLETED; // 前3关已通关（示例）
            }
            else {
                status = LevelSelectController_1.LevelStatus.LOCKED; // 其他关卡未解锁
            }
            this.levelDataList.push({
                levelNumber: i,
                status: status
            });
        }
    };
    /**
     * 创建关卡选择UI
     */
    LevelSelectDemo.prototype.createLevelSelectUI = function () {
        if (!this.content) {
            cc.error("Content node is not assigned!");
            return;
        }
        // 清空现有内容
        this.content.removeAllChildren();
        this.levelNodes = [];
        // 计算总宽度
        var totalWidth = (this.totalLevels - 1) * this.levelItemWidth + 650;
        this.content.width = totalWidth;
        for (var i = 0; i < this.totalLevels; i++) {
            var levelData = this.levelDataList[i];
            // 创建关卡节点
            var levelNode = this.createLevelNode(levelData);
            this.content.addChild(levelNode);
            this.levelNodes.push(levelNode);
            // 设置位置
            var posX = i * this.levelItemWidth - totalWidth / 2 + 650 / 2;
            levelNode.setPosition(posX, 0);
            // 创建连接线（除了最后一个关卡）
            if (i < this.totalLevels - 1) {
                var lineNode = this.createLineNode();
                this.content.addChild(lineNode);
                lineNode.setPosition(posX + this.levelItemWidth / 2, 0);
            }
        }
    };
    /**
     * 创建关卡节点
     */
    LevelSelectDemo.prototype.createLevelNode = function (levelData) {
        var _this = this;
        var node = new cc.Node("Level_" + levelData.levelNumber);
        // 添加Sprite组件
        node.addComponent(cc.Sprite);
        // 添加Label组件显示关卡数字
        var labelNode = new cc.Node("LevelLabel");
        var label = labelNode.addComponent(cc.Label);
        label.string = levelData.levelNumber.toString();
        label.fontSize = 20;
        label.node.color = cc.Color.WHITE;
        labelNode.parent = node;
        // 添加Button组件
        var button = node.addComponent(cc.Button);
        button.target = node;
        // 设置点击事件
        node.on('click', function () {
            _this.onLevelClicked(levelData.levelNumber);
        }, this);
        // 更新关卡外观
        this.updateLevelNodeAppearance(node, levelData, false);
        return node;
    };
    /**
     * 创建连接线节点
     */
    LevelSelectDemo.prototype.createLineNode = function () {
        var node = new cc.Node("Line");
        var sprite = node.addComponent(cc.Sprite);
        // 加载连接线图片
        cc.resources.load("hall_page_res/Level_Btn/pop_line", cc.SpriteFrame, function (err, spriteFrame) {
            if (!err && spriteFrame) {
                sprite.spriteFrame = spriteFrame;
            }
        });
        return node;
    };
    /**
     * 更新关卡节点外观
     */
    LevelSelectDemo.prototype.updateLevelNodeAppearance = function (node, levelData, isSelected) {
        var sprite = node.getComponent(cc.Sprite);
        if (!sprite)
            return;
        var imagePath = "";
        var size = cc.size(46, 46);
        // 根据状态和是否选中确定图片路径
        if (isSelected) {
            size = cc.size(86, 86);
            switch (levelData.status) {
                case LevelSelectController_1.LevelStatus.LOCKED:
                    imagePath = "hall_page_res/Level_Btn/pop_gray_choose";
                    break;
                case LevelSelectController_1.LevelStatus.CURRENT:
                    imagePath = "hall_page_res/Level_Btn/pop_yellow_choose";
                    break;
                case LevelSelectController_1.LevelStatus.COMPLETED:
                    imagePath = "hall_page_res/Level_Btn/pop_green_choose";
                    break;
            }
        }
        else {
            switch (levelData.status) {
                case LevelSelectController_1.LevelStatus.LOCKED:
                    imagePath = "hall_page_res/Level_Btn/pop_gray";
                    break;
                case LevelSelectController_1.LevelStatus.CURRENT:
                    imagePath = "hall_page_res/Level_Btn/pop_yellow";
                    break;
                case LevelSelectController_1.LevelStatus.COMPLETED:
                    imagePath = "hall_page_res/Level_Btn/pop_green";
                    break;
            }
        }
        // 设置节点大小
        node.setContentSize(size);
        // 加载并设置图片
        cc.resources.load(imagePath, cc.SpriteFrame, function (err, spriteFrame) {
            if (!err && spriteFrame) {
                sprite.spriteFrame = spriteFrame;
            }
        });
    };
    /**
     * 更新所有关卡显示
     */
    LevelSelectDemo.prototype.updateAllLevelsDisplay = function () {
        for (var i = 0; i < this.levelNodes.length; i++) {
            var node = this.levelNodes[i];
            var levelData = this.levelDataList[i];
            var isSelected = (levelData.levelNumber === this.currentSelectedLevel);
            this.updateLevelNodeAppearance(node, levelData, isSelected);
        }
    };
    /**
     * 滚动到指定关卡
     */
    LevelSelectDemo.prototype.scrollToLevel = function (levelNumber) {
        if (levelNumber < 1 || levelNumber > this.totalLevels)
            return;
        var targetIndex = levelNumber - 1;
        var contentWidth = this.content.width;
        var scrollViewWidth = this.scrollView.node.width;
        // 计算目标位置的偏移量
        var targetOffset = (targetIndex * this.levelItemWidth) / (contentWidth - scrollViewWidth);
        // 限制偏移量在有效范围内
        var clampedOffset = cc.misc.clampf(targetOffset, 0, 1);
        // 设置滚动位置
        this.scrollView.scrollToPercentHorizontal(clampedOffset, 0.3);
    };
    /**
     * 关卡点击事件处理
     */
    LevelSelectDemo.prototype.onLevelClicked = function (levelNumber) {
        // 检查关卡是否可以选择（未锁定）
        var levelData = this.levelDataList[levelNumber - 1];
        if (levelData.status === LevelSelectController_1.LevelStatus.LOCKED) {
            return;
        }
        // 更新当前选中关卡
        this.currentSelectedLevel = levelNumber;
        this.updateAllLevelsDisplay();
        // 滚动到选中关卡
        this.scrollToLevel(levelNumber);
        this.updateInfoDisplay();
    };
    /**
     * 更新信息显示
     */
    LevelSelectDemo.prototype.updateInfoDisplay = function () {
        if (this.infoLabel) {
            var levelData = this.levelDataList[this.currentSelectedLevel - 1];
            var statusText = "";
            switch (levelData.status) {
                case LevelSelectController_1.LevelStatus.LOCKED:
                    statusText = "未解锁";
                    break;
                case LevelSelectController_1.LevelStatus.CURRENT:
                    statusText = "进行中";
                    break;
                case LevelSelectController_1.LevelStatus.COMPLETED:
                    statusText = "已通关";
                    break;
            }
            this.infoLabel.string = "\u5F53\u524D\u9009\u4E2D: \u5173\u5361" + this.currentSelectedLevel + " (" + statusText + ")";
        }
    };
    /**
     * 完成当前关卡（测试用）
     */
    LevelSelectDemo.prototype.completeCurrentLevel = function () {
        var levelData = this.levelDataList[this.currentSelectedLevel - 1];
        if (levelData.status === LevelSelectController_1.LevelStatus.CURRENT) {
            levelData.status = LevelSelectController_1.LevelStatus.COMPLETED;
            // 解锁下一关
            if (this.currentSelectedLevel < this.totalLevels) {
                var nextLevelData = this.levelDataList[this.currentSelectedLevel];
                if (nextLevelData.status === LevelSelectController_1.LevelStatus.LOCKED) {
                    nextLevelData.status = LevelSelectController_1.LevelStatus.CURRENT;
                }
            }
            this.updateAllLevelsDisplay();
            this.updateInfoDisplay();
        }
    };
    __decorate([
        property(cc.ScrollView)
    ], LevelSelectDemo.prototype, "scrollView", void 0);
    __decorate([
        property(cc.Node)
    ], LevelSelectDemo.prototype, "content", void 0);
    __decorate([
        property(cc.Label)
    ], LevelSelectDemo.prototype, "infoLabel", void 0);
    LevelSelectDemo = __decorate([
        ccclass
    ], LevelSelectDemo);
    return LevelSelectDemo;
}(cc.Component));
exports.default = LevelSelectDemo;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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