
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/test/AnimationTest.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '158b18OZNlHupzOczn3aHmL', 'AnimationTest');
// scripts/test/AnimationTest.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var AnimationTest = /** @class */ (function (_super) {
    __extends(AnimationTest, _super);
    function AnimationTest() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.testGameStartBtn = null;
        _this.testRoundStartBtn = null;
        _this.statusLabel = null;
        return _this;
    }
    AnimationTest.prototype.onLoad = function () {
        // 设置按钮点击事件
        if (this.testGameStartBtn) {
            this.testGameStartBtn.node.on('click', this.testGameStartAnimation, this);
        }
        if (this.testRoundStartBtn) {
            this.testRoundStartBtn.node.on('click', this.testRoundStartAnimation, this);
        }
    };
    /**
     * 测试游戏开始动画
     */
    AnimationTest.prototype.testGameStartAnimation = function () {
        var _this = this;
        this.updateStatus("测试游戏开始动画...");
        // 获取GamePageController实例
        var gamePageController = window.gamePageController;
        if (gamePageController) {
            // 调用游戏开始动画
            if (gamePageController.showGameStartAnimation) {
                gamePageController.showGameStartAnimation();
                this.updateStatus("游戏开始动画已触发");
                // 3秒后隐藏
                this.scheduleOnce(function () {
                    if (gamePageController.hideGameStartAnimation) {
                        gamePageController.hideGameStartAnimation();
                        _this.updateStatus("游戏开始动画已隐藏");
                    }
                }, 3);
            }
            else {
                this.updateStatus("GamePageController中没有找到showGameStartAnimation方法");
            }
        }
        else {
            this.updateStatus("未找到GamePageController实例");
        }
    };
    /**
     * 测试回合开始动画
     */
    AnimationTest.prototype.testRoundStartAnimation = function () {
        this.updateStatus("测试回合开始动画...");
        // 获取GamePageController实例
        var gamePageController = window.gamePageController;
        if (gamePageController) {
            // 调用回合开始动画
            if (gamePageController.showRoundStartAnimation) {
                gamePageController.showRoundStartAnimation();
                this.updateStatus("回合开始动画已触发");
            }
            else {
                this.updateStatus("GamePageController中没有找到showRoundStartAnimation方法");
            }
        }
        else {
            this.updateStatus("未找到GamePageController实例");
        }
    };
    /**
     * 更新状态显示
     */
    AnimationTest.prototype.updateStatus = function (message) {
        if (this.statusLabel) {
            this.statusLabel.string = message;
        }
        console.log("[AnimationTest] " + message);
    };
    AnimationTest.prototype.onDestroy = function () {
        if (this.testGameStartBtn) {
            this.testGameStartBtn.node.off('click', this.testGameStartAnimation, this);
        }
        if (this.testRoundStartBtn) {
            this.testRoundStartBtn.node.off('click', this.testRoundStartAnimation, this);
        }
    };
    __decorate([
        property(cc.Button)
    ], AnimationTest.prototype, "testGameStartBtn", void 0);
    __decorate([
        property(cc.Button)
    ], AnimationTest.prototype, "testRoundStartBtn", void 0);
    __decorate([
        property(cc.Label)
    ], AnimationTest.prototype, "statusLabel", void 0);
    AnimationTest = __decorate([
        ccclass
    ], AnimationTest);
    return AnimationTest;
}(cc.Component));
exports.default = AnimationTest;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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