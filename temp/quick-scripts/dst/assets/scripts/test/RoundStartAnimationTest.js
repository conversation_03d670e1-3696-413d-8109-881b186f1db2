
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/test/RoundStartAnimationTest.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'ea574rBeutE3KMKVQxcutF3', 'RoundStartAnimationTest');
// scripts/test/RoundStartAnimationTest.ts

"use strict";
// 回合开始动画测试脚本
// 这个脚本可以附加到任何节点上，用于测试回合开始动画
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var RoundStartAnimationTest = /** @class */ (function (_super) {
    __extends(RoundStartAnimationTest, _super);
    function RoundStartAnimationTest() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.testButton = null;
        _this.roundStartNode = null;
        return _this;
    }
    RoundStartAnimationTest.prototype.onLoad = function () {
        var _this = this;
        console.log("=== RoundStartAnimationTest onLoad ===");
        // 创建测试按钮（如果没有在编辑器中设置）
        if (!this.testButton) {
            this.createTestButton();
        }
        // 创建回合开始节点
        this.createRoundStartNode();
        // 3秒后自动测试
        this.scheduleOnce(function () {
            console.log("3秒后自动测试动画");
            _this.testAnimation();
        }, 3);
    };
    RoundStartAnimationTest.prototype.createTestButton = function () {
        var buttonNode = new cc.Node("TestButton");
        buttonNode.addComponent(cc.Button);
        var sprite = buttonNode.addComponent(cc.Sprite);
        var label = new cc.Node("Label");
        label.addComponent(cc.Label).string = "测试动画";
        buttonNode.addChild(label);
        buttonNode.setPosition(0, 200);
        buttonNode.width = 200;
        buttonNode.height = 80;
        var canvas = cc.find("Canvas");
        if (canvas) {
            canvas.addChild(buttonNode);
            buttonNode.on('click', this.testAnimation, this);
        }
    };
    RoundStartAnimationTest.prototype.createRoundStartNode = function () {
        console.log("创建回合开始测试节点");
        this.roundStartNode = new cc.Node('round_start_test_node');
        var canvas = cc.find('Canvas');
        if (canvas) {
            canvas.addChild(this.roundStartNode);
        }
        // 设置节点属性
        this.roundStartNode.setPosition(-750, -1);
        this.roundStartNode.zIndex = 1000;
        this.roundStartNode.width = 200;
        this.roundStartNode.height = 100;
        // 添加文本标签
        var label = this.roundStartNode.addComponent(cc.Label);
        label.string = "回合开始";
        label.fontSize = 48;
        label.node.color = cc.Color.WHITE;
        // 添加背景色
        var graphics = this.roundStartNode.addComponent(cc.Graphics);
        graphics.fillColor = cc.Color.RED;
        graphics.rect(-100, -50, 200, 100);
        graphics.fill();
        this.roundStartNode.active = false;
        console.log("回合开始测试节点创建完成");
    };
    RoundStartAnimationTest.prototype.testAnimation = function () {
        var _this = this;
        console.log("开始测试回合开始动画");
        if (!this.roundStartNode) {
            console.error("回合开始节点不存在");
            return;
        }
        // 显示节点并开始动画
        this.roundStartNode.active = true;
        this.roundStartNode.setPosition(-750, -1);
        this.roundStartNode.opacity = 255;
        this.roundStartNode.scale = 1;
        console.log("节点状态 - 位置:", this.roundStartNode.position, "可见:", this.roundStartNode.active);
        // 执行动画：0.5秒移动到中间，1秒展示，0.5秒移动到右边
        cc.tween(this.roundStartNode)
            .to(0.5, { x: 0 }, { easing: 'quartOut' })
            .call(function () { return console.log("到达中间位置"); })
            .delay(1.0)
            .call(function () { return console.log("开始移动到右边"); })
            .to(0.5, { x: 750 }, { easing: 'quartIn' })
            .call(function () {
            console.log("动画完成，恢复初始状态");
            _this.roundStartNode.setPosition(-750, -1);
            _this.roundStartNode.active = false;
        })
            .start();
    };
    RoundStartAnimationTest.prototype.start = function () {
        if (this.testButton) {
            this.testButton.node.on('click', this.testAnimation, this);
        }
    };
    __decorate([
        property(cc.Button)
    ], RoundStartAnimationTest.prototype, "testButton", void 0);
    RoundStartAnimationTest = __decorate([
        ccclass
    ], RoundStartAnimationTest);
    return RoundStartAnimationTest;
}(cc.Component));
exports.default = RoundStartAnimationTest;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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