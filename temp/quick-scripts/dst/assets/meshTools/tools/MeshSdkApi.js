
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/meshTools/tools/MeshSdkApi.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'f530dRaLT5IBJrG6Sh+L1QP', 'MeshSdkApi');
// meshTools/tools/MeshSdkApi.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
var MeshTools_1 = require("../MeshTools");
var BaseSDK_1 = require("../BaseSDK");
var MessageBaseBean_1 = require("../../scripts/net/MessageBaseBean");
var GameMgr_1 = require("../../scripts/common/GameMgr");
var EventCenter_1 = require("../../scripts/common/EventCenter");
var MeshSdk = require("MeshSdk");
// var ZegoGameClient = ZG.ZegoGameClient;
var MeshSdkApi = /** @class */ (function (_super) {
    __extends(MeshSdkApi, _super);
    function MeshSdkApi() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this._meshSdk = MeshSdk.meshSDK; // new ZegoGameClient();
        _this._isChangeVoluming = false;
        _this.IsNotifyGameLoaded = false;
        _this.IsNotifyGameLoading = false;
        return _this;
    }
    // 监听 APP 事件  APP -> H5
    MeshSdkApi.prototype.AddAPPEvent = function () {
        // regReceiveMessage  注册 APP 回调到 H5 的函数
        this._meshSdk.regReceiveMessage({
            // 余额变更
            walletUpdate: function () {
                console.log("walletUpdate");
                var autoMessageBean = {
                    'msgId': MessageBaseBean_1.AutoMessageId.WalletUpdateMsg,
                    'data': {}
                };
                GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.AutoMessage, autoMessageBean);
            },
            // code 变更回调
            serverCodeUpdate: function (data) {
                data.code = encodeURIComponent(data.code);
                var autoMessageBean = {
                    'msgId': MessageBaseBean_1.AutoMessageId.ServerCodeUpdateMsg,
                    'data': { code: data.code }
                };
                GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.AutoMessage, autoMessageBean);
            }
        });
    };
    // H5 调用APP 获取APP 版本号
    MeshSdkApi.prototype.GetAppVersion = function (callback, errCallback) {
        callback && callback("1.0.0");
    };
    // H5 调用 APP  获取用户信息数据
    MeshSdkApi.prototype.GetConfig = function (callback) {
        if (MeshTools_1.MeshTools.Publish.isDataByURL) {
            callback({
                "gameConfig": {
                    "currencyIcon": "",
                    "sceneMode": 0
                },
                "code": MeshTools_1.MeshTools.Publish.code,
                "appId": MeshTools_1.MeshTools.Publish.appId,
                "language": MeshTools_1.MeshTools.Publish.language,
                "gameMode": MeshTools_1.MeshTools.Publish.gameMode,
                "userId": MeshTools_1.MeshTools.Publish.userId,
                "roomId": MeshTools_1.MeshTools.Publish.roomId,
                "appChannel": MeshTools_1.MeshTools.Publish.appChannel,
                "gsp": MeshTools_1.MeshTools.Publish.gsp
            });
            return;
        }
        if (CC_DEBUG) {
            callback({
                "gameConfig": {
                    "currencyIcon": "https://game-center-test.jieyou.shop/static/images/index/game_bean.png",
                    "sceneMode": 0
                },
                "code": "qFwaAVKyEYTmPey1vWA4Huq7bvto4xexT0UJRnh03vlwTghRwFyVsbO4JRLV",
                "appId": 66666666,
                "language": "0",
                "gameMode": 3,
                "userId": new Date(),
                "roomId": "room01",
                "appChannel": "debug",
                "gsp": 8001
            });
            return;
        }
        this._meshSdk.getConfig().then(callback).catch(function (err) {
            cc.error(err);
        });
    };
    // 通知APP 游戏资源加载完了
    MeshSdkApi.prototype.HideLoading = function () {
        if (this.IsNotifyGameLoaded == true) {
            return;
        }
        this.IsNotifyGameLoaded = true;
        this._meshSdk.gameLoaded();
    };
    // 销毁 WebView
    MeshSdkApi.prototype.CloseWebView = function () {
        this._meshSdk.destroy();
    };
    // 余额不足  H5 调用 APP
    MeshSdkApi.prototype.ShowAppShop = function (type) {
        cc.log("余额不足回调");
        this._meshSdk.gameRecharge();
    };
    return MeshSdkApi;
}(BaseSDK_1.BaseSDK));
exports.default = MeshSdkApi;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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