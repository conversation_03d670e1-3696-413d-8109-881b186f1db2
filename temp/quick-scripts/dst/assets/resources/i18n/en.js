
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/resources/i18n/en.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'cc55fhCTRdMjZxuQuVowyEX', 'en');
// resources/i18n/en.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.language = void 0;
exports.language = {
    //这部分是通用的
    kickout1: 'You have been asked to leave the room',
    LeaveRoom: 'The room is dissolved',
    InsufficientBalance: 'The current balance is insufficient, please go to purchase',
    GameRouteNotFound: 'Game route not found',
    NetworkError: 'network error',
    RoomIsFull: 'Room is full',
    EnterRoomNumber: 'Enter room number',
    GetUserInfoFailed: 'get user info failed',
    RoomDoesNotExist: 'Room does not exist',
    FailedToDeductGoldCoins: 'Failed to deduct gold coins',
    ExitApplication: 'Are you sure to leave?',
    QuitTheGame: 'Once you exit the game, you won’t be able to return to it.',
    NotEnoughPlayers: 'Not enough players',
    TheGameIsFullOfPlayers: 'The game is full of players',
    kickout2: 'Whether to kick {0} out of the room?',
    upSeat: 'Join',
    downSeat: 'Leave',
    startGame: 'Start',
    readyGame: 'Ready',
    cancelGame: 'Cancel',
    cancel: 'Cancel',
    confirm: 'Confirm',
    kickout3: 'Kick Out',
    back: 'Back',
    leave: 'Leave',
    music: 'Music',
    sound: 'Sound',
    join: 'Join',
    create: 'Create',
    auto: 'Auto',
    Room: 'Room',
    room_number: 'Room Number',
    copy: 'Copy',
    game_amount: 'Game Amount',
    player_numbers: 'Player Numbers:',
    room_exist: 'Room doesn’t exist',
    enter_room_number: 'Enter room number',
    free: 'Free',
    players: 'Players',
    Player: 'Player',
    Tickets: 'Tickets',
    Empty: 'Empty',
    nextlevel: 'Next',
    relevel: 'Play Again',
    rules: 'Rules',
    danjiguize: "Single Player Rules",
    lianjuguize: "Multiplayer Rules",
    dtips1: "Game Introduction:",
    dtips2: "Safe Zone:",
    dtips3: "Mine Zone:",
    dtips4: "Game Objective:",
    dtips5: "Marking:",
    dtips6: "Hint:",
    dinfo1: "The game contains hidden tiles that can be: numbered tiles, blank tiles, or mines. Click to reveal tiles - numbered tiles indicate how many mines are adjacent to that tile. Blank tiles will trigger a chain reaction of automatic reveals until numbered tiles are encountered. Use these mechanics to clear the board.",
    dinfo2: "Numbered tiles and blank tiles are collectively called the Safe Zone.",
    dinfo3: "Revealing a mine tile will cause instant failure.",
    dinfo4: "Reveal all safe tiles without triggering any mines to win.",
    dinfo5: "Long-press to place a flag marker on suspected mine tiles. Marking doesn't reveal the tile.",
    dinfo6: "Players get one free hint per game (max 4 uses). Clicking Hint will reveal one safe tile. Hints are game-specific and don't carry over.",
    ltips1: "Game Introduction:",
    ltips2: "Player Count:",
    ltips3: "Turn Duration:",
    ltips4: "Game Objective:",
    ltips5: "Marking:",
    ltips6: "Scoring Rules:",
    linfo1: "Uses the same tile mechanics as Single Player mode. Each turn, all players simultaneously select tiles. After time expires or all players finish choosing, results are revealed with score adjustments. Game ends when all tiles are claimed. Compete for the highest score!",
    linfo2: "2/3/4 Players",
    linfo3: "20 Seconds",
    linfo4: "Score points through gameplay mechanics to win.",
    linfo5: "Long-press to mark potential mines. Correct mine markings grant bonus points.",
    linfo6: "1. First player to select a tile each turn earns +1pt.\n2. Click-reveal: +6pts for safe tiles, -12pts for mines.\n3. Mark-reveal: +10pts for correctly flagged mines, 0pts for safe tiles.\n4. Shared tiles: Points are split (e.g., two players click same safe tile = +3pts each)."
};
var cocos = cc;
if (!cocos.Jou_i18n)
    cocos.Jou_i18n = {};
cocos.Jou_i18n.en = exports.language;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0cy9yZXNvdXJjZXMvaTE4bi9lbi50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBYSxRQUFBLFFBQVEsR0FBRztJQUN4QixTQUFTO0lBQ0wsUUFBUSxFQUFHLHVDQUF1QztJQUNsRCxTQUFTLEVBQUcsdUJBQXVCO0lBQ25DLG1CQUFtQixFQUFHLDREQUE0RDtJQUNsRixpQkFBaUIsRUFBRyxzQkFBc0I7SUFDMUMsWUFBWSxFQUFHLGVBQWU7SUFDOUIsVUFBVSxFQUFHLGNBQWM7SUFDM0IsZUFBZSxFQUFHLG1CQUFtQjtJQUNyQyxpQkFBaUIsRUFBRyxzQkFBc0I7SUFDMUMsZ0JBQWdCLEVBQUcscUJBQXFCO0lBQ3hDLHVCQUF1QixFQUFHLDZCQUE2QjtJQUN2RCxlQUFlLEVBQUcsd0JBQXdCO0lBQzFDLFdBQVcsRUFBRyw0REFBNEQ7SUFFMUUsZ0JBQWdCLEVBQUcsb0JBQW9CO0lBQ3ZDLHNCQUFzQixFQUFHLDZCQUE2QjtJQUN0RCxRQUFRLEVBQUUsc0NBQXNDO0lBRWhELE1BQU0sRUFBQyxNQUFNO0lBQ2IsUUFBUSxFQUFDLE9BQU87SUFDaEIsU0FBUyxFQUFDLE9BQU87SUFDakIsU0FBUyxFQUFDLE9BQU87SUFDakIsVUFBVSxFQUFDLFFBQVE7SUFDbkIsTUFBTSxFQUFDLFFBQVE7SUFDZixPQUFPLEVBQUMsU0FBUztJQUNqQixRQUFRLEVBQUMsVUFBVTtJQUNuQixJQUFJLEVBQUMsTUFBTTtJQUNYLEtBQUssRUFBQyxPQUFPO0lBQ2IsS0FBSyxFQUFDLE9BQU87SUFDYixLQUFLLEVBQUMsT0FBTztJQUNiLElBQUksRUFBQyxNQUFNO0lBQ1gsTUFBTSxFQUFDLFFBQVE7SUFDZixJQUFJLEVBQUMsTUFBTTtJQUNYLElBQUksRUFBQyxNQUFNO0lBQ1gsV0FBVyxFQUFDLGFBQWE7SUFDekIsSUFBSSxFQUFDLE1BQU07SUFDWCxXQUFXLEVBQUMsYUFBYTtJQUN6QixjQUFjLEVBQUMsaUJBQWlCO0lBQ2hDLFVBQVUsRUFBQyxvQkFBb0I7SUFDL0IsaUJBQWlCLEVBQUMsbUJBQW1CO0lBQ3JDLElBQUksRUFBQyxNQUFNO0lBQ1gsT0FBTyxFQUFDLFNBQVM7SUFDakIsTUFBTSxFQUFDLFFBQVE7SUFDZixPQUFPLEVBQUMsU0FBUztJQUNqQixLQUFLLEVBQUMsT0FBTztJQUViLFNBQVMsRUFBQyxNQUFNO0lBRWhCLE9BQU8sRUFBQyxZQUFZO0lBQ3BCLEtBQUssRUFBQyxPQUFPO0lBRWIsVUFBVSxFQUFFLHFCQUFxQjtJQUNyQyxXQUFXLEVBQUUsbUJBQW1CO0lBRWhDLE1BQU0sRUFBRSxvQkFBb0I7SUFDNUIsTUFBTSxFQUFFLFlBQVk7SUFDcEIsTUFBTSxFQUFFLFlBQVk7SUFDcEIsTUFBTSxFQUFFLGlCQUFpQjtJQUN6QixNQUFNLEVBQUUsVUFBVTtJQUNsQixNQUFNLEVBQUUsT0FBTztJQUVmLE1BQU0sRUFBRSwyVEFBMlQ7SUFDblUsTUFBTSxFQUFFLHVFQUF1RTtJQUMvRSxNQUFNLEVBQUUsbURBQW1EO0lBQzNELE1BQU0sRUFBRSw0REFBNEQ7SUFDcEUsTUFBTSxFQUFFLDZGQUE2RjtJQUNyRyxNQUFNLEVBQUUseUlBQXlJO0lBRWpKLE1BQU0sRUFBRSxvQkFBb0I7SUFDNUIsTUFBTSxFQUFFLGVBQWU7SUFDdkIsTUFBTSxFQUFFLGdCQUFnQjtJQUN4QixNQUFNLEVBQUUsaUJBQWlCO0lBQ3pCLE1BQU0sRUFBRSxVQUFVO0lBQ2xCLE1BQU0sRUFBRSxnQkFBZ0I7SUFFeEIsTUFBTSxFQUFFLDhRQUE4UTtJQUN0UixNQUFNLEVBQUUsZUFBZTtJQUN2QixNQUFNLEVBQUUsWUFBWTtJQUNwQixNQUFNLEVBQUUsaURBQWlEO0lBQ3pELE1BQU0sRUFBRSwrRUFBK0U7SUFDdkYsTUFBTSxFQUFFLHNSQUFzUjtDQUc3UixDQUFDO0FBRUYsSUFBTSxLQUFLLEdBQUcsRUFBUyxDQUFDO0FBQ3hCLElBQUksQ0FBQyxLQUFLLENBQUMsUUFBUTtJQUFFLEtBQUssQ0FBQyxRQUFRLEdBQUcsRUFBRSxDQUFDO0FBQ3pDLEtBQUssQ0FBQyxRQUFRLENBQUMsRUFBRSxHQUFHLGdCQUFRLENBQUMiLCJmaWxlIjoiIiwic291cmNlUm9vdCI6Ii8iLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgbGFuZ3VhZ2UgPSB7XG4vL+i/memDqOWIhuaYr+mAmueUqOeahFxuICAgIGtpY2tvdXQxIDogJ1lvdSBoYXZlIGJlZW4gYXNrZWQgdG8gbGVhdmUgdGhlIHJvb20nLC8v5oKo6KKr6K+35Ye65oi/6Ze0XG4gICAgTGVhdmVSb29tIDogJ1RoZSByb29tIGlzIGRpc3NvbHZlZCcsLy/miL/pl7Tlt7Lop6PmlaNcbiAgICBJbnN1ZmZpY2llbnRCYWxhbmNlIDogJ1RoZSBjdXJyZW50IGJhbGFuY2UgaXMgaW5zdWZmaWNpZW50LCBwbGVhc2UgZ28gdG8gcHVyY2hhc2UnLC8v5L2Z6aKd5LiN6LazXG4gICAgR2FtZVJvdXRlTm90Rm91bmQgOiAnR2FtZSByb3V0ZSBub3QgZm91bmQnLC8v5ri45oiP57q/6Lev5byC5bi4XG4gICAgTmV0d29ya0Vycm9yIDogJ25ldHdvcmsgZXJyb3InLC8v572R57uc5byC5bi4XG4gICAgUm9vbUlzRnVsbCA6ICdSb29tIGlzIGZ1bGwnLC8v5oi/6Ze05bey5ruhXG4gICAgRW50ZXJSb29tTnVtYmVyIDogJ0VudGVyIHJvb20gbnVtYmVyJywvL+i+k+WFpeaIv+mXtOWPt1xuICAgIEdldFVzZXJJbmZvRmFpbGVkIDogJ2dldCB1c2VyIGluZm8gZmFpbGVkJywvL+iOt+WPlueUqOaIt+S/oeaBr+Wksei0pVxuICAgIFJvb21Eb2VzTm90RXhpc3QgOiAnUm9vbSBkb2VzIG5vdCBleGlzdCcsLy/miL/pl7TkuI3lrZjlnKhcbiAgICBGYWlsZWRUb0RlZHVjdEdvbGRDb2lucyA6ICdGYWlsZWQgdG8gZGVkdWN0IGdvbGQgY29pbnMnLC8v5omj6Zmk6YeR5biB5aSx6LSlXG4gICAgRXhpdEFwcGxpY2F0aW9uIDogJ0FyZSB5b3Ugc3VyZSB0byBsZWF2ZT8nLC8v5a6M5YWo6YCA5Ye65ri45oiPXG4gICAgUXVpdFRoZUdhbWUgOiAnT25jZSB5b3UgZXhpdCB0aGUgZ2FtZSwgeW91IHdvbuKAmXQgYmUgYWJsZSB0byByZXR1cm4gdG8gaXQuJywvL+mAgOWHuuacrOWxgOa4uOaIj1xuXG4gICAgTm90RW5vdWdoUGxheWVycyA6ICdOb3QgZW5vdWdoIHBsYXllcnMnLC8v546p5a625pWw6YeP5LiN6LazXG4gICAgVGhlR2FtZUlzRnVsbE9mUGxheWVycyA6ICdUaGUgZ2FtZSBpcyBmdWxsIG9mIHBsYXllcnMnLC8v546p5a625pWw6YeP5bey5ruhXG4gICAga2lja291dDI6ICdXaGV0aGVyIHRvIGtpY2sgezB9IG91dCBvZiB0aGUgcm9vbT8nLC8v6Lii5Ye6546p5a625paH5qGIXG5cbiAgICB1cFNlYXQ6J0pvaW4nLCAvL+S4iuW6p1xuICAgIGRvd25TZWF0OidMZWF2ZScsIC8v5LiL5bqnXG4gICAgc3RhcnRHYW1lOidTdGFydCcsIC8v5byA5aeL5ri45oiPXG4gICAgcmVhZHlHYW1lOidSZWFkeScsIC8v5YeG5aSHXG4gICAgY2FuY2VsR2FtZTonQ2FuY2VsJywgLy/lj5bmtojlh4blpIdcbiAgICBjYW5jZWw6J0NhbmNlbCcsIFxuICAgIGNvbmZpcm06J0NvbmZpcm0nLCBcbiAgICBraWNrb3V0MzonS2ljayBPdXQnLCBcbiAgICBiYWNrOidCYWNrJywvL+i/lOWbnlxuICAgIGxlYXZlOidMZWF2ZScsIC8v6YCA5Ye6XG4gICAgbXVzaWM6J011c2ljJywgIC8v6Z+z5LmQXG4gICAgc291bmQ6J1NvdW5kJywgLy/pn7PmlYhcbiAgICBqb2luOidKb2luJywgLy/liqDlhaVcbiAgICBjcmVhdGU6J0NyZWF0ZScsIC8v5Yib5bu6XG4gICAgYXV0bzonQXV0bycsXG4gICAgUm9vbTonUm9vbScsXG4gICAgcm9vbV9udW1iZXI6J1Jvb20gTnVtYmVyJywgLy/miL/pl7Tlj7dcbiAgICBjb3B5OidDb3B5JywgLy/lpI3liLZcbiAgICBnYW1lX2Ftb3VudDonR2FtZSBBbW91bnQnLCAvL+a4uOaIj+i0ueeUqFxuICAgIHBsYXllcl9udW1iZXJzOidQbGF5ZXIgTnVtYmVyczonLCAvL+eOqeWutuaVsOmHj1xuICAgIHJvb21fZXhpc3Q6J1Jvb20gZG9lc27igJl0IGV4aXN0JywvL+aIv+mXtOS4jeWtmOWcqFxuICAgIGVudGVyX3Jvb21fbnVtYmVyOidFbnRlciByb29tIG51bWJlcicsLy/ovpPlhaXmiL/pl7Tlj7dcbiAgICBmcmVlOidGcmVlJyxcbiAgICBwbGF5ZXJzOidQbGF5ZXJzJywgLy/njqnlrrZcbiAgICBQbGF5ZXI6J1BsYXllcicsXG4gICAgVGlja2V0czonVGlja2V0cycsXG4gICAgRW1wdHk6J0VtcHR5JyxcblxuICAgIG5leHRsZXZlbDonTmV4dCcsLy/kuIvkuIDlhbNcblxuICAgIHJlbGV2ZWw6J1BsYXkgQWdhaW4nLCAvL+WGjeadpeS4gOWxgFxuICAgIHJ1bGVzOidSdWxlcycsXG5cbiAgICBkYW5qaWd1aXplOiBcIlNpbmdsZSBQbGF5ZXIgUnVsZXNcIixcbmxpYW5qdWd1aXplOiBcIk11bHRpcGxheWVyIFJ1bGVzXCIsXG5cbmR0aXBzMTogXCJHYW1lIEludHJvZHVjdGlvbjpcIixcbmR0aXBzMjogXCJTYWZlIFpvbmU6XCIsXG5kdGlwczM6IFwiTWluZSBab25lOlwiLFxuZHRpcHM0OiBcIkdhbWUgT2JqZWN0aXZlOlwiLFxuZHRpcHM1OiBcIk1hcmtpbmc6XCIsXG5kdGlwczY6IFwiSGludDpcIixcblxuZGluZm8xOiBcIlRoZSBnYW1lIGNvbnRhaW5zIGhpZGRlbiB0aWxlcyB0aGF0IGNhbiBiZTogbnVtYmVyZWQgdGlsZXMsIGJsYW5rIHRpbGVzLCBvciBtaW5lcy4gQ2xpY2sgdG8gcmV2ZWFsIHRpbGVzIC0gbnVtYmVyZWQgdGlsZXMgaW5kaWNhdGUgaG93IG1hbnkgbWluZXMgYXJlIGFkamFjZW50IHRvIHRoYXQgdGlsZS4gQmxhbmsgdGlsZXMgd2lsbCB0cmlnZ2VyIGEgY2hhaW4gcmVhY3Rpb24gb2YgYXV0b21hdGljIHJldmVhbHMgdW50aWwgbnVtYmVyZWQgdGlsZXMgYXJlIGVuY291bnRlcmVkLiBVc2UgdGhlc2UgbWVjaGFuaWNzIHRvIGNsZWFyIHRoZSBib2FyZC5cIixcbmRpbmZvMjogXCJOdW1iZXJlZCB0aWxlcyBhbmQgYmxhbmsgdGlsZXMgYXJlIGNvbGxlY3RpdmVseSBjYWxsZWQgdGhlIFNhZmUgWm9uZS5cIixcbmRpbmZvMzogXCJSZXZlYWxpbmcgYSBtaW5lIHRpbGUgd2lsbCBjYXVzZSBpbnN0YW50IGZhaWx1cmUuXCIsXG5kaW5mbzQ6IFwiUmV2ZWFsIGFsbCBzYWZlIHRpbGVzIHdpdGhvdXQgdHJpZ2dlcmluZyBhbnkgbWluZXMgdG8gd2luLlwiLFxuZGluZm81OiBcIkxvbmctcHJlc3MgdG8gcGxhY2UgYSBmbGFnIG1hcmtlciBvbiBzdXNwZWN0ZWQgbWluZSB0aWxlcy4gTWFya2luZyBkb2Vzbid0IHJldmVhbCB0aGUgdGlsZS5cIixcbmRpbmZvNjogXCJQbGF5ZXJzIGdldCBvbmUgZnJlZSBoaW50IHBlciBnYW1lIChtYXggNCB1c2VzKS4gQ2xpY2tpbmcgSGludCB3aWxsIHJldmVhbCBvbmUgc2FmZSB0aWxlLiBIaW50cyBhcmUgZ2FtZS1zcGVjaWZpYyBhbmQgZG9uJ3QgY2Fycnkgb3Zlci5cIixcblxubHRpcHMxOiBcIkdhbWUgSW50cm9kdWN0aW9uOlwiLFxubHRpcHMyOiBcIlBsYXllciBDb3VudDpcIixcbmx0aXBzMzogXCJUdXJuIER1cmF0aW9uOlwiLFxubHRpcHM0OiBcIkdhbWUgT2JqZWN0aXZlOlwiLFxubHRpcHM1OiBcIk1hcmtpbmc6XCIsXG5sdGlwczY6IFwiU2NvcmluZyBSdWxlczpcIixcblxubGluZm8xOiBcIlVzZXMgdGhlIHNhbWUgdGlsZSBtZWNoYW5pY3MgYXMgU2luZ2xlIFBsYXllciBtb2RlLiBFYWNoIHR1cm4sIGFsbCBwbGF5ZXJzIHNpbXVsdGFuZW91c2x5IHNlbGVjdCB0aWxlcy4gQWZ0ZXIgdGltZSBleHBpcmVzIG9yIGFsbCBwbGF5ZXJzIGZpbmlzaCBjaG9vc2luZywgcmVzdWx0cyBhcmUgcmV2ZWFsZWQgd2l0aCBzY29yZSBhZGp1c3RtZW50cy4gR2FtZSBlbmRzIHdoZW4gYWxsIHRpbGVzIGFyZSBjbGFpbWVkLiBDb21wZXRlIGZvciB0aGUgaGlnaGVzdCBzY29yZSFcIixcbmxpbmZvMjogXCIyLzMvNCBQbGF5ZXJzXCIsXG5saW5mbzM6IFwiMjAgU2Vjb25kc1wiLFxubGluZm80OiBcIlNjb3JlIHBvaW50cyB0aHJvdWdoIGdhbWVwbGF5IG1lY2hhbmljcyB0byB3aW4uXCIsXG5saW5mbzU6IFwiTG9uZy1wcmVzcyB0byBtYXJrIHBvdGVudGlhbCBtaW5lcy4gQ29ycmVjdCBtaW5lIG1hcmtpbmdzIGdyYW50IGJvbnVzIHBvaW50cy5cIixcbmxpbmZvNjogXCIxLiBGaXJzdCBwbGF5ZXIgdG8gc2VsZWN0IGEgdGlsZSBlYWNoIHR1cm4gZWFybnMgKzFwdC5cXG4yLiBDbGljay1yZXZlYWw6ICs2cHRzIGZvciBzYWZlIHRpbGVzLCAtMTJwdHMgZm9yIG1pbmVzLlxcbjMuIE1hcmstcmV2ZWFsOiArMTBwdHMgZm9yIGNvcnJlY3RseSBmbGFnZ2VkIG1pbmVzLCAwcHRzIGZvciBzYWZlIHRpbGVzLlxcbjQuIFNoYXJlZCB0aWxlczogUG9pbnRzIGFyZSBzcGxpdCAoZS5nLiwgdHdvIHBsYXllcnMgY2xpY2sgc2FtZSBzYWZlIHRpbGUgPSArM3B0cyBlYWNoKS5cIlxuXG4gICAgXG59O1xuXG5jb25zdCBjb2NvcyA9IGNjIGFzIGFueTtcbmlmICghY29jb3MuSm91X2kxOG4pIGNvY29zLkpvdV9pMThuID0ge307XG5jb2Nvcy5Kb3VfaTE4bi5lbiA9IGxhbmd1YWdlOyJdfQ==