{"version": 3, "sources": ["packages/2.4.9+版本多语言插件i18n/script/LocalizedSprite.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAM,IAAA,KAAiD,EAAE,CAAC,UAAU,EAA5D,OAAO,aAAA,EAAE,QAAQ,cAAA,EAAE,iBAAiB,uBAAA,EAAE,IAAI,UAAkB,CAAC;AAGrE;IAAA;QAEW,aAAQ,GAAW,EAAE,CAAC;QAEtB,gBAAW,GAAmB,IAAI,CAAC;IAC9C,CAAC;IAHG;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;yDACS;IAE7B;QADC,QAAQ,CAAC,EAAE,CAAC,WAAW,CAAC;4DACiB;IAJjC,mBAAmB;QAD/B,OAAO,CAAC,qBAAqB,CAAC;OAClB,mBAAmB,CAK/B;IAAD,0BAAC;CALD,AAKC,IAAA;AALY,kDAAmB;AAUhC;IAAqC,mCAAY;IAAjD;QAAA,qEAqDC;QAlDW,qBAAe,GAA+B,IAAI,KAAK,EAAuB,CAAC;QAE/E,eAAS,GAAc,IAAI,CAAC;;IAgDxC,CAAC;IA9CG,gCAAM,GAAN;QACI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;QAC9C,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,EAAE;YACxB,EAAE,CAAC,KAAK,CAAC,sDAAsD,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClF,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;SACxB;QAED,yCAAyC;QACzC,IAAI,SAAS,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE;YAChD,IAAM,KAAK,GAAG,EAAS,CAAC;YACxB,IAAI,KAAK,CAAC,QAAQ,KAAK,IAAI,EAAE;gBACzB,IAAI,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;gBAC5C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;oBAC9C,IAAI,IAAI,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;oBACxB,IAAI,IAAI,KAAK,qBAAqB;wBAAE,SAAS;oBAC7C,IAAI,IAAI,GAAG,IAAI,mBAAmB,EAAE,CAAC;oBACrC,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;oBAC7B,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;iBACnC;aACJ;SACJ;IACL,CAAC;IAED,kCAAQ,GAAR;QACI,IAAI,CAAC,OAAO,EAAE,CAAC;IACnB,CAAC;IAED,cAAc;IACP,iCAAO,GAAd;QACI,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI;YAAE,OAAO;QAEnC,IAAM,KAAK,GAAG,EAAS,CAAC;QACxB,IAAI,UAAkB,CAAC;QACvB,IAAI,SAAS;YAAE,UAAU,GAAG,KAAK,CAAC,QAAQ,CAAC,mBAAmB,CAAC;;YAC1D,UAAU,GAAG,MAAM,CAAC,YAAY,CAAC;QAEtC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YACzD,IAAI,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YACzC,IAAI,UAAU,CAAC,QAAQ,KAAK,UAAU,EAAE;gBACpC,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,UAAU,CAAC,WAAW,CAAC;gBACpD,OAAO;aACV;SACJ;QAED,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,IAAI,CAAC;IACtC,CAAC;IAjDD;QADC,QAAQ,CAAC,CAAC,mBAAmB,CAAC,CAAC;4DACuD;IAH9E,eAAe;QAH3B,OAAO;QACP,iBAAiB;QACjB,IAAI,CAAC,sBAAsB,CAAC;OAChB,eAAe,CAqD3B;IAAD,sBAAC;CArDD,AAqDC,CArDoC,EAAE,CAAC,SAAS,GAqDhD;AArDY,0CAAe", "file": "", "sourceRoot": "/", "sourcesContent": ["const { ccclass, property, executeInEditMode, menu } = cc._decorator;\r\n\r\n@ccclass(\"LocalizedSpriteData\")\r\nexport class LocalizedSpriteData {\r\n    @property(cc.String)\r\n    public language: string = \"\";\r\n    @property(cc.SpriteFrame)\r\n    public spriteFrame: cc.SpriteFrame = null;\r\n}\r\n\r\n@ccclass\r\n@executeInEditMode\r\n@menu(\"i18n/LocalizedSprite\")\r\nexport class LocalizedSprite extends cc.Component {\r\n\r\n    @property([LocalizedSpriteData])\r\n    private spriteDataArray: Array<LocalizedSpriteData> = new Array<LocalizedSpriteData>();\r\n\r\n    private curSprite: cc.Sprite = null;\r\n\r\n    onLoad() {\r\n        this.curSprite = this.getComponent(cc.Sprite);\r\n        if (this.curSprite == null) {\r\n            cc.error(\"There is no cc.Sprite on LocalizedSprite, nodeName: \" + this.node.name);\r\n            this.enabled = false;\r\n        }\r\n\r\n        // 根据当前已有的语言，自动填充LocalizedSpriteDataArray\r\n        if (CC_EDITOR && this.spriteDataArray.length === 0) {\r\n            const cocos = cc as any;\r\n            if (cocos.Jou_i18n !== null) {\r\n                let nameArray = Object.keys(cocos.Jou_i18n);\r\n                for (let i = 0, t = nameArray.length; i < t; i++) {\r\n                    let name = nameArray[i];\r\n                    if (name === \"defaultLanguageName\") continue;\r\n                    let data = new LocalizedSpriteData();\r\n                    data.language = nameArray[i];\r\n                    this.spriteDataArray.push(data);\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    onEnable() {\r\n        this.refresh();\r\n    }\r\n\r\n    // 刷新cc.Sprite\r\n    public refresh(): void {\r\n        if (this.curSprite == null) return;\r\n\r\n        const cocos = cc as any;\r\n        let targetName: string;\r\n        if (CC_EDITOR) targetName = cocos.Jou_i18n.defaultLanguageName;\r\n        else targetName = window.languageName;\r\n\r\n        for (let i = 0, t = this.spriteDataArray.length; i < t; i++) {\r\n            let spriteData = this.spriteDataArray[i];\r\n            if (spriteData.language === targetName) {\r\n                this.curSprite.spriteFrame = spriteData.spriteFrame;\r\n                return;\r\n            }\r\n        }\r\n\r\n        this.curSprite.spriteFrame = null;\r\n    }\r\n}"]}