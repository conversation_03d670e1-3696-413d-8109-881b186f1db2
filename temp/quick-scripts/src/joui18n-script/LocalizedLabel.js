"use strict";
cc._RF.push(module, '89242RzotlBU5Nw9GrIJCE0', 'LocalizedLabel');
// LocalizedLabel.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LocalizedLabel = void 0;
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property, executeInEditMode = _a.executeInEditMode, menu = _a.menu;
var LocalizedLabel = /** @class */ (function (_super) {
    __extends(LocalizedLabel, _super);
    function LocalizedLabel() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this._key = "";
        _this.curLabel = null;
        _this.paramStrArray = new Array();
        return _this;
    }
    Object.defineProperty(LocalizedLabel.prototype, "key", {
        get: function () { return this._key; },
        set: function (v) {
            this._key = v;
            this.refresh();
        },
        enumerable: false,
        configurable: true
    });
    LocalizedLabel.prototype.onLoad = function () {
        this.curLabel = this.getComponent(cc.Label);
        if (this.curLabel == null) {
            cc.error("There is no cc.Label on LocalizedLabel, nodeName: " + this.node.name);
            this.enabled = false;
        }
    };
    LocalizedLabel.prototype.onEnable = function () {
        this.refresh();
    };
    // 刷新cc.Label
    LocalizedLabel.prototype.refresh = function () {
        if (this.curLabel == null)
            return;
        var str = window.getLocalizedStr(this._key);
        // 填充参数
        for (var i = 0, t = this.paramStrArray.length; i < t; i++)
            str = str.replace("{" + i + "}", this.paramStrArray[i]);
        this.curLabel.string = str;
    };
    // 绑定参数
    LocalizedLabel.prototype.bindParam = function () {
        var paramStrArray = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            paramStrArray[_i] = arguments[_i];
        }
        this.bindParamStrArray(paramStrArray);
    };
    // 绑定参数StrArray
    LocalizedLabel.prototype.bindParamStrArray = function (paramStrArray) {
        if (paramStrArray == null)
            return;
        this.paramStrArray = paramStrArray;
        this.refresh();
    };
    __decorate([
        property({ serializable: true })
    ], LocalizedLabel.prototype, "_key", void 0);
    __decorate([
        property(cc.String)
    ], LocalizedLabel.prototype, "key", null);
    LocalizedLabel = __decorate([
        ccclass,
        executeInEditMode,
        menu("i18n/LocalizedLabel")
    ], LocalizedLabel);
    return LocalizedLabel;
}(cc.Component));
exports.LocalizedLabel = LocalizedLabel;
window.getLocalizedStr = function (key) {
    var cocos = cc;
    if (CC_EDITOR) {
        var defaultName = cocos.Jou_i18n.defaultLanguageName;
        var language = cocos.Jou_i18n[defaultName];
        if (language != null && language[key] != null)
            return language[key];
    }
    else {
        if (window.languageName != null && window.languageName !== "") {
            var language = cocos.Jou_i18n[window.languageName];
            if (language[key] != null)
                return language[key];
        }
        else
            console.error("LanguageName is null, please set it first.");
    }
    return key;
};
window.refreshAllLocalizedComp = function () {
    var rootNode = cc.director.getScene();
    for (var i = 0, t = rootNode.childrenCount; i < t; i++) {
        var curNode = rootNode.children[i];
        var labelArray = curNode.getComponentsInChildren("LocalizedLabel");
        for (var j = 0, tt = labelArray.length; j < tt; j++)
            labelArray[j].refresh();
        var spriteArray = curNode.getComponentsInChildren("LocalizedSprite");
        for (var j = 0, tt = spriteArray.length; j < tt; j++)
            spriteArray[j].refresh();
    }
};

cc._RF.pop();