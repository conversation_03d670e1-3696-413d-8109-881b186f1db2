"use strict";
cc._RF.push(module, '26d02ZV5VhNPaJdQ9+G+shN', 'LocalizedSprite');
// LocalizedSprite.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LocalizedSprite = exports.LocalizedSpriteData = void 0;
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property, executeInEditMode = _a.executeInEditMode, menu = _a.menu;
var LocalizedSpriteData = /** @class */ (function () {
    function LocalizedSpriteData() {
        this.language = "";
        this.spriteFrame = null;
    }
    __decorate([
        property(cc.String)
    ], LocalizedSpriteData.prototype, "language", void 0);
    __decorate([
        property(cc.SpriteFrame)
    ], LocalizedSpriteData.prototype, "spriteFrame", void 0);
    LocalizedSpriteData = __decorate([
        ccclass("LocalizedSpriteData")
    ], LocalizedSpriteData);
    return LocalizedSpriteData;
}());
exports.LocalizedSpriteData = LocalizedSpriteData;
var LocalizedSprite = /** @class */ (function (_super) {
    __extends(LocalizedSprite, _super);
    function LocalizedSprite() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.spriteDataArray = new Array();
        _this.curSprite = null;
        return _this;
    }
    LocalizedSprite.prototype.onLoad = function () {
        this.curSprite = this.getComponent(cc.Sprite);
        if (this.curSprite == null) {
            cc.error("There is no cc.Sprite on LocalizedSprite, nodeName: " + this.node.name);
            this.enabled = false;
        }
        // 根据当前已有的语言，自动填充LocalizedSpriteDataArray
        if (CC_EDITOR && this.spriteDataArray.length === 0) {
            var cocos = cc;
            if (cocos.Jou_i18n !== null) {
                var nameArray = Object.keys(cocos.Jou_i18n);
                for (var i = 0, t = nameArray.length; i < t; i++) {
                    var name = nameArray[i];
                    if (name === "defaultLanguageName")
                        continue;
                    var data = new LocalizedSpriteData();
                    data.language = nameArray[i];
                    this.spriteDataArray.push(data);
                }
            }
        }
    };
    LocalizedSprite.prototype.onEnable = function () {
        this.refresh();
    };
    // 刷新cc.Sprite
    LocalizedSprite.prototype.refresh = function () {
        if (this.curSprite == null)
            return;
        var cocos = cc;
        var targetName;
        if (CC_EDITOR)
            targetName = cocos.Jou_i18n.defaultLanguageName;
        else
            targetName = window.languageName;
        for (var i = 0, t = this.spriteDataArray.length; i < t; i++) {
            var spriteData = this.spriteDataArray[i];
            if (spriteData.language === targetName) {
                this.curSprite.spriteFrame = spriteData.spriteFrame;
                return;
            }
        }
        this.curSprite.spriteFrame = null;
    };
    __decorate([
        property([LocalizedSpriteData])
    ], LocalizedSprite.prototype, "spriteDataArray", void 0);
    LocalizedSprite = __decorate([
        ccclass,
        executeInEditMode,
        menu("i18n/LocalizedSprite")
    ], LocalizedSprite);
    return LocalizedSprite;
}(cc.Component));
exports.LocalizedSprite = LocalizedSprite;

cc._RF.pop();