{"version": 3, "sources": ["packages/2.4.9+版本多语言插件i18n/script/LocalizedLabel.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAEM,IAAA,KAAiD,EAAE,CAAC,UAAU,EAA5D,OAAO,aAAA,EAAE,QAAQ,cAAA,EAAE,iBAAiB,uBAAA,EAAE,IAAI,UAAkB,CAAC;AAKrE;IAAoC,kCAAY;IAAhD;QAAA,qEAiDC;QA9CW,UAAI,GAAW,EAAE,CAAC;QAQlB,cAAQ,GAAa,IAAI,CAAC;QAC1B,mBAAa,GAAkB,IAAI,KAAK,EAAU,CAAC;;IAqC/D,CAAC;IA5CG,sBAAW,+BAAG;aAAd,cAA2B,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;aAC9C,UAAe,CAAS;YACpB,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;YACd,IAAI,CAAC,OAAO,EAAE,CAAC;QACnB,CAAC;;;OAJ6C;IAS9C,+BAAM,GAAN;QACI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;QAC5C,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,EAAE;YACvB,EAAE,CAAC,KAAK,CAAC,oDAAoD,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChF,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;SACxB;IACL,CAAC;IAED,iCAAQ,GAAR;QACI,IAAI,CAAC,OAAO,EAAE,CAAC;IACnB,CAAC;IAED,aAAa;IACN,gCAAO,GAAd;QACI,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI;YAAE,OAAO;QAElC,IAAI,GAAG,GAAG,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAE5C,OAAO;QACP,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;YAAE,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;QAEnH,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,GAAG,CAAC;IAC/B,CAAC;IAED,OAAO;IACA,kCAAS,GAAhB;QAAiB,uBAA+B;aAA/B,UAA+B,EAA/B,qBAA+B,EAA/B,IAA+B;YAA/B,kCAA+B;;QAC5C,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;IAC1C,CAAC;IAED,eAAe;IACR,0CAAiB,GAAxB,UAAyB,aAA4B;QACjD,IAAI,aAAa,IAAI,IAAI;YAAE,OAAO;QAClC,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,OAAO,EAAE,CAAC;IACnB,CAAC;IA7CD;QADC,QAAQ,CAAC,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC;gDACP;IAE1B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;6CAC0B;IALrC,cAAc;QAH1B,OAAO;QACP,iBAAiB;QACjB,IAAI,CAAC,qBAAqB,CAAC;OACf,cAAc,CAiD1B;IAAD,qBAAC;CAjDD,AAiDC,CAjDmC,EAAE,CAAC,SAAS,GAiD/C;AAjDY,wCAAc;AA4D3B,MAAM,CAAC,eAAe,GAAG,UAAC,GAAW;IACjC,IAAM,KAAK,GAAG,EAAS,CAAC;IAExB,IAAI,SAAS,EAAE;QACX,IAAI,WAAW,GAAG,KAAK,CAAC,QAAQ,CAAC,mBAAmB,CAAC;QACrD,IAAI,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;QAC3C,IAAI,QAAQ,IAAI,IAAI,IAAI,QAAQ,CAAC,GAAG,CAAC,IAAI,IAAI;YAAE,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC;KACvE;SACI;QACD,IAAI,MAAM,CAAC,YAAY,IAAI,IAAI,IAAI,MAAM,CAAC,YAAY,KAAK,EAAE,EAAE;YAC3D,IAAI,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;YACnD,IAAI,QAAQ,CAAC,GAAG,CAAC,IAAI,IAAI;gBAAE,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC;SACnD;;YACI,OAAO,CAAC,KAAK,CAAC,4CAA4C,CAAC,CAAC;KACpE;IAED,OAAO,GAAG,CAAC;AACf,CAAC,CAAC;AAEF,MAAM,CAAC,uBAAuB,GAAG;IAC7B,IAAI,QAAQ,GAAG,EAAE,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;IACtC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,aAAa,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;QACpD,IAAI,OAAO,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACnC,IAAI,UAAU,GAAG,OAAO,CAAC,uBAAuB,CAAC,gBAAgB,CAAqB,CAAC;QACvF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE;YAAE,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;QAC7E,IAAI,WAAW,GAAG,OAAO,CAAC,uBAAuB,CAAC,iBAAiB,CAAsB,CAAC;QAC1F,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE;YAAE,WAAW,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;KAClF;AACL,CAAC,CAAA", "file": "", "sourceRoot": "/", "sourcesContent": ["import { LocalizedSprite } from \"./LocalizedSprite\";\r\n\r\nconst { ccclass, property, executeInEditMode, menu } = cc._decorator;\r\n\r\n@ccclass\r\n@executeInEditMode\r\n@menu(\"i18n/LocalizedLabel\")\r\nexport class LocalizedLabel extends cc.Component {\r\n\r\n    @property({ serializable: true })\r\n    private _key: string = \"\";\r\n    @property(cc.String)\r\n    public get key(): string { return this._key; }\r\n    public set key(v: string) {\r\n        this._key = v;\r\n        this.refresh();\r\n    }\r\n\r\n    private curLabel: cc.Label = null;\r\n    private paramStrArray: Array<string> = new Array<string>();\r\n\r\n    onLoad() {\r\n        this.curLabel = this.getComponent(cc.Label);\r\n        if (this.curLabel == null) {\r\n            cc.error(\"There is no cc.Label on LocalizedLabel, nodeName: \" + this.node.name);\r\n            this.enabled = false;\r\n        }\r\n    }\r\n\r\n    onEnable() {\r\n        this.refresh();\r\n    }\r\n\r\n    // 刷新cc.Label\r\n    public refresh(): void {\r\n        if (this.curLabel == null) return;\r\n\r\n        let str = window.getLocalizedStr(this._key);\r\n\r\n        // 填充参数\r\n        for (let i = 0, t = this.paramStrArray.length; i < t; i++) str = str.replace(\"{\" + i + \"}\", this.paramStrArray[i]);\r\n\r\n        this.curLabel.string = str;\r\n    }\r\n\r\n    // 绑定参数\r\n    public bindParam(...paramStrArray: Array<string>): void {\r\n        this.bindParamStrArray(paramStrArray);\r\n    }\r\n\r\n    // 绑定参数StrArray\r\n    public bindParamStrArray(paramStrArray: Array<string>): void {\r\n        if (paramStrArray == null) return;\r\n        this.paramStrArray = paramStrArray;\r\n        this.refresh();\r\n    }\r\n}\r\n\r\n// 全局声明和定义\r\ndeclare global {\r\n    interface Window {\r\n        languageName: string;\r\n        getLocalizedStr: (str: string) => string;\r\n        refreshAllLocalizedComp: () => void;\r\n    }\r\n}\r\n\r\nwindow.getLocalizedStr = (key: string) => {\r\n    const cocos = cc as any;\r\n\r\n    if (CC_EDITOR) {\r\n        let defaultName = cocos.Jou_i18n.defaultLanguageName;\r\n        let language = cocos.Jou_i18n[defaultName];\r\n        if (language != null && language[key] != null) return language[key];\r\n    }\r\n    else {\r\n        if (window.languageName != null && window.languageName !== \"\") {\r\n            let language = cocos.Jou_i18n[window.languageName];\r\n            if (language[key] != null) return language[key];\r\n        }\r\n        else console.error(\"LanguageName is null, please set it first.\");\r\n    }\r\n\r\n    return key;\r\n};\r\n\r\nwindow.refreshAllLocalizedComp = () => {\r\n    let rootNode = cc.director.getScene();\r\n    for (let i = 0, t = rootNode.childrenCount; i < t; i++) {\r\n        let curNode = rootNode.children[i];\r\n        let labelArray = curNode.getComponentsInChildren(\"LocalizedLabel\") as LocalizedLabel[];\r\n        for (let j = 0, tt = labelArray.length; j < tt; j++) labelArray[j].refresh();\r\n        let spriteArray = curNode.getComponentsInChildren(\"LocalizedSprite\") as LocalizedSprite[];\r\n        for (let j = 0, tt = spriteArray.length; j < tt; j++) spriteArray[j].refresh();\r\n    }\r\n}"]}