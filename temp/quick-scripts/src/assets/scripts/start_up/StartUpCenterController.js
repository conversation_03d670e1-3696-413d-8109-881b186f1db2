"use strict";
cc._RF.push(module, '9ba1dVF68pFvbF9tS0dicZi', 'StartUpCenterController');
// scripts/start_up/StartUpCenterController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var EventCenter_1 = require("../common/EventCenter");
var GameMgr_1 = require("../common/GameMgr");
var MessageBaseBean_1 = require("../net/MessageBaseBean");
var Config_1 = require("../util/Config");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var StartUpCenterController = /** @class */ (function (_super) {
    __extends(StartUpCenterController, _super);
    function StartUpCenterController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.progress = null;
        _this.progressLabel = null;
        _this.countdownInterval = null; //倒计时的 id
        _this.login = false; //登录是否成功
        _this.loding = false; //加载是否成功
        _this.preload = false; //加载是否成功
        return _this;
        // update (dt) {}
    }
    StartUpCenterController.prototype.onLoad = function () {
        var _this = this;
        cc.resources.preloadDir(Config_1.Config.buttonRes, cc.SpriteAtlas, function (error, items) {
            if (error) {
                GameMgr_1.GameMgr.Console.Log('预加载按钮资源失败');
            }
            else {
                GameMgr_1.GameMgr.Console.Log('预加载按钮资源成功');
                _this.preload = true;
                _this.jumpHall();
            }
        }); //提前预加载图片
    };
    StartUpCenterController.prototype.onEnable = function () {
        this.updateCountdownLabel(0);
        this.startCountdown();
    };
    StartUpCenterController.prototype.start = function () {
    };
    StartUpCenterController.prototype.startCountdown = function () {
        var _this = this;
        var remainingSeconds = 0;
        this.countdownInterval = setInterval(function () {
            remainingSeconds++;
            if (remainingSeconds > 100) {
                clearInterval(_this.countdownInterval);
                _this.countdownInterval = null;
                _this.loding = true;
                // 进度到 100的处理
                _this.jumpHall();
                return;
            }
            _this.updateCountdownLabel(remainingSeconds);
        }, 10);
    };
    StartUpCenterController.prototype.updateCountdownLabel = function (percent) {
        this.progressLabel.string = percent + "%";
        this.progress.fillRange = percent / 100;
    };
    StartUpCenterController.prototype.onDisable = function () {
        if (this.countdownInterval) {
            clearInterval(this.countdownInterval);
            this.countdownInterval = null;
        }
    };
    StartUpCenterController.prototype.setLogin = function () {
        this.login = true;
        this.jumpHall();
    };
    StartUpCenterController.prototype.jumpHall = function () {
        //加载成功 并且登录成功 才允许跳转大厅页面
        if (this.loding && this.login && this.preload) {
            var autoMessageBean = {
                'msgId': MessageBaseBean_1.AutoMessageId.JumpHallPage,
                'data': { 'type': 1 } //1是启动页面跳转的
            };
            GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.AutoMessage, autoMessageBean);
        }
    };
    __decorate([
        property(cc.Sprite)
    ], StartUpCenterController.prototype, "progress", void 0);
    __decorate([
        property(cc.Label)
    ], StartUpCenterController.prototype, "progressLabel", void 0);
    StartUpCenterController = __decorate([
        ccclass
    ], StartUpCenterController);
    return StartUpCenterController;
}(cc.Component));
exports.default = StartUpCenterController;

cc._RF.pop();