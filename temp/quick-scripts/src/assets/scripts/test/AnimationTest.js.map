{"version": 3, "sources": ["assets/scripts/test/AnimationTest.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAM,IAAA,KAAwB,EAAE,CAAC,UAAU,EAAnC,OAAO,aAAA,EAAE,QAAQ,cAAkB,CAAC;AAG5C;IAA2C,iCAAY;IAAvD;QAAA,qEA0FC;QAvFG,sBAAgB,GAAc,IAAI,CAAC;QAGnC,uBAAiB,GAAc,IAAI,CAAC;QAGpC,iBAAW,GAAa,IAAI,CAAC;;IAiFjC,CAAC;IA/EG,8BAAM,GAAN;QACI,WAAW;QACX,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,sBAAsB,EAAE,IAAI,CAAC,CAAC;SAC7E;QAED,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,uBAAuB,EAAE,IAAI,CAAC,CAAC;SAC/E;IACL,CAAC;IAED;;OAEG;IACH,8CAAsB,GAAtB;QAAA,iBAwBC;QAvBG,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;QAEjC,yBAAyB;QACzB,IAAM,kBAAkB,GAAI,MAAc,CAAC,kBAAkB,CAAC;QAC9D,IAAI,kBAAkB,EAAE;YACpB,WAAW;YACX,IAAI,kBAAkB,CAAC,sBAAsB,EAAE;gBAC3C,kBAAkB,CAAC,sBAAsB,EAAE,CAAC;gBAC5C,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;gBAE/B,QAAQ;gBACR,IAAI,CAAC,YAAY,CAAC;oBACd,IAAI,kBAAkB,CAAC,sBAAsB,EAAE;wBAC3C,kBAAkB,CAAC,sBAAsB,EAAE,CAAC;wBAC5C,KAAI,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;qBAClC;gBACL,CAAC,EAAE,CAAC,CAAC,CAAC;aACT;iBAAM;gBACH,IAAI,CAAC,YAAY,CAAC,iDAAiD,CAAC,CAAC;aACxE;SACJ;aAAM;YACH,IAAI,CAAC,YAAY,CAAC,yBAAyB,CAAC,CAAC;SAChD;IACL,CAAC;IAED;;OAEG;IACH,+CAAuB,GAAvB;QACI,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;QAEjC,yBAAyB;QACzB,IAAM,kBAAkB,GAAI,MAAc,CAAC,kBAAkB,CAAC;QAC9D,IAAI,kBAAkB,EAAE;YACpB,WAAW;YACX,IAAI,kBAAkB,CAAC,uBAAuB,EAAE;gBAC5C,kBAAkB,CAAC,uBAAuB,EAAE,CAAC;gBAC7C,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;aAClC;iBAAM;gBACH,IAAI,CAAC,YAAY,CAAC,kDAAkD,CAAC,CAAC;aACzE;SACJ;aAAM;YACH,IAAI,CAAC,YAAY,CAAC,yBAAyB,CAAC,CAAC;SAChD;IACL,CAAC;IAED;;OAEG;IACK,oCAAY,GAApB,UAAqB,OAAe;QAChC,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,OAAO,CAAC;SACrC;QACD,OAAO,CAAC,GAAG,CAAC,qBAAmB,OAAS,CAAC,CAAC;IAC9C,CAAC;IAED,iCAAS,GAAT;QACI,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,sBAAsB,EAAE,IAAI,CAAC,CAAC;SAC9E;QACD,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,uBAAuB,EAAE,IAAI,CAAC,CAAC;SAChF;IACL,CAAC;IAtFD;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;2DACe;IAGnC;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;4DACgB;IAGpC;QADC,QAAQ,CAAC,EAAE,CAAC,KAAK,CAAC;sDACU;IATZ,aAAa;QADjC,OAAO;OACa,aAAa,CA0FjC;IAAD,oBAAC;CA1FD,AA0FC,CA1F0C,EAAE,CAAC,SAAS,GA0FtD;kBA1FoB,aAAa", "file": "", "sourceRoot": "/", "sourcesContent": ["const { ccclass, property } = cc._decorator;\n\n@ccclass\nexport default class AnimationTest extends cc.Component {\n\n    @property(cc.Button)\n    testGameStartBtn: cc.Button = null;\n\n    @property(cc.Button)\n    testRoundStartBtn: cc.Button = null;\n\n    @property(cc.Label)\n    statusLabel: cc.Label = null;\n\n    onLoad() {\n        // 设置按钮点击事件\n        if (this.testGameStartBtn) {\n            this.testGameStartBtn.node.on('click', this.testGameStartAnimation, this);\n        }\n\n        if (this.testRoundStartBtn) {\n            this.testRoundStartBtn.node.on('click', this.testRoundStartAnimation, this);\n        }\n    }\n\n    /**\n     * 测试游戏开始动画\n     */\n    testGameStartAnimation() {\n        this.updateStatus(\"测试游戏开始动画...\");\n        \n        // 获取GamePageController实例\n        const gamePageController = (window as any).gamePageController;\n        if (gamePageController) {\n            // 调用游戏开始动画\n            if (gamePageController.showGameStartAnimation) {\n                gamePageController.showGameStartAnimation();\n                this.updateStatus(\"游戏开始动画已触发\");\n                \n                // 3秒后隐藏\n                this.scheduleOnce(() => {\n                    if (gamePageController.hideGameStartAnimation) {\n                        gamePageController.hideGameStartAnimation();\n                        this.updateStatus(\"游戏开始动画已隐藏\");\n                    }\n                }, 3);\n            } else {\n                this.updateStatus(\"GamePageController中没有找到showGameStartAnimation方法\");\n            }\n        } else {\n            this.updateStatus(\"未找到GamePageController实例\");\n        }\n    }\n\n    /**\n     * 测试回合开始动画\n     */\n    testRoundStartAnimation() {\n        this.updateStatus(\"测试回合开始动画...\");\n        \n        // 获取GamePageController实例\n        const gamePageController = (window as any).gamePageController;\n        if (gamePageController) {\n            // 调用回合开始动画\n            if (gamePageController.showRoundStartAnimation) {\n                gamePageController.showRoundStartAnimation();\n                this.updateStatus(\"回合开始动画已触发\");\n            } else {\n                this.updateStatus(\"GamePageController中没有找到showRoundStartAnimation方法\");\n            }\n        } else {\n            this.updateStatus(\"未找到GamePageController实例\");\n        }\n    }\n\n    /**\n     * 更新状态显示\n     */\n    private updateStatus(message: string) {\n        if (this.statusLabel) {\n            this.statusLabel.string = message;\n        }\n        console.log(`[AnimationTest] ${message}`);\n    }\n\n    onDestroy() {\n        if (this.testGameStartBtn) {\n            this.testGameStartBtn.node.off('click', this.testGameStartAnimation, this);\n        }\n        if (this.testRoundStartBtn) {\n            this.testRoundStartBtn.node.off('click', this.testRoundStartAnimation, this);\n        }\n    }\n}\n"]}