"use strict";
cc._RF.push(module, '158b18OZNlHupzOczn3aHmL', 'AnimationTest');
// scripts/test/AnimationTest.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var AnimationTest = /** @class */ (function (_super) {
    __extends(AnimationTest, _super);
    function AnimationTest() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.testGameStartBtn = null;
        _this.testRoundStartBtn = null;
        _this.statusLabel = null;
        return _this;
    }
    AnimationTest.prototype.onLoad = function () {
        // 设置按钮点击事件
        if (this.testGameStartBtn) {
            this.testGameStartBtn.node.on('click', this.testGameStartAnimation, this);
        }
        if (this.testRoundStartBtn) {
            this.testRoundStartBtn.node.on('click', this.testRoundStartAnimation, this);
        }
    };
    /**
     * 测试游戏开始动画
     */
    AnimationTest.prototype.testGameStartAnimation = function () {
        var _this = this;
        this.updateStatus("测试游戏开始动画...");
        // 获取GamePageController实例
        var gamePageController = window.gamePageController;
        if (gamePageController) {
            // 调用游戏开始动画
            if (gamePageController.showGameStartAnimation) {
                gamePageController.showGameStartAnimation();
                this.updateStatus("游戏开始动画已触发");
                // 3秒后隐藏
                this.scheduleOnce(function () {
                    if (gamePageController.hideGameStartAnimation) {
                        gamePageController.hideGameStartAnimation();
                        _this.updateStatus("游戏开始动画已隐藏");
                    }
                }, 3);
            }
            else {
                this.updateStatus("GamePageController中没有找到showGameStartAnimation方法");
            }
        }
        else {
            this.updateStatus("未找到GamePageController实例");
        }
    };
    /**
     * 测试回合开始动画
     */
    AnimationTest.prototype.testRoundStartAnimation = function () {
        this.updateStatus("测试回合开始动画...");
        // 获取GamePageController实例
        var gamePageController = window.gamePageController;
        if (gamePageController) {
            // 调用回合开始动画
            if (gamePageController.showRoundStartAnimation) {
                gamePageController.showRoundStartAnimation();
                this.updateStatus("回合开始动画已触发");
            }
            else {
                this.updateStatus("GamePageController中没有找到showRoundStartAnimation方法");
            }
        }
        else {
            this.updateStatus("未找到GamePageController实例");
        }
    };
    /**
     * 更新状态显示
     */
    AnimationTest.prototype.updateStatus = function (message) {
        if (this.statusLabel) {
            this.statusLabel.string = message;
        }
        console.log("[AnimationTest] " + message);
    };
    AnimationTest.prototype.onDestroy = function () {
        if (this.testGameStartBtn) {
            this.testGameStartBtn.node.off('click', this.testGameStartAnimation, this);
        }
        if (this.testRoundStartBtn) {
            this.testRoundStartBtn.node.off('click', this.testRoundStartAnimation, this);
        }
    };
    __decorate([
        property(cc.Button)
    ], AnimationTest.prototype, "testGameStartBtn", void 0);
    __decorate([
        property(cc.Button)
    ], AnimationTest.prototype, "testRoundStartBtn", void 0);
    __decorate([
        property(cc.Label)
    ], AnimationTest.prototype, "statusLabel", void 0);
    AnimationTest = __decorate([
        ccclass
    ], AnimationTest);
    return AnimationTest;
}(cc.Component));
exports.default = AnimationTest;

cc._RF.pop();