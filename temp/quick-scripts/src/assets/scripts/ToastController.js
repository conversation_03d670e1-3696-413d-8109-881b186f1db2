"use strict";
cc._RF.push(module, '9aee1Hwx5RBJoqOKsbha9O+', 'ToastController');
// scripts/ToastController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var ToastController = /** @class */ (function (_super) {
    __extends(ToastController, _super);
    function ToastController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.alertBg = null;
        _this.toastLabel = null;
        _this.isShow = false;
        return _this;
    }
    // onLoad () {}
    ToastController.prototype.start = function () {
    };
    //设置提示文案
    ToastController.prototype.showContent = function (content) {
        if (this.isShow) { //判断当前是否正在显示提示文案  如果显示的话 就把上一个的倒计时关掉
            this.unschedule(this.hideContent);
        }
        this.toastLabel.string = content;
        this.alertBg.active = true;
        this.isShow = true;
        this.scheduleOnce(this.hideContent, 1.5);
    };
    ToastController.prototype.hideContent = function () {
        this.alertBg.active = false;
        this.isShow = false;
        this.toastLabel.string = '';
    };
    __decorate([
        property(cc.Node)
    ], ToastController.prototype, "alertBg", void 0);
    __decorate([
        property(cc.Label)
    ], ToastController.prototype, "toastLabel", void 0);
    ToastController = __decorate([
        ccclass
    ], ToastController);
    return ToastController;
}(cc.Component));
exports.default = ToastController;

cc._RF.pop();