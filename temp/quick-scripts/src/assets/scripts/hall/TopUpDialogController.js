"use strict";
cc._RF.push(module, 'd9c3ennLiFK2qSO3sB2lGHN', 'TopUpDialogController');
// scripts/hall/TopUpDialogController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var GameMgr_1 = require("../common/GameMgr");
var Config_1 = require("../util/Config");
var Tools_1 = require("../util/Tools");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var TopUpDialogController = /** @class */ (function (_super) {
    __extends(TopUpDialogController, _super);
    function TopUpDialogController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.boardBg = null;
        _this.boardBtnClose = null;
        _this.contentLay = null;
        _this.cancelBtn = null;
        _this.confirmBtn = null;
        _this.tipContent = null;
        _this.backCallback = null; //隐藏弹窗的回调
        return _this;
        // update (dt) {}
    }
    // onLoad () {}
    TopUpDialogController.prototype.start = function () {
        var _this = this;
        Tools_1.Tools.imageButtonClick(this.boardBtnClose, Config_1.Config.buttonRes + 'board_btn_close_normal', Config_1.Config.buttonRes + 'board_btn_close_pressed', function () {
            _this.hide();
        });
        //cancel 按钮点击事件
        Tools_1.Tools.yellowButton(this.cancelBtn, function () {
            _this.hide();
        });
        //Confirm 按钮点击事件
        Tools_1.Tools.greenButton(this.confirmBtn, function () {
            GameMgr_1.GameMgr.H5SDK.ShowAppShop();
            _this.hide();
        });
    };
    TopUpDialogController.prototype.show = function (backCallback) {
        this.backCallback = backCallback;
        this.node.active = true;
        this.boardBg.scale = 0;
        // 执行缩放动画
        cc.tween(this.boardBg)
            .to(Config_1.Config.dialogScaleTime, { scale: 1 })
            .start();
    };
    TopUpDialogController.prototype.hide = function () {
        var _this = this;
        if (this.backCallback) {
            this.backCallback();
        }
        // 执行缩放动画
        cc.tween(this.boardBg)
            .to(Config_1.Config.dialogScaleTime, { scale: 0 })
            .call(function () {
            _this.node.active = false;
        })
            .start();
    };
    __decorate([
        property(cc.Node)
    ], TopUpDialogController.prototype, "boardBg", void 0);
    __decorate([
        property(cc.Node)
    ], TopUpDialogController.prototype, "boardBtnClose", void 0);
    __decorate([
        property(cc.Node)
    ], TopUpDialogController.prototype, "contentLay", void 0);
    __decorate([
        property(cc.Node)
    ], TopUpDialogController.prototype, "cancelBtn", void 0);
    __decorate([
        property(cc.Node)
    ], TopUpDialogController.prototype, "confirmBtn", void 0);
    __decorate([
        property(cc.Label)
    ], TopUpDialogController.prototype, "tipContent", void 0);
    TopUpDialogController = __decorate([
        ccclass
    ], TopUpDialogController);
    return TopUpDialogController;
}(cc.Component));
exports.default = TopUpDialogController;

cc._RF.pop();