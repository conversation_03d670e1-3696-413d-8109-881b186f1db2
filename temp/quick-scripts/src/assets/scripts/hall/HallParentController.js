"use strict";
cc._RF.push(module, 'c6e47rrbmlLFbrYAuFsjy0x', 'HallParentController');
// scripts/hall/HallParentController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Publish_1 = require("../../meshTools/tools/Publish");
var GlobalBean_1 = require("../bean/GlobalBean");
var GameMgr_1 = require("../common/GameMgr");
var MessageId_1 = require("../net/MessageId");
var WebSocketManager_1 = require("../net/WebSocketManager");
var ToastController_1 = require("../ToastController");
var Config_1 = require("../util/Config");
var Tools_1 = require("../util/Tools");
var HallCenterLayController_1 = require("./HallCenterLayController");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var HallParentController = /** @class */ (function (_super) {
    __extends(HallParentController, _super);
    function HallParentController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.boardTitle = null;
        _this.boardIcon = null;
        _this.beansNumber = null;
        _this.boardBtnBack = null; //返回按钮
        _this.boardBtnInfo = null; //游戏简介按钮
        _this.boardBtnSetting = null; //设置按钮
        _this.hallCenterLayController = null;
        _this.toastController = null; //toast 的布局
        return _this;
        // update (dt) {}
    }
    // onLoad () {}
    HallParentController.prototype.onEnable = function () {
        this.updateGold();
    };
    HallParentController.prototype.start = function () {
        var _this = this;
        Tools_1.Tools.setTouchEvent(this.boardTitle, function () {
            GameMgr_1.GameMgr.H5SDK.ShowAppShop();
        });
        if (Publish_1.Publish.GetInstance().currencyIcon != null && Publish_1.Publish.GetInstance().currencyIcon !== '') {
            Tools_1.Tools.setNodeSpriteFrameUrl(this.boardIcon, Publish_1.Publish.GetInstance().currencyIcon);
        }
        Tools_1.Tools.imageButtonClick(this.boardBtnBack, Config_1.Config.buttonRes + 'board_btn_back_normal', Config_1.Config.buttonRes + 'board_btn_back_pressed', function () {
            var type = _this.hallCenterLayController.getCenterLaytouType();
            switch (type) {
                case HallCenterLayController_1.CenterLaytouType.HALL_AUTO_VIEW:
                    if (_this.backClick) {
                        _this.backClick();
                    }
                    break;
                case HallCenterLayController_1.CenterLaytouType.HALL_CREAT_ROOM_VIEW:
                    // this.hallCenterLayController.setCenterLaytouType(CenterLaytouType.HALL_AUTO_VIEW)
                    //发送离开房间的信息
                    WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeLeaveInvite, {});
                    break;
                case HallCenterLayController_1.CenterLaytouType.HALL_JOIN_ROOM_VIEW:
                    _this.hallCenterLayController.setCenterLaytouType(HallCenterLayController_1.CenterLaytouType.HALL_AUTO_VIEW);
                    break;
            }
        });
        Tools_1.Tools.imageButtonClick(this.boardBtnInfo, Config_1.Config.buttonRes + 'board_btn_info_normal', Config_1.Config.buttonRes + 'board_btn_info_pressed', function () {
            if (_this.infoClick) {
                _this.infoClick();
            }
        });
        Tools_1.Tools.imageButtonClick(this.boardBtnSetting, Config_1.Config.buttonRes + 'board_btn_setting_normal', Config_1.Config.buttonRes + 'board_btn_setting_pressed', function () {
            if (_this.settingClick) {
                _this.settingClick();
            }
        });
        this.hallCenterLayController.setClick(function () {
            //start 按钮的点击回调
            if (_this.startClick) {
                _this.startClick();
            }
        }, function () {
            if (_this.createClick) {
                _this.createClick();
            }
        }, function (userId, nickname) {
            _this.seatCallback(userId, nickname);
        });
    };
    //设置按钮点击事件的回调的
    HallParentController.prototype.setClick = function (backClick, infoClick, settingClick, startClick, createClick, seatCallback) {
        this.backClick = backClick;
        this.infoClick = infoClick;
        this.settingClick = settingClick;
        this.startClick = startClick;
        this.createClick = createClick;
        this.seatCallback = seatCallback;
    };
    //更新金币数量
    HallParentController.prototype.updateGold = function () {
        if (GlobalBean_1.GlobalBean.GetInstance().loginData) {
            this.beansNumber.string = Tools_1.Tools.NumToTBMK(GlobalBean_1.GlobalBean.GetInstance().loginData.userInfo.coin);
        }
    };
    //房间已经解散但是自己重连回来被遗留在 房间中的处理
    HallParentController.prototype.exitTheRoom = function () {
        var type = this.hallCenterLayController.getCenterLaytouType();
        if (type === HallCenterLayController_1.CenterLaytouType.HALL_CREAT_ROOM_VIEW) {
            this.toastController.showContent(window.getLocalizedStr('LeaveRoom'));
            this.hallCenterLayController.setCenterLaytouType(HallCenterLayController_1.CenterLaytouType.HALL_AUTO_VIEW);
        }
    };
    //设置接受邀请成功
    HallParentController.prototype.setAcceptInvite = function (acceptInvite) {
        this.hallCenterLayController.setAcceptInvite(acceptInvite);
    };
    //离开房间
    HallParentController.prototype.leaveRoom = function (noticeLeaveInvite) {
        this.hallCenterLayController.leaveRoom(noticeLeaveInvite);
    };
    //设置门票
    HallParentController.prototype.setFees = function () {
        this.hallCenterLayController.setFees();
    };
    //进入私人房间
    HallParentController.prototype.joinCreateRoom = function () {
        this.hallCenterLayController.joinCreateRoom();
    };
    HallParentController.prototype.joinError = function () {
        this.hallCenterLayController.joinError();
    };
    //准备 取消准备
    HallParentController.prototype.setReadyState = function (noticeUserInviteStatus) {
        this.hallCenterLayController.setReadyState(noticeUserInviteStatus);
    };
    __decorate([
        property(cc.Node)
    ], HallParentController.prototype, "boardTitle", void 0);
    __decorate([
        property(cc.Node)
    ], HallParentController.prototype, "boardIcon", void 0);
    __decorate([
        property(cc.Label)
    ], HallParentController.prototype, "beansNumber", void 0);
    __decorate([
        property(cc.Node)
    ], HallParentController.prototype, "boardBtnBack", void 0);
    __decorate([
        property(cc.Node)
    ], HallParentController.prototype, "boardBtnInfo", void 0);
    __decorate([
        property(cc.Node)
    ], HallParentController.prototype, "boardBtnSetting", void 0);
    __decorate([
        property(HallCenterLayController_1.default)
    ], HallParentController.prototype, "hallCenterLayController", void 0);
    __decorate([
        property(ToastController_1.default)
    ], HallParentController.prototype, "toastController", void 0);
    HallParentController = __decorate([
        ccclass
    ], HallParentController);
    return HallParentController;
}(cc.Component));
exports.default = HallParentController;

cc._RF.pop();