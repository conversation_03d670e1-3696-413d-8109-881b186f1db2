{"version": 3, "sources": ["assets/scripts/hall/HallAutoController.ts"], "names": [], "mappings": ";;;;;AAAA,oBAAoB;AACpB,4EAA4E;AAC5E,mBAAmB;AACnB,sFAAsF;AACtF,8BAA8B;AAC9B,sFAAsF;;;;;;;;;;;;;;;;;;;;;;AAGtF,iDAAgD;AAChD,yCAAwC;AACxC,uCAAsC;AAEhC,IAAA,KAAwB,EAAE,CAAC,UAAU,EAAnC,OAAO,aAAA,EAAE,QAAQ,cAAkB,CAAC;AAE5C,IAAY,UAGX;AAHD,WAAY,UAAU;IAClB,2CAAI,CAAA;IACJ,2CAAI,CAAA;AACR,CAAC,EAHW,UAAU,GAAV,kBAAU,KAAV,kBAAU,QAGrB;AAGD;IAAgD,sCAAY;IAA5D;QAAA,qEAkMC;QA/LG,aAAO,GAAY,IAAI,CAAC;QAExB,aAAO,GAAY,IAAI,CAAC;QAExB,oBAAc,GAAY,IAAI,CAAC;QAE/B,iBAAW,GAAY,IAAI,CAAC;QAE5B,kBAAY,GAAY,IAAI,CAAC;QAE7B,gBAAU,GAAY,IAAI,CAAC;QAG3B,kBAAY,GAAY,IAAI,CAAA;QAC5B,kBAAY,GAAY,IAAI,CAAA;QAE5B,yBAAmB,GAAY,IAAI,CAAA,CAAG,KAAK;QAC3C,wBAAkB,GAAY,IAAI,CAAA,CAAK,KAAK;QAC5C,kBAAY,GAAa,IAAI,CAAA,CAAE,MAAM;QAErC,gBAAU,GAAe,IAAI,CAAA;QAE7B,gBAAU,GAAa,IAAI,CAAA;QAC3B,iBAAW,GAAa,IAAI,CAAA;QAC5B,eAAS,GAAa,IAAI,CAAA;QAK1B,sBAAgB,GAAG,CAAC,CAAC;QACrB,oBAAc,GAAG,CAAC,CAAC;;QAgKnB,iBAAiB;IACrB,CAAC;IA/JG,mCAAM,GAAN;QACI,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,cAAc,CAAC,CAAA;QAC/D,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,cAAc,CAAC,CAAA;QAC/D,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,+BAA+B,CAAC,CAAA;QAC9F,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,8BAA8B,CAAC,CAAA;QAC5F,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC,YAAY,CAAC,EAAE,CAAC,KAAK,CAAC,CAAA;IAClG,CAAC;IAGD,UAAU;IACV,oCAAO,GAAP;QACI,IAAI,UAAU,GAAiB,uBAAU,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,WAAW,CAAA;QAC7E,kBAAkB;QAClB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACxC,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE;gBACvB,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;aACvC;YACD,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE;gBACvB,IAAI,CAAC,gBAAgB,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;aACzC;SACJ;QACD,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAA,OAAO;IAC/C,CAAC;IAGD,kCAAK,GAAL;QAAA,iBAoEC;QAlEG,aAAK,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,EAAE;YAC9B,KAAI,CAAC,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA;QACvC,CAAC,CAAC,CAAA;QACF,aAAK,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,EAAE;YAC9B,KAAI,CAAC,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA;QACvC,CAAC,CAAC,CAAA;QAEF,cAAc;QACd,aAAK,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,EAAE;YAChC,IAAI,KAAI,CAAC,UAAU,EAAE;gBACjB,KAAI,CAAC,UAAU,EAAE,CAAA;aACpB;QACL,CAAC,CAAC,CAAA;QAGF,eAAe;QACf,aAAK,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,EAAE;YACjC,IAAI,KAAI,CAAC,WAAW,EAAE;gBAClB,KAAI,CAAC,WAAW,EAAE,CAAA;aACrB;QACL,CAAC,CAAC,CAAA;QAEF,aAAa;QACb,aAAK,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,EAAE;YAChC,IAAI,KAAI,CAAC,SAAS,EAAE;gBAChB,KAAI,CAAC,SAAS,EAAE,CAAA;aACnB;QACL,CAAC,CAAC,CAAA;QAEF,OAAO;QACP,aAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,mBAAmB,EAAE,eAAM,CAAC,SAAS,GAAG,+BAA+B,EAAE,eAAM,CAAC,SAAS,GAAG,gCAAgC,EAAE;YACtJ,IAAI,uBAAU,CAAC,WAAW,EAAE,CAAC,WAAW,IAAI,UAAU,CAAC,IAAI,EAAE;gBACzD,IAAI,KAAI,CAAC,gBAAgB,KAAK,CAAC,EAAE;oBAC7B,OAAO;iBACV;gBACD,KAAI,CAAC,gBAAgB,EAAE,CAAC;gBAExB,KAAI,CAAC,aAAa,CAAC,KAAI,CAAC,cAAc,EAAE,KAAI,CAAC,gBAAgB,CAAC,CAAC;aAClE;iBAAM;gBACH,IAAI,KAAI,CAAC,cAAc,KAAK,CAAC,EAAE;oBAC3B,OAAO;iBACV;gBACD,KAAI,CAAC,cAAc,EAAE,CAAC;gBAEtB,KAAI,CAAC,aAAa,CAAC,KAAI,CAAC,gBAAgB,EAAE,KAAI,CAAC,cAAc,CAAC,CAAC;aAClE;QAEL,CAAC,CAAC,CAAA;QACF,OAAO;QACP,aAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,kBAAkB,EAAE,eAAM,CAAC,SAAS,GAAG,8BAA8B,EAAE,eAAM,CAAC,SAAS,GAAG,+BAA+B,EAAE;YACnJ,IAAI,uBAAU,CAAC,WAAW,EAAE,CAAC,WAAW,IAAI,UAAU,CAAC,IAAI,EAAE;gBACzD,IAAI,KAAI,CAAC,gBAAgB,KAAK,KAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;oBAC/D,OAAO;iBACV;gBACD,KAAI,CAAC,gBAAgB,EAAE,CAAC;gBACxB,KAAI,CAAC,aAAa,CAAC,KAAI,CAAC,cAAc,EAAE,KAAI,CAAC,gBAAgB,CAAC,CAAC;aAClE;iBAAM;gBACH,IAAI,KAAI,CAAC,cAAc,KAAK,KAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;oBAC/D,OAAO;iBACV;gBACD,KAAI,CAAC,cAAc,EAAE,CAAC;gBAEtB,KAAI,CAAC,aAAa,CAAC,KAAI,CAAC,gBAAgB,EAAE,KAAI,CAAC,cAAc,CAAC,CAAC;aAClE;QAEL,CAAC,CAAC,CAAA;IACN,CAAC;IAED,QAAQ;IACA,0CAAa,GAArB,UAAsB,UAAsB,EAAE,QAAgB;QAC1D,IAAI,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACrC,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE,CAAC;QACnF,uBAAU,CAAC,WAAW,EAAE,CAAC,UAAU,GAAG,IAAI,CAAC;IAC/C,CAAC;IAGD,yBAAyB;IACzB,0CAAa,GAAb,UAAc,UAAsB;QAChC,IAAI,IAAI,CAAC,UAAU,KAAK,UAAU,EAAE;YAChC,OAAM;SACT;QACD,IAAI,CAAC,UAAU,GAAG,UAAU,CAAA;QAC5B,uBAAU,CAAC,WAAW,EAAE,CAAC,WAAW,GAAG,UAAU,CAAA;QACjD,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,KAAK,CAAA;QAChC,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,KAAK,CAAA;QAChC,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,KAAK,CAAA;QAC/B,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,KAAK,CAAA;QAChC,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,KAAK,CAAA;QAG9B,QAAQ,UAAU,EAAE;YAChB,KAAK,UAAU,CAAC,IAAI;gBAChB,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAA;gBAC/B,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,IAAI,CAAA;gBAC9B,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,KAAK,CAAA;gBAChC,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,KAAK,CAAA;gBAC9B,MAAK;YACT,KAAK,UAAU,CAAC,IAAI;gBAChB,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAA;gBAC/B,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,KAAK,CAAA;gBAC/B,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAA;gBAC/B,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,IAAI,CAAA;gBAC7B,MAAK;SACZ;QAID,IAAI,uBAAU,CAAC,WAAW,EAAE,CAAC,WAAW,IAAI,UAAU,CAAC,IAAI,EAAE;YACzD,IAAI,IAAI,CAAC,cAAc,EAAE;gBACrB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;aAClE;iBAAM;gBACH,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,CAAA;aAC5D;SAEJ;aAAM;YACH,IAAI,IAAI,CAAC,gBAAgB,EAAE;gBACvB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;aAClE;iBAAM;gBACH,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,CAAA;aAC5D;SAEJ;IACL,CAAC;IAED,WAAW;IACX,2CAAc,GAAd,UAAe,UAAoB,EAAE,WAAqB,EAAE,SAAmB;QAC3E,IAAI,CAAC,UAAU,GAAG,UAAU,CAAA;QAC5B,IAAI,CAAC,WAAW,GAAG,WAAW,CAAA;QAC9B,IAAI,CAAC,SAAS,GAAG,SAAS,CAAA;IAC9B,CAAC;IA5LD;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;uDACM;IAExB;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;uDACM;IAExB;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;8DACa;IAE/B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;2DACU;IAE5B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;4DACW;IAE7B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;0DACS;IAbV,kBAAkB;QADtC,OAAO;OACa,kBAAkB,CAkMtC;IAAD,yBAAC;CAlMD,AAkMC,CAlM+C,EAAE,CAAC,SAAS,GAkM3D;kBAlMoB,kBAAkB", "file": "", "sourceRoot": "/", "sourcesContent": ["// Learn TypeScript:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html\n// Learn Attribute:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html\n// Learn life-cycle callbacks:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html\n\nimport { LoginData, RoomConfig } from \"../bean/GameBean\";\nimport { GlobalBean } from \"../bean/GlobalBean\";\nimport { Config } from \"../util/Config\";\nimport { Tools } from \"../util/Tools\";\n\nconst { ccclass, property } = cc._decorator;\n\nexport enum AutoOrRoom {\n    AUTO,\n    ROOM\n}\n\n@ccclass\nexport default class HallAutoController extends cc.Component {\n\n    @property(cc.Node)\n    autoLay: cc.Node = null;\n    @property(cc.Node)\n    roomLay: cc.Node = null;\n    @property(cc.Node)\n    boardTicketLay: cc.Node = null;\n    @property(cc.Node)\n    startButton: cc.Node = null;\n    @property(cc.Node)\n    createButton: cc.Node = null;\n    @property(cc.Node)\n    joinButton: cc.Node = null;\n\n\n    boardTabAuto: cc.Node = null\n    boardTabRoom: cc.Node = null\n\n    boardTicketBtnMinus: cc.Node = null   //门票➖\n    boardTicketBtnPlus: cc.Node = null     //门票➕\n    ticketNumber: cc.Label = null  //门票价格\n\n    autoOrRoom: AutoOrRoom = null\n\n    startClick: Function = null\n    createClick: Function = null\n    joinClick: Function = null\n\n    autoRoomConfig: RoomConfig;//auto 的房间数据\n    createRoomConfig: RoomConfig;//room 的房间数据\n\n    autoFeesPosition = 0;\n    createPosition = 0;\n\n    onLoad() {\n        this.boardTabAuto = this.autoLay.getChildByName('board_tab_02')\n        this.boardTabRoom = this.roomLay.getChildByName('board_tab_02')\n        this.boardTicketBtnMinus = this.boardTicketLay.getChildByName('board_ticket_btn_minus_normal')\n        this.boardTicketBtnPlus = this.boardTicketLay.getChildByName('board_ticket_btn_plus_normal')\n        this.ticketNumber = this.boardTicketLay.getChildByName('ticket_number').getComponent(cc.Label)\n    }\n\n\n    //设置游戏开始数据\n    setFees() {\n        let roomConfig: RoomConfig[] = GlobalBean.GetInstance().loginData.roomConfigs\n        //房间类型 1-普通场 2-私人场\n        for (let i = 0; i < roomConfig.length; i++) {\n            if (roomConfig[i].id == 1) {\n                this.autoRoomConfig = roomConfig[i];\n            }\n            if (roomConfig[i].id == 2) {\n                this.createRoomConfig = roomConfig[i];\n            }\n        }\n        this.setAutoOrRoom(AutoOrRoom.AUTO);//设置初始值\n    }\n\n\n    start() {\n\n        Tools.setTouchEvent(this.autoLay, () => {\n            this.setAutoOrRoom(AutoOrRoom.AUTO)\n        })\n        Tools.setTouchEvent(this.roomLay, () => {\n            this.setAutoOrRoom(AutoOrRoom.ROOM)\n        })\n\n        //start 按钮点击事件\n        Tools.greenButton(this.startButton, () => {\n            if (this.startClick) {\n                this.startClick()\n            }\n        })\n\n\n        //create 按钮点击事件\n        Tools.greenButton(this.createButton, () => {\n            if (this.createClick) {\n                this.createClick()\n            }\n        })\n\n        //join 按钮点击事件\n        Tools.yellowButton(this.joinButton, () => {\n            if (this.joinClick) {\n                this.joinClick()\n            }\n        })\n\n        //点击按钮减\n        Tools.imageButtonClick(this.boardTicketBtnMinus, Config.buttonRes + 'board_ticket_btn_minus_normal', Config.buttonRes + 'board_ticket_btn_minus_pressed', () => {\n            if (GlobalBean.GetInstance().autoAndRoom == AutoOrRoom.AUTO) {\n                if (this.autoFeesPosition === 0) {\n                    return;\n                }\n                this.autoFeesPosition--;\n\n                this.setTicketsNum(this.autoRoomConfig, this.autoFeesPosition);\n            } else {\n                if (this.createPosition === 0) {\n                    return;\n                }\n                this.createPosition--;\n\n                this.setTicketsNum(this.createRoomConfig, this.createPosition);\n            }\n\n        })\n        //点击按钮加\n        Tools.imageButtonClick(this.boardTicketBtnPlus, Config.buttonRes + 'board_ticket_btn_plus_normal', Config.buttonRes + 'board_ticket_btn_plus_pressed', () => {\n            if (GlobalBean.GetInstance().autoAndRoom == AutoOrRoom.AUTO) {\n                if (this.autoFeesPosition === this.autoRoomConfig.fees.length - 1) {\n                    return;\n                }\n                this.autoFeesPosition++;\n                this.setTicketsNum(this.autoRoomConfig, this.autoFeesPosition);\n            } else {\n                if (this.createPosition === this.createRoomConfig.fees.length - 1) {\n                    return;\n                }\n                this.createPosition++;\n\n                this.setTicketsNum(this.createRoomConfig, this.createPosition);\n            }\n\n        })\n    }\n\n    //赋值门票价格\n    private setTicketsNum(roomConfig: RoomConfig, position: number) {\n        let fees = roomConfig.fees[position];\n        this.ticketNumber.string = fees === 0 ? window.getLocalizedStr('free') : fees + '';\n        GlobalBean.GetInstance().ticketsNum = fees;\n    }\n\n\n    //设置展示 auto 还是 room 的view\n    setAutoOrRoom(autoOrRoom: AutoOrRoom) {\n        if (this.autoOrRoom === autoOrRoom) {\n            return\n        }\n        this.autoOrRoom = autoOrRoom\n        GlobalBean.GetInstance().autoAndRoom = autoOrRoom\n        this.boardTabAuto.active = false\n        this.boardTabRoom.active = false\n        this.startButton.active = false\n        this.createButton.active = false\n        this.joinButton.active = false\n\n\n        switch (autoOrRoom) {\n            case AutoOrRoom.AUTO:\n                this.boardTabAuto.active = true\n                this.startButton.active = true\n                this.createButton.active = false\n                this.joinButton.active = false\n                break\n            case AutoOrRoom.ROOM:\n                this.boardTabRoom.active = true\n                this.startButton.active = false\n                this.createButton.active = true\n                this.joinButton.active = true\n                break\n        }\n\n\n\n        if (GlobalBean.GetInstance().autoAndRoom == AutoOrRoom.AUTO) {\n            if (this.autoRoomConfig) {\n                this.setTicketsNum(this.autoRoomConfig, this.autoFeesPosition);\n            } else {\n                this.ticketNumber.string = window.getLocalizedStr('free')\n            }\n\n        } else {\n            if (this.createRoomConfig) {\n                this.setTicketsNum(this.createRoomConfig, this.createPosition);\n            } else {\n                this.ticketNumber.string = window.getLocalizedStr('free')\n            }\n\n        }\n    }\n\n    //设置点击按钮的回调\n    setButtonClick(startClick: Function, createClick: Function, joinClick: Function) {\n        this.startClick = startClick\n        this.createClick = createClick\n        this.joinClick = joinClick\n    }\n\n    // update (dt) {}\n}\n"]}