{"version": 3, "sources": ["assets/scripts/hall/Level/LevelSelectExample.ts"], "names": [], "mappings": ";;;;;AAAA,oBAAoB;AACpB,4EAA4E;AAC5E,mBAAmB;AACnB,sFAAsF;AACtF,8BAA8B;AAC9B,sFAAsF;;;;;;;;;;;;;;;;;;;;;AAEtF,iEAAsD;AAEhD,IAAA,KAAwB,EAAE,CAAC,UAAU,EAAnC,OAAO,aAAA,EAAE,QAAQ,cAAkB,CAAC;AAE5C;;;GAGG;AAEH;IAAgD,sCAAY;IAA5D;QAAA,qEA4YC;QA1YG,OAAO;QACC,gBAAU,GAAkB,IAAI,CAAC;QACjC,aAAO,GAAY,IAAI,CAAC;QACxB,eAAS,GAAa,IAAI,CAAC;QAEnC,OAAO;QACC,mBAAa,GAAU,EAAE,CAAC;QAC1B,0BAAoB,GAAW,CAAC,CAAC;QACjC,iBAAW,GAAW,EAAE,CAAC;QACzB,oBAAc,GAAW,GAAG,CAAC;QAErC,SAAS;QACD,gBAAU,GAAc,EAAE,CAAC;;IA8XvC,CAAC;IA5XG,mCAAM,GAAN;QACI,IAAI,CAAC,QAAQ,EAAE,CAAC;QAChB,IAAI,CAAC,aAAa,EAAE,CAAC;QACrB,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC/B,CAAC;IAED,kCAAK,GAAL;QACI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;QAC9C,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC7B,CAAC;IAED;;OAEG;IACK,qCAAQ,GAAhB;QACI,OAAO;QACP,IAAM,EAAE,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACrC,EAAE,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;QAC3B,EAAE,CAAC,cAAc,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QAC7B,EAAE,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC;QAC1B,EAAE,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC;QAEtB,OAAO;QACP,IAAM,SAAS,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACvC,IAAM,UAAU,GAAG,SAAS,CAAC,YAAY,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;QACpD,UAAU,CAAC,MAAM,GAAG,MAAM,CAAC;QAC3B,UAAU,CAAC,QAAQ,GAAG,EAAE,CAAC;QACzB,UAAU,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC;QACvC,SAAS,CAAC,WAAW,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QAC9B,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC;QAE7B,eAAe;QACf,IAAM,cAAc,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACjD,IAAI,CAAC,UAAU,GAAG,cAAc,CAAC,YAAY,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC;QAC7D,cAAc,CAAC,cAAc,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;QACxC,cAAc,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACjC,cAAc,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC;QAElC,aAAa;QACb,IAAM,QAAQ,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACzC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;QAC/B,QAAQ,CAAC,cAAc,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;QAClC,QAAQ,CAAC,MAAM,GAAG,cAAc,CAAC;QAEjC,YAAY;QACZ,IAAI,CAAC,OAAO,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACtC,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;QACtC,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,QAAQ,CAAC;QAE/B,iBAAiB;QACjB,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QACvC,IAAI,CAAC,UAAU,CAAC,UAAU,GAAG,IAAI,CAAC;QAClC,IAAI,CAAC,UAAU,CAAC,QAAQ,GAAG,KAAK,CAAC;QACjC,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,IAAI,CAAC;QAC/B,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,IAAI,CAAC;QAE/B,gBAAgB;QAChB,IAAI,CAAC,UAAU,CAAC,mBAAmB,GAAG,IAAI,CAAC;QAC3C,IAAI,CAAC,UAAU,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAEzC,SAAS;QACT,IAAM,QAAQ,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC1C,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;QACjD,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,WAAW,CAAC;QACpC,IAAI,CAAC,SAAS,CAAC,QAAQ,GAAG,EAAE,CAAC;QAC7B,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC;QAC3C,QAAQ,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;QAC9B,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC;QAE5B,SAAS;QACT,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC7B,CAAC;IAED;;OAEG;IACK,8CAAiB,GAAzB;QAAA,iBAeC;QAdG,SAAS;QACT,IAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE;YAC/D,KAAI,CAAC,oBAAoB,EAAE,CAAC;QAChC,CAAC,CAAC,CAAC;QAEH,OAAO;QACP,IAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE;YACvD,KAAI,CAAC,WAAW,EAAE,CAAC;QACvB,CAAC,CAAC,CAAC;QAEH,SAAS;QACT,IAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE;YAC1D,KAAI,CAAC,iBAAiB,EAAE,CAAC;QAC7B,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACK,yCAAY,GAApB,UAAqB,IAAY,EAAE,QAAiB,EAAE,QAAkB;QACpE,IAAM,OAAO,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACtC,IAAM,MAAM,GAAG,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;QAC/C,IAAM,MAAM,GAAG,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;QAE/C,SAAS;QACT,OAAO,CAAC,cAAc,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;QAChC,OAAO,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC;QAC9B,OAAO,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAC9B,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC;QAE3B,OAAO;QACP,IAAM,SAAS,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACvC,IAAM,KAAK,GAAG,SAAS,CAAC,YAAY,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;QAC/C,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC;QACpB,KAAK,CAAC,QAAQ,GAAG,EAAE,CAAC;QACpB,KAAK,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC;QAClC,SAAS,CAAC,MAAM,GAAG,OAAO,CAAC;QAE3B,SAAS;QACT,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;QAEpC,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;OAEG;IACK,0CAAa,GAArB;QACI,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;QACxB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC,EAAE,EAAE;YACxC,IAAI,MAAM,SAAa,CAAC;YACxB,IAAI,CAAC,KAAK,CAAC,EAAE;gBACT,MAAM,GAAG,mCAAW,CAAC,OAAO,CAAC;aAChC;iBAAM,IAAI,CAAC,IAAI,CAAC,EAAE;gBACf,MAAM,GAAG,mCAAW,CAAC,SAAS,CAAC;aAClC;iBAAM;gBACH,MAAM,GAAG,mCAAW,CAAC,MAAM,CAAC;aAC/B;YAED,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;gBACpB,WAAW,EAAE,CAAC;gBACd,MAAM,EAAE,MAAM;aACjB,CAAC,CAAC;SACN;IACL,CAAC;IAED;;OAEG;IACK,gDAAmB,GAA3B;QACI,IAAI,CAAC,IAAI,CAAC,OAAO;YAAE,OAAO;QAE1B,SAAS;QACT,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,CAAC;QACjC,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;QAErB,QAAQ;QACR,IAAM,UAAU,GAAG,CAAC,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,GAAG,GAAG,CAAC;QACtE,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,UAAU,CAAC;QAEhC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,EAAE,EAAE;YACvC,IAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;YAExC,SAAS;YACT,IAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;YAClD,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;YACjC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAEhC,OAAO;YACP,IAAM,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC,cAAc,GAAG,UAAU,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;YAChE,SAAS,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;YAE/B,kBAAkB;YAClB,IAAI,CAAC,GAAG,IAAI,CAAC,WAAW,GAAG,CAAC,EAAE;gBAC1B,IAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;gBACvC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;gBAChC,QAAQ,CAAC,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC,cAAc,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;aAC3D;SACJ;IACL,CAAC;IAED;;OAEG;IACK,4CAAe,GAAvB,UAAwB,SAAc;QAAtC,iBA2BC;QA1BG,IAAM,IAAI,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,WAAS,SAAS,CAAC,WAAa,CAAC,CAAC;QAE3D,aAAa;QACb,IAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;QAE5C,kBAAkB;QAClB,IAAM,SAAS,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC5C,IAAM,KAAK,GAAG,SAAS,CAAC,YAAY,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;QAC/C,KAAK,CAAC,MAAM,GAAG,SAAS,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC;QAChD,KAAK,CAAC,QAAQ,GAAG,EAAE,CAAC;QACpB,KAAK,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC;QAClC,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC;QAExB,aAAa;QACb,IAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;QAC5C,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC;QAErB,SAAS;QACT,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE;YACb,KAAI,CAAC,cAAc,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;QAC/C,CAAC,EAAE,IAAI,CAAC,CAAC;QAET,SAAS;QACT,IAAI,CAAC,yBAAyB,CAAC,IAAI,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;QAEvD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,2CAAc,GAAtB;QACI,IAAM,IAAI,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACjC,IAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;QAC5C,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC1B,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC;QAE5B,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,sDAAyB,GAAjC,UAAkC,IAAa,EAAE,SAAc,EAAE,UAAmB;QAChF,IAAI,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QAC3B,IAAI,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC;QAE1B,cAAc;QACd,IAAI,UAAU,EAAE;YACZ,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;SAC1B;QAED,QAAQ,SAAS,CAAC,MAAM,EAAE;YACtB,KAAK,mCAAW,CAAC,MAAM;gBACnB,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC;gBACtB,MAAM;YACV,KAAK,mCAAW,CAAC,OAAO;gBACpB,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC;gBACxB,MAAM;YACV,KAAK,mCAAW,CAAC,SAAS;gBACtB,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC;gBACvB,MAAM;SACb;QAED,YAAY;QACZ,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAC1B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACvB,CAAC;IAED;;OAEG;IACK,mDAAsB,GAA9B;QACI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC7C,IAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YAChC,IAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;YACxC,IAAM,UAAU,GAAG,CAAC,SAAS,CAAC,WAAW,KAAK,IAAI,CAAC,oBAAoB,CAAC,CAAC;YAEzE,IAAI,CAAC,yBAAyB,CAAC,IAAI,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;SAC/D;IACL,CAAC;IAED;;OAEG;IACK,0CAAa,GAArB,UAAsB,WAAmB;QACrC,IAAI,WAAW,GAAG,CAAC,IAAI,WAAW,GAAG,IAAI,CAAC,WAAW;YAAE,OAAO;QAE9D,IAAM,WAAW,GAAG,WAAW,GAAG,CAAC,CAAC;QACpC,IAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;QACxC,IAAM,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC;QAEnD,IAAM,YAAY,GAAG,CAAC,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,YAAY,GAAG,eAAe,CAAC,CAAC;QAC5F,IAAM,aAAa,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAEzD,IAAI,CAAC,UAAU,CAAC,yBAAyB,CAAC,aAAa,EAAE,GAAG,CAAC,CAAC;IAClE,CAAC;IAED;;OAEG;IACK,2CAAc,GAAtB,UAAuB,WAAmB;QACtC,IAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;QACtD,IAAI,SAAS,CAAC,MAAM,KAAK,mCAAW,CAAC,MAAM,EAAE;YAEzC,OAAO;SACV;QAED,IAAI,CAAC,oBAAoB,GAAG,WAAW,CAAC;QACxC,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAC9B,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;QAChC,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAG7B,CAAC;IAED;;OAEG;IACK,8CAAiB,GAAzB;QACI,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC,CAAC;YACpE,IAAI,UAAU,GAAG,EAAE,CAAC;YACpB,QAAQ,SAAS,CAAC,MAAM,EAAE;gBACtB,KAAK,mCAAW,CAAC,MAAM;oBACnB,UAAU,GAAG,KAAK,CAAC;oBACnB,MAAM;gBACV,KAAK,mCAAW,CAAC,OAAO;oBACpB,UAAU,GAAG,KAAK,CAAC;oBACnB,MAAM;gBACV,KAAK,mCAAW,CAAC,SAAS;oBACtB,UAAU,GAAG,KAAK,CAAC;oBACnB,MAAM;aACb;YACD,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,2CAAW,IAAI,CAAC,oBAAoB,UAAK,UAAU,MAAG,CAAC;SAClF;IACL,CAAC;IAED;;OAEG;IACK,iDAAoB,GAA5B;QACI,IAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC,CAAC;QACpE,IAAI,SAAS,CAAC,MAAM,KAAK,mCAAW,CAAC,OAAO,EAAE;YAC1C,SAAS,CAAC,MAAM,GAAG,mCAAW,CAAC,SAAS,CAAC;YAEzC,QAAQ;YACR,IAAI,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,WAAW,EAAE;gBAC9C,IAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;gBACpE,IAAI,aAAa,CAAC,MAAM,KAAK,mCAAW,CAAC,MAAM,EAAE;oBAC7C,aAAa,CAAC,MAAM,GAAG,mCAAW,CAAC,OAAO,CAAC;iBAC9C;aACJ;YAED,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAC9B,IAAI,CAAC,iBAAiB,EAAE,CAAC;SAC5B;IACL,CAAC;IAED;;OAEG;IACK,wCAAW,GAAnB;QACI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAChD,IAAI,CAAC,KAAK,CAAC,EAAE;gBACT,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,mCAAW,CAAC,OAAO,CAAC;aACtD;iBAAM;gBACH,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,mCAAW,CAAC,MAAM,CAAC;aACrD;SACJ;QACD,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC;QAC9B,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAC9B,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;QACtB,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC7B,CAAC;IAED;;OAEG;IACK,8CAAiB,GAAzB;QACI,IAAM,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;QAC3D,IAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,eAAe,GAAG,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QAErE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAChD,IAAI,CAAC,GAAG,eAAe,EAAE;gBACrB,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,mCAAW,CAAC,SAAS,CAAC;aACxD;iBAAM,IAAI,CAAC,KAAK,eAAe,EAAE;gBAC9B,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,mCAAW,CAAC,OAAO,CAAC;aACtD;iBAAM;gBACH,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,mCAAW,CAAC,MAAM,CAAC;aACrD;SACJ;QAED,IAAI,CAAC,oBAAoB,GAAG,YAAY,CAAC;QACzC,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAC9B,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;QACjC,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC7B,CAAC;IA3YgB,kBAAkB;QADtC,OAAO;OACa,kBAAkB,CA4YtC;IAAD,yBAAC;CA5YD,AA4YC,CA5Y+C,EAAE,CAAC,SAAS,GA4Y3D;kBA5YoB,kBAAkB", "file": "", "sourceRoot": "/", "sourcesContent": ["// Learn TypeScript:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html\n// Learn Attribute:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html\n// Learn life-cycle callbacks:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html\n\nimport { LevelStatus } from \"./LevelSelectController\";\n\nconst { ccclass, property } = cc._decorator;\n\n/**\n * 关卡选择使用示例\n * 这个脚本展示了如何快速创建一个关卡选择界面\n */\n@ccclass\nexport default class LevelSelectExample extends cc.Component {\n\n    // UI节点\n    private scrollView: cc.ScrollView = null;\n    private content: cc.Node = null;\n    private infoLabel: cc.Label = null;\n\n    // 关卡数据\n    private levelDataList: any[] = [];\n    private currentSelectedLevel: number = 1;\n    private totalLevels: number = 30;\n    private levelItemWidth: number = 150;\n\n    // 关卡节点列表\n    private levelNodes: cc.Node[] = [];\n\n    onLoad() {\n        this.createUI();\n        this.initLevelData();\n        this.createLevelSelectUI();\n    }\n\n    start() {\n        this.scrollToLevel(this.currentSelectedLevel);\n        this.updateInfoDisplay();\n    }\n\n    /**\n     * 创建UI结构\n     */\n    private createUI() {\n        // 创建背景\n        const bg = new cc.Node(\"Background\");\n        bg.addComponent(cc.Sprite);\n        bg.setContentSize(750, 1334);\n        bg.color = cc.Color.BLACK;\n        bg.parent = this.node;\n\n        // 创建标题\n        const titleNode = new cc.Node(\"Title\");\n        const titleLabel = titleNode.addComponent(cc.Label);\n        titleLabel.string = \"选择关卡\";\n        titleLabel.fontSize = 36;\n        titleLabel.node.color = cc.Color.WHITE;\n        titleNode.setPosition(0, 500);\n        titleNode.parent = this.node;\n\n        // 创建ScrollView\n        const scrollViewNode = new cc.Node(\"ScrollView\");\n        this.scrollView = scrollViewNode.addComponent(cc.ScrollView);\n        scrollViewNode.setContentSize(650, 150);\n        scrollViewNode.setPosition(0, 0);\n        scrollViewNode.parent = this.node;\n\n        // 创建Viewport\n        const viewport = new cc.Node(\"Viewport\");\n        viewport.addComponent(cc.Mask);\n        viewport.setContentSize(650, 150);\n        viewport.parent = scrollViewNode;\n\n        // 创建Content\n        this.content = new cc.Node(\"Content\");\n        this.content.setContentSize(650, 150);\n        this.content.parent = viewport;\n\n        // 设置ScrollView属性\n        this.scrollView.content = this.content;\n        this.scrollView.horizontal = true;\n        this.scrollView.vertical = false;\n        this.scrollView.inertia = true;\n        this.scrollView.elastic = true;\n\n        // 修复Scrollbar问题\n        this.scrollView.horizontalScrollBar = null;\n        this.scrollView.verticalScrollBar = null;\n\n        // 创建信息标签\n        const infoNode = new cc.Node(\"InfoLabel\");\n        this.infoLabel = infoNode.addComponent(cc.Label);\n        this.infoLabel.string = \"当前选中: 关卡1\";\n        this.infoLabel.fontSize = 24;\n        this.infoLabel.node.color = cc.Color.WHITE;\n        infoNode.setPosition(0, -200);\n        infoNode.parent = this.node;\n\n        // 创建测试按钮\n        this.createTestButtons();\n    }\n\n    /**\n     * 创建测试按钮\n     */\n    private createTestButtons() {\n        // 完成关卡按钮\n        const completeBtn = this.createButton(\"完成当前关卡\", cc.v2(-150, -300), () => {\n            this.completeCurrentLevel();\n        });\n\n        // 重置按钮\n        const resetBtn = this.createButton(\"重置关卡\", cc.v2(0, -300), () => {\n            this.resetLevels();\n        });\n\n        // 随机进度按钮\n        const randomBtn = this.createButton(\"随机进度\", cc.v2(150, -300), () => {\n            this.setRandomProgress();\n        });\n    }\n\n    /**\n     * 创建按钮\n     */\n    private createButton(text: string, position: cc.Vec2, callback: Function): cc.Node {\n        const btnNode = new cc.Node(\"Button\");\n        const button = btnNode.addComponent(cc.Button);\n        const sprite = btnNode.addComponent(cc.Sprite);\n        \n        // 设置按钮外观\n        btnNode.setContentSize(120, 40);\n        btnNode.color = cc.Color.BLUE;\n        btnNode.setPosition(position);\n        btnNode.parent = this.node;\n\n        // 添加文字\n        const labelNode = new cc.Node(\"Label\");\n        const label = labelNode.addComponent(cc.Label);\n        label.string = text;\n        label.fontSize = 16;\n        label.node.color = cc.Color.WHITE;\n        labelNode.parent = btnNode;\n\n        // 设置点击事件\n        btnNode.on('click', callback, this);\n\n        return btnNode;\n    }\n\n    /**\n     * 初始化关卡数据\n     */\n    private initLevelData() {\n        this.levelDataList = [];\n        for (let i = 1; i <= this.totalLevels; i++) {\n            let status: LevelStatus;\n            if (i === 1) {\n                status = LevelStatus.CURRENT;\n            } else if (i <= 3) {\n                status = LevelStatus.COMPLETED;\n            } else {\n                status = LevelStatus.LOCKED;\n            }\n\n            this.levelDataList.push({\n                levelNumber: i,\n                status: status\n            });\n        }\n    }\n\n    /**\n     * 创建关卡选择UI\n     */\n    private createLevelSelectUI() {\n        if (!this.content) return;\n\n        // 清空现有内容\n        this.content.removeAllChildren();\n        this.levelNodes = [];\n\n        // 计算总宽度\n        const totalWidth = (this.totalLevels - 1) * this.levelItemWidth + 650;\n        this.content.width = totalWidth;\n\n        for (let i = 0; i < this.totalLevels; i++) {\n            const levelData = this.levelDataList[i];\n            \n            // 创建关卡节点\n            const levelNode = this.createLevelNode(levelData);\n            this.content.addChild(levelNode);\n            this.levelNodes.push(levelNode);\n\n            // 设置位置\n            const posX = i * this.levelItemWidth - totalWidth / 2 + 650 / 2;\n            levelNode.setPosition(posX, 0);\n\n            // 创建连接线（除了最后一个关卡）\n            if (i < this.totalLevels - 1) {\n                const lineNode = this.createLineNode();\n                this.content.addChild(lineNode);\n                lineNode.setPosition(posX + this.levelItemWidth / 2, 0);\n            }\n        }\n    }\n\n    /**\n     * 创建关卡节点\n     */\n    private createLevelNode(levelData: any): cc.Node {\n        const node = new cc.Node(`Level_${levelData.levelNumber}`);\n        \n        // 添加Sprite组件\n        const sprite = node.addComponent(cc.Sprite);\n        \n        // 添加Label组件显示关卡数字\n        const labelNode = new cc.Node(\"LevelLabel\");\n        const label = labelNode.addComponent(cc.Label);\n        label.string = levelData.levelNumber.toString();\n        label.fontSize = 20;\n        label.node.color = cc.Color.WHITE;\n        labelNode.parent = node;\n\n        // 添加Button组件\n        const button = node.addComponent(cc.Button);\n        button.target = node;\n\n        // 设置点击事件\n        node.on('click', () => {\n            this.onLevelClicked(levelData.levelNumber);\n        }, this);\n\n        // 更新关卡外观\n        this.updateLevelNodeAppearance(node, levelData, false);\n\n        return node;\n    }\n\n    /**\n     * 创建连接线节点\n     */\n    private createLineNode(): cc.Node {\n        const node = new cc.Node(\"Line\");\n        const sprite = node.addComponent(cc.Sprite);\n        node.setContentSize(6, 6);\n        node.color = cc.Color.WHITE;\n\n        return node;\n    }\n\n    /**\n     * 更新关卡节点外观\n     */\n    private updateLevelNodeAppearance(node: cc.Node, levelData: any, isSelected: boolean) {\n        let size = cc.size(46, 46);\n        let color = cc.Color.GRAY;\n\n        // 根据状态确定颜色和大小\n        if (isSelected) {\n            size = cc.size(86, 86);\n        }\n\n        switch (levelData.status) {\n            case LevelStatus.LOCKED:\n                color = cc.Color.GRAY;\n                break;\n            case LevelStatus.CURRENT:\n                color = cc.Color.YELLOW;\n                break;\n            case LevelStatus.COMPLETED:\n                color = cc.Color.GREEN;\n                break;\n        }\n\n        // 设置节点大小和颜色\n        node.setContentSize(size);\n        node.color = color;\n    }\n\n    /**\n     * 更新所有关卡显示\n     */\n    private updateAllLevelsDisplay() {\n        for (let i = 0; i < this.levelNodes.length; i++) {\n            const node = this.levelNodes[i];\n            const levelData = this.levelDataList[i];\n            const isSelected = (levelData.levelNumber === this.currentSelectedLevel);\n            \n            this.updateLevelNodeAppearance(node, levelData, isSelected);\n        }\n    }\n\n    /**\n     * 滚动到指定关卡\n     */\n    private scrollToLevel(levelNumber: number) {\n        if (levelNumber < 1 || levelNumber > this.totalLevels) return;\n\n        const targetIndex = levelNumber - 1;\n        const contentWidth = this.content.width;\n        const scrollViewWidth = this.scrollView.node.width;\n        \n        const targetOffset = (targetIndex * this.levelItemWidth) / (contentWidth - scrollViewWidth);\n        const clampedOffset = cc.misc.clampf(targetOffset, 0, 1);\n        \n        this.scrollView.scrollToPercentHorizontal(clampedOffset, 0.3);\n    }\n\n    /**\n     * 关卡点击事件处理\n     */\n    private onLevelClicked(levelNumber: number) {\n        const levelData = this.levelDataList[levelNumber - 1];\n        if (levelData.status === LevelStatus.LOCKED) {\n        \n            return;\n        }\n\n        this.currentSelectedLevel = levelNumber;\n        this.updateAllLevelsDisplay();\n        this.scrollToLevel(levelNumber);\n        this.updateInfoDisplay();\n        \n       \n    }\n\n    /**\n     * 更新信息显示\n     */\n    private updateInfoDisplay() {\n        if (this.infoLabel) {\n            const levelData = this.levelDataList[this.currentSelectedLevel - 1];\n            let statusText = \"\";\n            switch (levelData.status) {\n                case LevelStatus.LOCKED:\n                    statusText = \"未解锁\";\n                    break;\n                case LevelStatus.CURRENT:\n                    statusText = \"进行中\";\n                    break;\n                case LevelStatus.COMPLETED:\n                    statusText = \"已通关\";\n                    break;\n            }\n            this.infoLabel.string = `当前选中: 关卡${this.currentSelectedLevel} (${statusText})`;\n        }\n    }\n\n    /**\n     * 完成当前关卡\n     */\n    private completeCurrentLevel() {\n        const levelData = this.levelDataList[this.currentSelectedLevel - 1];\n        if (levelData.status === LevelStatus.CURRENT) {\n            levelData.status = LevelStatus.COMPLETED;\n            \n            // 解锁下一关\n            if (this.currentSelectedLevel < this.totalLevels) {\n                const nextLevelData = this.levelDataList[this.currentSelectedLevel];\n                if (nextLevelData.status === LevelStatus.LOCKED) {\n                    nextLevelData.status = LevelStatus.CURRENT;\n                }\n            }\n            \n            this.updateAllLevelsDisplay();\n            this.updateInfoDisplay();\n        }\n    }\n\n    /**\n     * 重置关卡\n     */\n    private resetLevels() {\n        for (let i = 0; i < this.levelDataList.length; i++) {\n            if (i === 0) {\n                this.levelDataList[i].status = LevelStatus.CURRENT;\n            } else {\n                this.levelDataList[i].status = LevelStatus.LOCKED;\n            }\n        }\n        this.currentSelectedLevel = 1;\n        this.updateAllLevelsDisplay();\n        this.scrollToLevel(1);\n        this.updateInfoDisplay();\n    }\n\n    /**\n     * 设置随机进度\n     */\n    private setRandomProgress() {\n        const completedLevels = Math.floor(Math.random() * 10) + 1;\n        const currentLevel = Math.min(completedLevels + 1, this.totalLevels);\n\n        for (let i = 0; i < this.levelDataList.length; i++) {\n            if (i < completedLevels) {\n                this.levelDataList[i].status = LevelStatus.COMPLETED;\n            } else if (i === completedLevels) {\n                this.levelDataList[i].status = LevelStatus.CURRENT;\n            } else {\n                this.levelDataList[i].status = LevelStatus.LOCKED;\n            }\n        }\n\n        this.currentSelectedLevel = currentLevel;\n        this.updateAllLevelsDisplay();\n        this.scrollToLevel(currentLevel);\n        this.updateInfoDisplay();\n    }\n}\n"]}