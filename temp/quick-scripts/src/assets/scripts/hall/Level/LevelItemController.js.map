{"version": 3, "sources": ["assets/scripts/hall/Level/LevelItemController.ts"], "names": [], "mappings": ";;;;;AAAA,oBAAoB;AACpB,4EAA4E;AAC5E,mBAAmB;AACnB,sFAAsF;AACtF,8BAA8B;AAC9B,sFAAsF;;;;;;;;;;;;;;;;;;;;;AAEtF,iEAAsD;AAEhD,IAAA,KAAwB,EAAE,CAAC,UAAU,EAAnC,OAAO,aAAA,EAAE,QAAQ,cAAkB,CAAC;AAG5C;IAAiD,uCAAY;IAA7D;QAAA,qEAoPC;QAjPG,iBAAW,GAAc,IAAI,CAAC;QAG9B,gBAAU,GAAa,IAAI,CAAC;QAG5B,iBAAW,GAAc,IAAI,CAAC;QAEtB,iBAAW,GAAW,CAAC,CAAC;QACxB,iBAAW,GAAgB,mCAAW,CAAC,MAAM,CAAC;QAC9C,gBAAU,GAAY,KAAK,CAAC;QAI5B,sBAAgB,GAAW,EAAE,CAAC,CAAC,cAAc;;IAmOzD,CAAC;4BApPoB,mBAAmB;IAmBpC,oCAAM,GAAN;QACI,UAAU;QACV,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACnB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;SACnD;QACD,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAClB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,sBAAsB,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;SAC3D;QACD,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACnB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;SACnD;IACL,CAAC;IAED,mCAAK,GAAL;QACI,gBAAgB;QAChB,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACI,0CAAY,GAAnB,UAAoB,WAAmB,EAAE,MAAmB,EAAE,QAAyB;QAAzB,yBAAA,EAAA,gBAAyB;QACnF,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,WAAW,GAAG,MAAM,CAAC;QAC1B,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC;QAE3B,SAAS;QACT,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,WAAW,CAAC,QAAQ,EAAE,CAAC;SACnD;QAED,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACK,4CAAc,GAAtB,UAAuB,WAAmB;QACtC,OAAO,WAAW,GAAG,CAAC,KAAK,CAAC,CAAC;IACjC,CAAC;IAED;;OAEG;IACK,8CAAgB,GAAxB;QACI,IAAI,CAAC,IAAI,CAAC,WAAW;YAAE,OAAO;QAE9B,IAAI,SAAS,GAAG,EAAE,CAAC;QACnB,IAAI,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QAE3B,6BAA6B;QAC7B,IAAM,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC7D,IAAM,QAAQ,GAAG,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;QAG5C,qBAAqB;QACrB,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YACvB,QAAQ,IAAI,CAAC,WAAW,EAAE;gBACtB,KAAK,mCAAW,CAAC,MAAM;oBACnB,SAAS,GAAG,qCAAmC,QAAQ,YAAS,CAAC;oBACjE,MAAM;gBACV,KAAK,mCAAW,CAAC,OAAO;oBACpB,SAAS,GAAG,uCAAqC,QAAQ,YAAS,CAAC;oBACnE,MAAM;gBACV,KAAK,mCAAW,CAAC,SAAS;oBACtB,SAAS,GAAG,sCAAoC,QAAQ,YAAS,CAAC;oBAClE,MAAM;aACb;SACJ;aAAM;YACH,QAAQ,IAAI,CAAC,WAAW,EAAE;gBACtB,KAAK,mCAAW,CAAC,MAAM;oBACnB,SAAS,GAAG,qCAAmC,QAAU,CAAC;oBAC1D,MAAM;gBACV,KAAK,mCAAW,CAAC,OAAO;oBACpB,SAAS,GAAG,uCAAqC,QAAU,CAAC;oBAC5D,MAAM;gBACV,KAAK,mCAAW,CAAC,SAAS;oBACtB,SAAS,GAAG,sCAAoC,QAAU,CAAC;oBAC3D,MAAM;aACb;SACJ;QAED,SAAS;QACT,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAE/B,0BAA0B;QAC1B,IAAI,IAAI,CAAC,gBAAgB,KAAK,SAAS,EAAE;YACrC,IAAI,CAAC,wBAAwB,CAAC,SAAS,CAAC,CAAC;YACzC,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC;SACrC;QAED,WAAW;QACX,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,WAAW,CAAC,YAAY,GAAG,CAAC,IAAI,CAAC,WAAW,KAAK,mCAAW,CAAC,MAAM,CAAC,CAAC;SAC7E;QAED,SAAS;QACT,IAAI,CAAC,eAAe,EAAE,CAAC;IAC3B,CAAC;IAED;;OAEG;IACI,yCAAW,GAAlB,UAAmB,QAAiB;QAChC,IAAI,IAAI,CAAC,UAAU,KAAK,QAAQ,EAAE;YAC9B,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC;YAC3B,IAAI,CAAC,gBAAgB,EAAE,CAAC;SAC3B;IACL,CAAC;IAED;;OAEG;IACI,4CAAc,GAArB;QACI,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED;;OAEG;IACI,4CAAc,GAArB;QACI,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED;;OAEG;IACI,2CAAa,GAApB;QACI,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED;;OAEG;IACK,6CAAe,GAAvB;QACI,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAClB,EAAE,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;YACnD,OAAO;SACV;QAED,YAAY;QACZ,IAAI,CAAC,UAAU,CAAC,QAAQ,GAAG,EAAE,CAAC;QAE9B,kBAAkB;QAClB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QAErD,SAAS;QACT,IAAI,CAAC,UAAU,CAAC,eAAe,GAAG,EAAE,CAAC,KAAK,CAAC,eAAe,CAAC,MAAM,CAAC;QAClE,IAAI,CAAC,UAAU,CAAC,aAAa,GAAG,EAAE,CAAC,KAAK,CAAC,aAAa,CAAC,MAAM,CAAC;QAE9D,oCAAoC;QACpC,oBAAoB;QAEpB,qBAAqB;QACrB,IAAI,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC,YAAY,CAAC,CAAC;QAC5D,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC,YAAY,CAAC,CAAC;SAC3D;QAED,eAAe;QACf,IAAI,YAAsB,CAAC;QAC3B,QAAQ,IAAI,CAAC,WAAW,EAAE;YACtB,KAAK,mCAAW,CAAC,MAAM;gBACnB,iBAAiB;gBACjB,YAAY,GAAG,EAAE,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;gBACvC,MAAM;YACV,KAAK,mCAAW,CAAC,OAAO;gBACpB,oBAAoB;gBACpB,YAAY,GAAG,EAAE,CAAC,KAAK,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;gBACpC,MAAM;YACV,KAAK,mCAAW,CAAC,SAAS;gBACtB,iBAAiB;gBACjB,YAAY,GAAG,EAAE,CAAC,KAAK,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC;gBACrC,MAAM;YACV;gBACI,YAAY,GAAG,EAAE,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;gBACvC,MAAM;SACb;QAED,OAAO,CAAC,KAAK,GAAG,YAAY,CAAC;QAC7B,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC;QAElB,WAAW;QACX,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC3C,CAAC;IAED;;;OAGG;IACK,sDAAwB,GAAhC,UAAiC,SAAiB;QAAlD,iBAmBC;QAlBG,QAAQ;QACR,IAAM,iBAAiB,GAAG,qBAAmB,CAAC,gBAAgB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC9E,IAAI,iBAAiB,IAAI,IAAI,CAAC,WAAW,EAAE;YACvC,IAAI,CAAC,WAAW,CAAC,WAAW,GAAG,iBAAiB,CAAC;YACjD,OAAO;SACV;QAED,aAAa;QACb,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,CAAC,WAAW,EAAE,UAAC,GAAG,EAAE,WAAW;YAC1D,IAAI,CAAC,GAAG,IAAI,WAAW,IAAI,KAAI,CAAC,WAAW,EAAE;gBACzC,OAAO;gBACP,KAAI,CAAC,WAAW,CAAC,WAAW,GAAG,WAA6B,CAAC;gBAC7D,OAAO;gBACP,qBAAmB,CAAC,gBAAgB,CAAC,GAAG,CAAC,SAAS,EAAE,WAA6B,CAAC,CAAC;aACtF;iBAAM,IAAI,GAAG,EAAE;gBACZ,EAAE,CAAC,KAAK,CAAC,4BAA0B,SAAW,EAAE,GAAG,CAAC,CAAC;aACxD;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACI,0CAAY,GAAnB;QACI,IAAI,IAAI,CAAC,WAAW,KAAK,mCAAW,CAAC,MAAM,EAAE;YAEzC,OAAO;SACV;QAED,WAAW;QACX,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;IAEvD,CAAC;;IApOD,gBAAgB;IACD,oCAAgB,GAAgC,IAAI,GAAG,EAAE,CAAC;IAbzE;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;4DACU;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,KAAK,CAAC;2DACS;IAG5B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;4DACU;IATb,mBAAmB;QADvC,OAAO;OACa,mBAAmB,CAoPvC;IAAD,0BAAC;CApPD,AAoPC,CApPgD,EAAE,CAAC,SAAS,GAoP5D;kBApPoB,mBAAmB", "file": "", "sourceRoot": "/", "sourcesContent": ["// Learn TypeScript:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html\n// Learn Attribute:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html\n// Learn life-cycle callbacks:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html\n\nimport { LevelStatus } from \"./LevelSelectController\";\n\nconst { ccclass, property } = cc._decorator;\n\n@ccclass\nexport default class LevelItemController extends cc.Component {\n\n    @property(cc.Sprite)\n    levelSprite: cc.Sprite = null;\n\n    @property(cc.Label)\n    levelLabel: cc.Label = null;\n\n    @property(cc.Button)\n    levelButton: cc.Button = null;\n\n    private levelNumber: number = 1;\n    private levelStatus: LevelStatus = LevelStatus.LOCKED;\n    private isSelected: boolean = false;\n\n    // 性能优化：缓存已加载的资源\n    private static spriteFrameCache: Map<string, cc.SpriteFrame> = new Map();\n    private currentImagePath: string = \"\"; // 记录当前使用的图片路径\n\n    onLoad() {\n        // 初始化组件引用\n        if (!this.levelSprite) {\n            this.levelSprite = this.getComponent(cc.Sprite);\n        }\n        if (!this.levelLabel) {\n            this.levelLabel = this.getComponentInChildren(cc.Label);\n        }\n        if (!this.levelButton) {\n            this.levelButton = this.getComponent(cc.Button);\n        }\n    }\n\n    start() {\n        // 确保在start时更新外观\n        this.updateAppearance();\n    }\n\n    /**\n     * 设置关卡数据\n     */\n    public setLevelData(levelNumber: number, status: LevelStatus, selected: boolean = false) {\n        this.levelNumber = levelNumber;\n        this.levelStatus = status;\n        this.isSelected = selected;\n\n        // 更新标签文本\n        if (this.levelLabel) {\n            this.levelLabel.string = levelNumber.toString();\n        }\n\n        this.updateAppearance();\n    }\n\n    /**\n     * 检查是否为特殊关卡（第5、10、15、20、25关）\n     */\n    private isSpecialLevel(levelNumber: number): boolean {\n        return levelNumber % 5 === 0;\n    }\n\n    /**\n     * 更新外观\n     */\n    private updateAppearance() {\n        if (!this.levelSprite) return;\n\n        let imagePath = \"\";\n        let size = cc.size(46, 46);\n\n        // 检查是否为特殊关卡（第5、10、15、20、25关）\n        const isSpecialLevel = this.isSpecialLevel(this.levelNumber);\n        const uiSuffix = isSpecialLevel ? \"01\" : \"\";\n\n\n        // 根据状态和是否选中确定图片路径和大小\n        if (this.isSelected) {\n            size = cc.size(86, 86);\n            switch (this.levelStatus) {\n                case LevelStatus.LOCKED:\n                    imagePath = `hall_page_res/Level_Btn/pop_gray${uiSuffix}_choose`;\n                    break;\n                case LevelStatus.CURRENT:\n                    imagePath = `hall_page_res/Level_Btn/pop_yellow${uiSuffix}_choose`;\n                    break;\n                case LevelStatus.COMPLETED:\n                    imagePath = `hall_page_res/Level_Btn/pop_green${uiSuffix}_choose`;\n                    break;\n            }\n        } else {\n            switch (this.levelStatus) {\n                case LevelStatus.LOCKED:\n                    imagePath = `hall_page_res/Level_Btn/pop_gray${uiSuffix}`;\n                    break;\n                case LevelStatus.CURRENT:\n                    imagePath = `hall_page_res/Level_Btn/pop_yellow${uiSuffix}`;\n                    break;\n                case LevelStatus.COMPLETED:\n                    imagePath = `hall_page_res/Level_Btn/pop_green${uiSuffix}`;\n                    break;\n            }\n        }\n\n        // 设置节点大小\n        this.node.setContentSize(size);\n\n        // 优化的图片加载：只有当图片路径改变时才重新加载\n        if (this.currentImagePath !== imagePath) {\n            this.loadSpriteFrameOptimized(imagePath);\n            this.currentImagePath = imagePath;\n        }\n\n        // 设置按钮交互状态\n        if (this.levelButton) {\n            this.levelButton.interactable = (this.levelStatus !== LevelStatus.LOCKED);\n        }\n\n        // 设置标签样式\n        this.setupLabelStyle();\n    }\n\n    /**\n     * 设置选中状态\n     */\n    public setSelected(selected: boolean) {\n        if (this.isSelected !== selected) {\n            this.isSelected = selected;\n            this.updateAppearance();\n        }\n    }\n\n    /**\n     * 获取关卡号\n     */\n    public getLevelNumber(): number {\n        return this.levelNumber;\n    }\n\n    /**\n     * 获取关卡状态\n     */\n    public getLevelStatus(): LevelStatus {\n        return this.levelStatus;\n    }\n\n    /**\n     * 是否选中\n     */\n    public getIsSelected(): boolean {\n        return this.isSelected;\n    }\n\n    /**\n     * 设置标签样式\n     */\n    private setupLabelStyle() {\n        if (!this.levelLabel) {\n            cc.warn(\"LevelItemController: levelLabel is null\");\n            return;\n        }\n\n        // 设置字体大小为30\n        this.levelLabel.fontSize = 30;\n\n        // 设置颜色为白色 #FFFFFF\n        this.levelLabel.node.color = cc.color(255, 255, 255);\n\n        // 设置居中对齐\n        this.levelLabel.horizontalAlign = cc.Label.HorizontalAlign.CENTER;\n        this.levelLabel.verticalAlign = cc.Label.VerticalAlign.CENTER;\n\n        // 注意：Cocos Creator的Label组件不支持直接设置加粗\n        // 如需加粗效果，需要使用加粗字体文件\n\n        // 添加外边框 LabelOutline\n        let outline = this.levelLabel.getComponent(cc.LabelOutline);\n        if (!outline) {\n            outline = this.levelLabel.addComponent(cc.LabelOutline);\n        }\n\n        // 根据关卡状态设置边框颜色\n        let outlineColor: cc.Color;\n        switch (this.levelStatus) {\n            case LevelStatus.LOCKED:\n                // 未解锁边框为 #7B7B7B\n                outlineColor = cc.color(123, 123, 123);\n                break;\n            case LevelStatus.CURRENT:\n                // 当前玩到的关卡边框 #CF5800\n                outlineColor = cc.color(207, 88, 0);\n                break;\n            case LevelStatus.COMPLETED:\n                // 已解锁边框为 #119C0F\n                outlineColor = cc.color(17, 156, 15);\n                break;\n            default:\n                outlineColor = cc.color(123, 123, 123);\n                break;\n        }\n\n        outline.color = outlineColor;\n        outline.width = 1;\n\n        // 确保标签位置居中\n        this.levelLabel.node.setPosition(0, 0);\n    }\n\n    /**\n     * 优化的图片加载方法：使用缓存避免重复加载\n     * @param imagePath 图片路径\n     */\n    private loadSpriteFrameOptimized(imagePath: string) {\n        // 先检查缓存\n        const cachedSpriteFrame = LevelItemController.spriteFrameCache.get(imagePath);\n        if (cachedSpriteFrame && this.levelSprite) {\n            this.levelSprite.spriteFrame = cachedSpriteFrame;\n            return;\n        }\n\n        // 缓存中没有，异步加载\n        cc.resources.load(imagePath, cc.SpriteFrame, (err, spriteFrame) => {\n            if (!err && spriteFrame && this.levelSprite) {\n                // 设置图片\n                this.levelSprite.spriteFrame = spriteFrame as cc.SpriteFrame;\n                // 缓存图片\n                LevelItemController.spriteFrameCache.set(imagePath, spriteFrame as cc.SpriteFrame);\n            } else if (err) {\n                cc.error(`Failed to load sprite: ${imagePath}`, err);\n            }\n        });\n    }\n\n    /**\n     * 关卡点击事件\n     */\n    public onLevelClick() {\n        if (this.levelStatus === LevelStatus.LOCKED) {\n            \n            return;\n        }\n\n        // 发送关卡选择事件\n        this.node.emit('level-selected', this.levelNumber);\n        \n    }\n}\n"]}