{"version": 3, "sources": ["assets/scripts/hall/HallParentController.ts"], "names": [], "mappings": ";;;;;AAAA,oBAAoB;AACpB,4EAA4E;AAC5E,mBAAmB;AACnB,sFAAsF;AACtF,8BAA8B;AAC9B,sFAAsF;;;;;;;;;;;;;;;;;;;;;AAEtF,yDAAwD;AAExD,iDAAgD;AAChD,6CAA4C;AAC5C,8CAA6C;AAC7C,4DAA2D;AAC3D,sDAAiD;AACjD,yCAAwC;AACxC,uCAAsC;AACtC,qEAAsF;AAEhF,IAAA,KAAwB,EAAE,CAAC,UAAU,EAAnC,OAAO,aAAA,EAAE,QAAQ,cAAkB,CAAC;AAG5C;IAAkD,wCAAY;IAA9D;QAAA,qEA4IC;QAzIG,gBAAU,GAAY,IAAI,CAAA;QAE1B,eAAS,GAAY,IAAI,CAAA;QAEzB,iBAAW,GAAa,IAAI,CAAC;QAE7B,kBAAY,GAAY,IAAI,CAAC,CAAC,MAAM;QAEpC,kBAAY,GAAY,IAAI,CAAC,CAAC,QAAQ;QAEtC,qBAAe,GAAY,IAAI,CAAC,CAAE,MAAM;QAExC,6BAAuB,GAA4B,IAAI,CAAC;QAExD,qBAAe,GAAoB,IAAI,CAAA,CAAE,WAAW;;QA0HpD,iBAAiB;IACrB,CAAC;IAjHG,eAAe;IACL,uCAAQ,GAAlB;QACI,IAAI,CAAC,UAAU,EAAE,CAAA;IACrB,CAAC;IAED,oCAAK,GAAL;QAAA,iBAoDC;QAlDG,aAAK,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,EAAE;YACjC,iBAAO,CAAC,KAAK,CAAC,WAAW,EAAE,CAAA;QAC/B,CAAC,CAAC,CAAA;QAEF,IAAI,iBAAO,CAAC,WAAW,EAAE,CAAC,YAAY,IAAI,IAAI,IAAI,iBAAO,CAAC,WAAW,EAAE,CAAC,YAAY,KAAK,EAAE,EAAE;YACzF,aAAK,CAAC,qBAAqB,CAAC,IAAI,CAAC,SAAS,EAAC,iBAAO,CAAC,WAAW,EAAE,CAAC,YAAY,CAAC,CAAA;SACjF;QAED,aAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,YAAY,EAAE,eAAM,CAAC,SAAS,GAAG,uBAAuB,EAAE,eAAM,CAAC,SAAS,GAAG,wBAAwB,EAAE;YAC/H,IAAI,IAAI,GAAG,KAAI,CAAC,uBAAuB,CAAC,mBAAmB,EAAE,CAAA;YAC7D,QAAQ,IAAI,EAAE;gBACV,KAAK,0CAAgB,CAAC,cAAc;oBAChC,IAAI,KAAI,CAAC,SAAS,EAAE;wBAChB,KAAI,CAAC,SAAS,EAAE,CAAA;qBACnB;oBACD,MAAK;gBACT,KAAK,0CAAgB,CAAC,oBAAoB;oBACtC,oFAAoF;oBACpF,WAAW;oBACX,mCAAgB,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,qBAAS,CAAC,kBAAkB,EAAE,EAAE,CAAC,CAAC;oBACzE,MAAK;gBACT,KAAK,0CAAgB,CAAC,mBAAmB;oBACrC,KAAI,CAAC,uBAAuB,CAAC,mBAAmB,CAAC,0CAAgB,CAAC,cAAc,CAAC,CAAA;oBACjF,MAAK;aACZ;QAGL,CAAC,CAAC,CAAC;QACH,aAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,YAAY,EAAE,eAAM,CAAC,SAAS,GAAG,uBAAuB,EAAE,eAAM,CAAC,SAAS,GAAG,wBAAwB,EAAE;YAC/H,IAAI,KAAI,CAAC,SAAS,EAAE;gBAChB,KAAI,CAAC,SAAS,EAAE,CAAA;aACnB;QACL,CAAC,CAAC,CAAC;QACH,aAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,eAAe,EAAE,eAAM,CAAC,SAAS,GAAG,0BAA0B,EAAE,eAAM,CAAC,SAAS,GAAG,2BAA2B,EAAE;YACxI,IAAI,KAAI,CAAC,YAAY,EAAE;gBACnB,KAAI,CAAC,YAAY,EAAE,CAAA;aACtB;QACL,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC;YAClC,eAAe;YACf,IAAI,KAAI,CAAC,UAAU,EAAE;gBACjB,KAAI,CAAC,UAAU,EAAE,CAAA;aACpB;QACL,CAAC,EAAE;YACC,IAAI,KAAI,CAAC,WAAW,EAAE;gBAClB,KAAI,CAAC,WAAW,EAAE,CAAA;aACrB;QACL,CAAC,EAAE,UAAC,MAAc,EAAE,QAAgB;YAChC,KAAI,CAAC,YAAY,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAA;QACvC,CAAC,CAAC,CAAA;IACN,CAAC;IAED,cAAc;IACd,uCAAQ,GAAR,UAAS,SAAmB,EAAE,SAAmB,EAAE,YAAsB,EAAE,UAAoB,EAAE,WAAqB,EAAE,YAAsB;QAG1I,IAAI,CAAC,SAAS,GAAG,SAAS,CAAA;QAC1B,IAAI,CAAC,SAAS,GAAG,SAAS,CAAA;QAC1B,IAAI,CAAC,YAAY,GAAG,YAAY,CAAA;QAChC,IAAI,CAAC,UAAU,GAAG,UAAU,CAAA;QAC5B,IAAI,CAAC,WAAW,GAAG,WAAW,CAAA;QAC9B,IAAI,CAAC,YAAY,GAAG,YAAY,CAAA;IACpC,CAAC;IAED,QAAQ;IACR,yCAAU,GAAV;QACI,IAAI,uBAAU,CAAC,WAAW,EAAE,CAAC,SAAS,EAAE;YACpC,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,aAAK,CAAC,SAAS,CAAC,uBAAU,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;SAC9F;IACL,CAAC;IAED,2BAA2B;IAC3B,0CAAW,GAAX;QACI,IAAI,IAAI,GAAG,IAAI,CAAC,uBAAuB,CAAC,mBAAmB,EAAE,CAAA;QAC7D,IAAI,IAAI,KAAK,0CAAgB,CAAC,oBAAoB,EAAE;YAChD,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,MAAM,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC,CAAA;YACrE,IAAI,CAAC,uBAAuB,CAAC,mBAAmB,CAAC,0CAAgB,CAAC,cAAc,CAAC,CAAA;SACpF;IACL,CAAC;IAED,UAAU;IACV,8CAAe,GAAf,UAAgB,YAA0B;QACtC,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC,YAAY,CAAC,CAAA;IAC9D,CAAC;IACD,MAAM;IACN,wCAAS,GAAT,UAAU,iBAAoC;QAC1C,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAA;IAC7D,CAAC;IAED,MAAM;IACN,sCAAO,GAAP;QACI,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,CAAA;IAC1C,CAAC;IACD,QAAQ;IACR,6CAAc,GAAd;QACI,IAAI,CAAC,uBAAuB,CAAC,cAAc,EAAE,CAAA;IACjD,CAAC;IACD,wCAAS,GAAT;QACI,IAAI,CAAC,uBAAuB,CAAC,SAAS,EAAE,CAAA;IAC5C,CAAC;IACD,SAAS;IACT,4CAAa,GAAb,UAAc,sBAA8C;QACxD,IAAI,CAAC,uBAAuB,CAAC,aAAa,CAAC,sBAAsB,CAAC,CAAA;IACtE,CAAC;IAtID;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;4DACQ;IAE1B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;2DACO;IAEzB;QADC,QAAQ,CAAC,EAAE,CAAC,KAAK,CAAC;6DACU;IAE7B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;8DACW;IAE7B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;8DACW;IAE7B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;iEACc;IAEhC;QADC,QAAQ,CAAC,iCAAuB,CAAC;yEACsB;IAExD;QADC,QAAQ,CAAC,yBAAe,CAAC;iEACa;IAjBtB,oBAAoB;QADxC,OAAO;OACa,oBAAoB,CA4IxC;IAAD,2BAAC;CA5ID,AA4IC,CA5IiD,EAAE,CAAC,SAAS,GA4I7D;kBA5IoB,oBAAoB", "file": "", "sourceRoot": "/", "sourcesContent": ["// Learn TypeScript:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html\n// Learn Attribute:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html\n// Learn life-cycle callbacks:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html\n\nimport { Publish } from \"../../meshTools/tools/Publish\";\nimport { AcceptInvite, NoticeLeaveInvite, NoticeUserInviteStatus } from \"../bean/GameBean\";\nimport { GlobalBean } from \"../bean/GlobalBean\";\nimport { GameMgr } from \"../common/GameMgr\";\nimport { MessageId } from \"../net/MessageId\";\nimport { WebSocketManager } from \"../net/WebSocketManager\";\nimport ToastController from \"../ToastController\";\nimport { Config } from \"../util/Config\";\nimport { Tools } from \"../util/Tools\";\nimport HallCenterLayController, { CenterLaytouType } from \"./HallCenterLayController\";\n\nconst { ccclass, property } = cc._decorator;\n\n@ccclass\nexport default class HallParentController extends cc.Component {\n\n    @property(cc.Node)\n    boardTitle: cc.Node = null\n    @property(cc.Node)\n    boardIcon: cc.Node = null\n    @property(cc.Label)\n    beansNumber: cc.Label = null;\n    @property(cc.Node)\n    boardBtnBack: cc.Node = null; //返回按钮\n    @property(cc.Node)\n    boardBtnInfo: cc.Node = null; //游戏简介按钮\n    @property(cc.Node)\n    boardBtnSetting: cc.Node = null;  //设置按钮\n    @property(HallCenterLayController)\n    hallCenterLayController: HallCenterLayController = null;\n    @property(ToastController)\n    toastController: ToastController = null  //toast 的布局\n\n    backClick: Function\n    infoClick: Function\n    settingClick: Function\n    startClick: Function\n    createClick: Function\n    seatCallback: Function\n\n\n    // onLoad () {}\n    protected onEnable(): void {\n        this.updateGold()\n    }\n\n    start() {\n\n        Tools.setTouchEvent(this.boardTitle, () => {\n            GameMgr.H5SDK.ShowAppShop()\n        })\n\n        if (Publish.GetInstance().currencyIcon != null && Publish.GetInstance().currencyIcon !== '') {\n            Tools.setNodeSpriteFrameUrl(this.boardIcon,Publish.GetInstance().currencyIcon)\n        }\n\n        Tools.imageButtonClick(this.boardBtnBack, Config.buttonRes + 'board_btn_back_normal', Config.buttonRes + 'board_btn_back_pressed', () => {\n            let type = this.hallCenterLayController.getCenterLaytouType()\n            switch (type) {\n                case CenterLaytouType.HALL_AUTO_VIEW:\n                    if (this.backClick) {\n                        this.backClick()\n                    }\n                    break\n                case CenterLaytouType.HALL_CREAT_ROOM_VIEW:\n                    // this.hallCenterLayController.setCenterLaytouType(CenterLaytouType.HALL_AUTO_VIEW)\n                    //发送离开房间的信息\n                    WebSocketManager.GetInstance().sendMsg(MessageId.MsgTypeLeaveInvite, {});\n                    break\n                case CenterLaytouType.HALL_JOIN_ROOM_VIEW:\n                    this.hallCenterLayController.setCenterLaytouType(CenterLaytouType.HALL_AUTO_VIEW)\n                    break\n            }\n\n\n        });\n        Tools.imageButtonClick(this.boardBtnInfo, Config.buttonRes + 'board_btn_info_normal', Config.buttonRes + 'board_btn_info_pressed', () => {\n            if (this.infoClick) {\n                this.infoClick()\n            }\n        });\n        Tools.imageButtonClick(this.boardBtnSetting, Config.buttonRes + 'board_btn_setting_normal', Config.buttonRes + 'board_btn_setting_pressed', () => {\n            if (this.settingClick) {\n                this.settingClick()\n            }\n        });\n        this.hallCenterLayController.setClick(() => {\n            //start 按钮的点击回调\n            if (this.startClick) {\n                this.startClick()\n            }\n        }, () => {\n            if (this.createClick) {\n                this.createClick()\n            }\n        }, (userId: string, nickname: string) => {\n            this.seatCallback(userId, nickname)\n        })\n    }\n\n    //设置按钮点击事件的回调的\n    setClick(backClick: Function, infoClick: Function, settingClick: Function, startClick: Function, createClick: Function, seatCallback: Function\n    ) {\n\n        this.backClick = backClick\n        this.infoClick = infoClick\n        this.settingClick = settingClick\n        this.startClick = startClick\n        this.createClick = createClick\n        this.seatCallback = seatCallback\n    }\n\n    //更新金币数量\n    updateGold() {\n        if (GlobalBean.GetInstance().loginData) {\n            this.beansNumber.string = Tools.NumToTBMK(GlobalBean.GetInstance().loginData.userInfo.coin)\n        }\n    }\n\n    //房间已经解散但是自己重连回来被遗留在 房间中的处理\n    exitTheRoom() {\n        let type = this.hallCenterLayController.getCenterLaytouType()\n        if (type === CenterLaytouType.HALL_CREAT_ROOM_VIEW) {\n            this.toastController.showContent(window.getLocalizedStr('LeaveRoom'))\n            this.hallCenterLayController.setCenterLaytouType(CenterLaytouType.HALL_AUTO_VIEW)\n        }\n    }\n\n    //设置接受邀请成功\n    setAcceptInvite(acceptInvite: AcceptInvite) {\n        this.hallCenterLayController.setAcceptInvite(acceptInvite)\n    }\n    //离开房间\n    leaveRoom(noticeLeaveInvite: NoticeLeaveInvite) {\n        this.hallCenterLayController.leaveRoom(noticeLeaveInvite)\n    }\n\n    //设置门票\n    setFees() {\n        this.hallCenterLayController.setFees()\n    }\n    //进入私人房间\n    joinCreateRoom() {\n        this.hallCenterLayController.joinCreateRoom()\n    }\n    joinError() {\n        this.hallCenterLayController.joinError()\n    }\n    //准备 取消准备\n    setReadyState(noticeUserInviteStatus: NoticeUserInviteStatus) {\n        this.hallCenterLayController.setReadyState(noticeUserInviteStatus)\n    }\n\n    // update (dt) {}\n}\n"]}