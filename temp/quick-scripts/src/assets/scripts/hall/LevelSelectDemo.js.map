{"version": 3, "sources": ["assets/scripts/hall/LevelSelectDemo.ts"], "names": [], "mappings": ";;;;;AAAA,oBAAoB;AACpB,4EAA4E;AAC5E,mBAAmB;AACnB,sFAAsF;AACtF,8BAA8B;AAC9B,sFAAsF;;;;;;;;;;;;;;;;;;;;;AAEtF,uEAA4D;AAEtD,IAAA,KAAwB,EAAE,CAAC,UAAU,EAAnC,OAAO,aAAA,EAAE,QAAQ,cAAkB,CAAC;AAE5C;;;GAGG;AAEH;IAA6C,mCAAY;IAAzD;QAAA,qEA6RC;QA1RG,gBAAU,GAAkB,IAAI,CAAC;QAGjC,aAAO,GAAY,IAAI,CAAC;QAGxB,eAAS,GAAa,IAAI,CAAC;QAE3B,OAAO;QACC,mBAAa,GAAU,EAAE,CAAC;QAC1B,0BAAoB,GAAW,CAAC,CAAC;QACjC,iBAAW,GAAW,EAAE,CAAC;QACzB,oBAAc,GAAW,GAAG,CAAC;QAErC,SAAS;QACD,gBAAU,GAAc,EAAE,CAAC;;IA2QvC,CAAC;IAzQG,gCAAM,GAAN;QACI,IAAI,CAAC,aAAa,EAAE,CAAC;QACrB,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC/B,CAAC;IAED,+BAAK,GAAL;QACI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;QAC9C,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC7B,CAAC;IAED;;OAEG;IACK,uCAAa,GAArB;QACI,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;QACxB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC,EAAE,EAAE;YACxC,IAAI,MAAM,SAAa,CAAC;YACxB,IAAI,CAAC,KAAK,CAAC,EAAE;gBACT,MAAM,GAAG,mCAAW,CAAC,OAAO,CAAC,CAAC,WAAW;aAC5C;iBAAM,IAAI,CAAC,IAAI,CAAC,EAAE;gBACf,MAAM,GAAG,mCAAW,CAAC,SAAS,CAAC,CAAC,aAAa;aAChD;iBAAM;gBACH,MAAM,GAAG,mCAAW,CAAC,MAAM,CAAC,CAAC,UAAU;aAC1C;YAED,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;gBACpB,WAAW,EAAE,CAAC;gBACd,MAAM,EAAE,MAAM;aACjB,CAAC,CAAC;SACN;IACL,CAAC;IAED;;OAEG;IACK,6CAAmB,GAA3B;QACI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACf,EAAE,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAC;YAC1C,OAAO;SACV;QAED,SAAS;QACT,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,CAAC;QACjC,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;QAErB,QAAQ;QACR,IAAM,UAAU,GAAG,CAAC,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,GAAG,GAAG,CAAC;QACtE,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,UAAU,CAAC;QAEhC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,EAAE,EAAE;YACvC,IAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;YAExC,SAAS;YACT,IAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;YAClD,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;YACjC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAEhC,OAAO;YACP,IAAM,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC,cAAc,GAAG,UAAU,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;YAChE,SAAS,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;YAE/B,kBAAkB;YAClB,IAAI,CAAC,GAAG,IAAI,CAAC,WAAW,GAAG,CAAC,EAAE;gBAC1B,IAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;gBACvC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;gBAChC,QAAQ,CAAC,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC,cAAc,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;aAC3D;SACJ;IACL,CAAC;IAED;;OAEG;IACK,yCAAe,GAAvB,UAAwB,SAAc;QAAtC,iBA2BC;QA1BG,IAAM,IAAI,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,WAAS,SAAS,CAAC,WAAa,CAAC,CAAC;QAE3D,aAAa;QACb,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;QAE7B,kBAAkB;QAClB,IAAM,SAAS,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC5C,IAAM,KAAK,GAAG,SAAS,CAAC,YAAY,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;QAC/C,KAAK,CAAC,MAAM,GAAG,SAAS,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC;QAChD,KAAK,CAAC,QAAQ,GAAG,EAAE,CAAC;QACpB,KAAK,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC;QAClC,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC;QAExB,aAAa;QACb,IAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;QAC5C,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC;QAErB,SAAS;QACT,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE;YACb,KAAI,CAAC,cAAc,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;QAC/C,CAAC,EAAE,IAAI,CAAC,CAAC;QAET,SAAS;QACT,IAAI,CAAC,yBAAyB,CAAC,IAAI,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;QAEvD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,wCAAc,GAAtB;QACI,IAAM,IAAI,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACjC,IAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;QAE5C,UAAU;QACV,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,kCAAkC,EAAE,EAAE,CAAC,WAAW,EAAE,UAAC,GAAG,EAAE,WAAW;YACnF,IAAI,CAAC,GAAG,IAAI,WAAW,EAAE;gBACrB,MAAM,CAAC,WAAW,GAAG,WAA6B,CAAC;aACtD;QACL,CAAC,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,mDAAyB,GAAjC,UAAkC,IAAa,EAAE,SAAc,EAAE,UAAmB;QAChF,IAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;QAC5C,IAAI,CAAC,MAAM;YAAE,OAAO;QAEpB,IAAI,SAAS,GAAG,EAAE,CAAC;QACnB,IAAI,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QAE3B,kBAAkB;QAClB,IAAI,UAAU,EAAE;YACZ,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YACvB,QAAQ,SAAS,CAAC,MAAM,EAAE;gBACtB,KAAK,mCAAW,CAAC,MAAM;oBACnB,SAAS,GAAG,yCAAyC,CAAC;oBACtD,MAAM;gBACV,KAAK,mCAAW,CAAC,OAAO;oBACpB,SAAS,GAAG,2CAA2C,CAAC;oBACxD,MAAM;gBACV,KAAK,mCAAW,CAAC,SAAS;oBACtB,SAAS,GAAG,0CAA0C,CAAC;oBACvD,MAAM;aACb;SACJ;aAAM;YACH,QAAQ,SAAS,CAAC,MAAM,EAAE;gBACtB,KAAK,mCAAW,CAAC,MAAM;oBACnB,SAAS,GAAG,kCAAkC,CAAC;oBAC/C,MAAM;gBACV,KAAK,mCAAW,CAAC,OAAO;oBACpB,SAAS,GAAG,oCAAoC,CAAC;oBACjD,MAAM;gBACV,KAAK,mCAAW,CAAC,SAAS;oBACtB,SAAS,GAAG,mCAAmC,CAAC;oBAChD,MAAM;aACb;SACJ;QAED,SAAS;QACT,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAE1B,UAAU;QACV,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,CAAC,WAAW,EAAE,UAAC,GAAG,EAAE,WAAW;YAC1D,IAAI,CAAC,GAAG,IAAI,WAAW,EAAE;gBACrB,MAAM,CAAC,WAAW,GAAG,WAA6B,CAAC;aACtD;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACK,gDAAsB,GAA9B;QACI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC7C,IAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YAChC,IAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;YACxC,IAAM,UAAU,GAAG,CAAC,SAAS,CAAC,WAAW,KAAK,IAAI,CAAC,oBAAoB,CAAC,CAAC;YAEzE,IAAI,CAAC,yBAAyB,CAAC,IAAI,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;SAC/D;IACL,CAAC;IAED;;OAEG;IACK,uCAAa,GAArB,UAAsB,WAAmB;QACrC,IAAI,WAAW,GAAG,CAAC,IAAI,WAAW,GAAG,IAAI,CAAC,WAAW;YAAE,OAAO;QAE9D,IAAM,WAAW,GAAG,WAAW,GAAG,CAAC,CAAC;QACpC,IAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;QACxC,IAAM,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC;QAEnD,aAAa;QACb,IAAM,YAAY,GAAG,CAAC,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,YAAY,GAAG,eAAe,CAAC,CAAC;QAE5F,cAAc;QACd,IAAM,aAAa,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAEzD,SAAS;QACT,IAAI,CAAC,UAAU,CAAC,yBAAyB,CAAC,aAAa,EAAE,GAAG,CAAC,CAAC;IAClE,CAAC;IAED;;OAEG;IACK,wCAAc,GAAtB,UAAuB,WAAmB;QACtC,kBAAkB;QAClB,IAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;QACtD,IAAI,SAAS,CAAC,MAAM,KAAK,mCAAW,CAAC,MAAM,EAAE;YAEzC,OAAO;SACV;QAED,WAAW;QACX,IAAI,CAAC,oBAAoB,GAAG,WAAW,CAAC;QACxC,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAE9B,UAAU;QACV,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;QAEhC,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAE7B,CAAC;IAED;;OAEG;IACK,2CAAiB,GAAzB;QACI,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC,CAAC;YACpE,IAAI,UAAU,GAAG,EAAE,CAAC;YACpB,QAAQ,SAAS,CAAC,MAAM,EAAE;gBACtB,KAAK,mCAAW,CAAC,MAAM;oBACnB,UAAU,GAAG,KAAK,CAAC;oBACnB,MAAM;gBACV,KAAK,mCAAW,CAAC,OAAO;oBACpB,UAAU,GAAG,KAAK,CAAC;oBACnB,MAAM;gBACV,KAAK,mCAAW,CAAC,SAAS;oBACtB,UAAU,GAAG,KAAK,CAAC;oBACnB,MAAM;aACb;YACD,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,2CAAW,IAAI,CAAC,oBAAoB,UAAK,UAAU,MAAG,CAAC;SAClF;IACL,CAAC;IAED;;OAEG;IACI,8CAAoB,GAA3B;QACI,IAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC,CAAC;QACpE,IAAI,SAAS,CAAC,MAAM,KAAK,mCAAW,CAAC,OAAO,EAAE;YAC1C,SAAS,CAAC,MAAM,GAAG,mCAAW,CAAC,SAAS,CAAC;YAEzC,QAAQ;YACR,IAAI,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,WAAW,EAAE;gBAC9C,IAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;gBACpE,IAAI,aAAa,CAAC,MAAM,KAAK,mCAAW,CAAC,MAAM,EAAE;oBAC7C,aAAa,CAAC,MAAM,GAAG,mCAAW,CAAC,OAAO,CAAC;iBAC9C;aACJ;YAED,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAC9B,IAAI,CAAC,iBAAiB,EAAE,CAAC;SAC5B;IACL,CAAC;IAzRD;QADC,QAAQ,CAAC,EAAE,CAAC,UAAU,CAAC;uDACS;IAGjC;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;oDACM;IAGxB;QADC,QAAQ,CAAC,EAAE,CAAC,KAAK,CAAC;sDACQ;IATV,eAAe;QADnC,OAAO;OACa,eAAe,CA6RnC;IAAD,sBAAC;CA7RD,AA6RC,CA7R4C,EAAE,CAAC,SAAS,GA6RxD;kBA7RoB,eAAe", "file": "", "sourceRoot": "/", "sourcesContent": ["// Learn TypeScript:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html\n// Learn Attribute:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html\n// Learn life-cycle callbacks:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html\n\nimport { LevelStatus } from \"./Level/LevelSelectController\";\n\nconst { ccclass, property } = cc._decorator;\n\n/**\n * 关卡选择演示控制器\n * 这个脚本展示了如何创建一个完整的关卡选择界面\n */\n@ccclass\nexport default class LevelSelectDemo extends cc.Component {\n\n    @property(cc.ScrollView)\n    scrollView: cc.ScrollView = null;\n\n    @property(cc.Node)\n    content: cc.Node = null;\n\n    @property(cc.Label)\n    infoLabel: cc.Label = null;\n\n    // 关卡数据\n    private levelDataList: any[] = [];\n    private currentSelectedLevel: number = 1;\n    private totalLevels: number = 30;\n    private levelItemWidth: number = 150;\n\n    // 关卡节点列表\n    private levelNodes: cc.Node[] = [];\n\n    onLoad() {\n        this.initLevelData();\n        this.createLevelSelectUI();\n    }\n\n    start() {\n        this.scrollToLevel(this.currentSelectedLevel);\n        this.updateInfoDisplay();\n    }\n\n    /**\n     * 初始化关卡数据\n     */\n    private initLevelData() {\n        this.levelDataList = [];\n        for (let i = 1; i <= this.totalLevels; i++) {\n            let status: LevelStatus;\n            if (i === 1) {\n                status = LevelStatus.CURRENT; // 第一关为当前关卡\n            } else if (i <= 3) {\n                status = LevelStatus.COMPLETED; // 前3关已通关（示例）\n            } else {\n                status = LevelStatus.LOCKED; // 其他关卡未解锁\n            }\n\n            this.levelDataList.push({\n                levelNumber: i,\n                status: status\n            });\n        }\n    }\n\n    /**\n     * 创建关卡选择UI\n     */\n    private createLevelSelectUI() {\n        if (!this.content) {\n            cc.error(\"Content node is not assigned!\");\n            return;\n        }\n\n        // 清空现有内容\n        this.content.removeAllChildren();\n        this.levelNodes = [];\n\n        // 计算总宽度\n        const totalWidth = (this.totalLevels - 1) * this.levelItemWidth + 650;\n        this.content.width = totalWidth;\n\n        for (let i = 0; i < this.totalLevels; i++) {\n            const levelData = this.levelDataList[i];\n            \n            // 创建关卡节点\n            const levelNode = this.createLevelNode(levelData);\n            this.content.addChild(levelNode);\n            this.levelNodes.push(levelNode);\n\n            // 设置位置\n            const posX = i * this.levelItemWidth - totalWidth / 2 + 650 / 2;\n            levelNode.setPosition(posX, 0);\n\n            // 创建连接线（除了最后一个关卡）\n            if (i < this.totalLevels - 1) {\n                const lineNode = this.createLineNode();\n                this.content.addChild(lineNode);\n                lineNode.setPosition(posX + this.levelItemWidth / 2, 0);\n            }\n        }\n    }\n\n    /**\n     * 创建关卡节点\n     */\n    private createLevelNode(levelData: any): cc.Node {\n        const node = new cc.Node(`Level_${levelData.levelNumber}`);\n        \n        // 添加Sprite组件\n        node.addComponent(cc.Sprite);\n        \n        // 添加Label组件显示关卡数字\n        const labelNode = new cc.Node(\"LevelLabel\");\n        const label = labelNode.addComponent(cc.Label);\n        label.string = levelData.levelNumber.toString();\n        label.fontSize = 20;\n        label.node.color = cc.Color.WHITE;\n        labelNode.parent = node;\n\n        // 添加Button组件\n        const button = node.addComponent(cc.Button);\n        button.target = node;\n\n        // 设置点击事件\n        node.on('click', () => {\n            this.onLevelClicked(levelData.levelNumber);\n        }, this);\n\n        // 更新关卡外观\n        this.updateLevelNodeAppearance(node, levelData, false);\n\n        return node;\n    }\n\n    /**\n     * 创建连接线节点\n     */\n    private createLineNode(): cc.Node {\n        const node = new cc.Node(\"Line\");\n        const sprite = node.addComponent(cc.Sprite);\n        \n        // 加载连接线图片\n        cc.resources.load(\"hall_page_res/Level_Btn/pop_line\", cc.SpriteFrame, (err, spriteFrame) => {\n            if (!err && spriteFrame) {\n                sprite.spriteFrame = spriteFrame as cc.SpriteFrame;\n            }\n        });\n\n        return node;\n    }\n\n    /**\n     * 更新关卡节点外观\n     */\n    private updateLevelNodeAppearance(node: cc.Node, levelData: any, isSelected: boolean) {\n        const sprite = node.getComponent(cc.Sprite);\n        if (!sprite) return;\n\n        let imagePath = \"\";\n        let size = cc.size(46, 46);\n\n        // 根据状态和是否选中确定图片路径\n        if (isSelected) {\n            size = cc.size(86, 86);\n            switch (levelData.status) {\n                case LevelStatus.LOCKED:\n                    imagePath = \"hall_page_res/Level_Btn/pop_gray_choose\";\n                    break;\n                case LevelStatus.CURRENT:\n                    imagePath = \"hall_page_res/Level_Btn/pop_yellow_choose\";\n                    break;\n                case LevelStatus.COMPLETED:\n                    imagePath = \"hall_page_res/Level_Btn/pop_green_choose\";\n                    break;\n            }\n        } else {\n            switch (levelData.status) {\n                case LevelStatus.LOCKED:\n                    imagePath = \"hall_page_res/Level_Btn/pop_gray\";\n                    break;\n                case LevelStatus.CURRENT:\n                    imagePath = \"hall_page_res/Level_Btn/pop_yellow\";\n                    break;\n                case LevelStatus.COMPLETED:\n                    imagePath = \"hall_page_res/Level_Btn/pop_green\";\n                    break;\n            }\n        }\n\n        // 设置节点大小\n        node.setContentSize(size);\n\n        // 加载并设置图片\n        cc.resources.load(imagePath, cc.SpriteFrame, (err, spriteFrame) => {\n            if (!err && spriteFrame) {\n                sprite.spriteFrame = spriteFrame as cc.SpriteFrame;\n            }\n        });\n    }\n\n    /**\n     * 更新所有关卡显示\n     */\n    private updateAllLevelsDisplay() {\n        for (let i = 0; i < this.levelNodes.length; i++) {\n            const node = this.levelNodes[i];\n            const levelData = this.levelDataList[i];\n            const isSelected = (levelData.levelNumber === this.currentSelectedLevel);\n            \n            this.updateLevelNodeAppearance(node, levelData, isSelected);\n        }\n    }\n\n    /**\n     * 滚动到指定关卡\n     */\n    private scrollToLevel(levelNumber: number) {\n        if (levelNumber < 1 || levelNumber > this.totalLevels) return;\n\n        const targetIndex = levelNumber - 1;\n        const contentWidth = this.content.width;\n        const scrollViewWidth = this.scrollView.node.width;\n        \n        // 计算目标位置的偏移量\n        const targetOffset = (targetIndex * this.levelItemWidth) / (contentWidth - scrollViewWidth);\n        \n        // 限制偏移量在有效范围内\n        const clampedOffset = cc.misc.clampf(targetOffset, 0, 1);\n        \n        // 设置滚动位置\n        this.scrollView.scrollToPercentHorizontal(clampedOffset, 0.3);\n    }\n\n    /**\n     * 关卡点击事件处理\n     */\n    private onLevelClicked(levelNumber: number) {\n        // 检查关卡是否可以选择（未锁定）\n        const levelData = this.levelDataList[levelNumber - 1];\n        if (levelData.status === LevelStatus.LOCKED) {\n        \n            return;\n        }\n\n        // 更新当前选中关卡\n        this.currentSelectedLevel = levelNumber;\n        this.updateAllLevelsDisplay();\n        \n        // 滚动到选中关卡\n        this.scrollToLevel(levelNumber);\n\n        this.updateInfoDisplay();\n   \n    }\n\n    /**\n     * 更新信息显示\n     */\n    private updateInfoDisplay() {\n        if (this.infoLabel) {\n            const levelData = this.levelDataList[this.currentSelectedLevel - 1];\n            let statusText = \"\";\n            switch (levelData.status) {\n                case LevelStatus.LOCKED:\n                    statusText = \"未解锁\";\n                    break;\n                case LevelStatus.CURRENT:\n                    statusText = \"进行中\";\n                    break;\n                case LevelStatus.COMPLETED:\n                    statusText = \"已通关\";\n                    break;\n            }\n            this.infoLabel.string = `当前选中: 关卡${this.currentSelectedLevel} (${statusText})`;\n        }\n    }\n\n    /**\n     * 完成当前关卡（测试用）\n     */\n    public completeCurrentLevel() {\n        const levelData = this.levelDataList[this.currentSelectedLevel - 1];\n        if (levelData.status === LevelStatus.CURRENT) {\n            levelData.status = LevelStatus.COMPLETED;\n            \n            // 解锁下一关\n            if (this.currentSelectedLevel < this.totalLevels) {\n                const nextLevelData = this.levelDataList[this.currentSelectedLevel];\n                if (nextLevelData.status === LevelStatus.LOCKED) {\n                    nextLevelData.status = LevelStatus.CURRENT;\n                }\n            }\n            \n            this.updateAllLevelsDisplay();\n            this.updateInfoDisplay();\n        }\n    }\n}\n"]}