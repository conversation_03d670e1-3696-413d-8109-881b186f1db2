"use strict";
cc._RF.push(module, '0a188/DymxMVpmLudNIvq/G', 'KickOutDialogController');
// scripts/hall/KickOutDialogController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var MessageId_1 = require("../net/MessageId");
var WebSocketManager_1 = require("../net/WebSocketManager");
var Config_1 = require("../util/Config");
var Tools_1 = require("../util/Tools");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var KickOutDialogController = /** @class */ (function (_super) {
    __extends(KickOutDialogController, _super);
    function KickOutDialogController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.boardBg = null;
        _this.boardBtnClose = null;
        _this.content = null;
        _this.cancelBtn = null;
        _this.kickOutBtn = null;
        _this.brownText = null;
        _this.userId = '';
        return _this;
        // update (dt) {}
    }
    KickOutDialogController.prototype.onLoad = function () {
        this.brownText = this.content.getComponent(cc.Label);
        this.localizedLabel = this.content.getComponent('LocalizedLabel');
    };
    KickOutDialogController.prototype.start = function () {
        var _this = this;
        Tools_1.Tools.imageButtonClick(this.boardBtnClose, Config_1.Config.buttonRes + 'board_btn_close_normal', Config_1.Config.buttonRes + 'board_btn_close_pressed', function () {
            _this.hide();
        });
        //cancel 按钮点击事件
        Tools_1.Tools.yellowButton(this.cancelBtn, function () {
            _this.hide();
        });
        //kickOut 按钮点击事件
        Tools_1.Tools.redButton(this.kickOutBtn, function () {
            _this.hide();
            //发送玩家被踢出的消息
            WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeInviteKickOut, { 'userId': _this.userId });
        });
    };
    KickOutDialogController.prototype.show = function (userId, nickname) {
        this.node.active = true;
        this.boardBg.scale = 0;
        this.userId = userId;
        this.brownText.string = window.getLocalizedStr('kickout2');
        this.localizedLabel.bindParam(this.truncateString(nickname));
        // 执行缩放动画
        cc.tween(this.boardBg)
            .to(Config_1.Config.dialogScaleTime, { scale: 1 })
            .start();
    };
    KickOutDialogController.prototype.hide = function () {
        var _this = this;
        // 执行缩放动画
        cc.tween(this.boardBg)
            .to(Config_1.Config.dialogScaleTime, { scale: 0 })
            .call(function () {
            _this.node.active = false;
        })
            .start();
    };
    KickOutDialogController.prototype.truncateString = function (str) {
        if (str.length > 10) {
            return str.slice(0, 10) + '...';
        }
        else {
            return str;
        }
    };
    __decorate([
        property(cc.Node)
    ], KickOutDialogController.prototype, "boardBg", void 0);
    __decorate([
        property(cc.Node)
    ], KickOutDialogController.prototype, "boardBtnClose", void 0);
    __decorate([
        property(cc.Node)
    ], KickOutDialogController.prototype, "content", void 0);
    __decorate([
        property(cc.Node)
    ], KickOutDialogController.prototype, "cancelBtn", void 0);
    __decorate([
        property(cc.Node)
    ], KickOutDialogController.prototype, "kickOutBtn", void 0);
    KickOutDialogController = __decorate([
        ccclass
    ], KickOutDialogController);
    return KickOutDialogController;
}(cc.Component));
exports.default = KickOutDialogController;

cc._RF.pop();