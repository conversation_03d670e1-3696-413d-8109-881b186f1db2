{"version": 3, "sources": ["assets/scripts/hall/PlayerLayoutController.ts"], "names": [], "mappings": ";;;;;AAAA,oBAAoB;AACpB,4EAA4E;AAC5E,mBAAmB;AACnB,sFAAsF;AACtF,8BAA8B;AAC9B,sFAAsF;;;;;;;;;;;;;;;;;;;;;AAEtF,iDAAgD;AAChD,uCAAsC;AAEhC,IAAA,KAAwB,EAAE,CAAC,UAAU,EAAnC,OAAO,aAAA,EAAE,QAAQ,cAAkB,CAAC;AAG5C;IAAoD,0CAAY;IAAhE;QAAA,qEAkEC;QA/DG,aAAO,GAAY,IAAI,CAAC,CAAC,aAAa;QAEtC,aAAO,GAAY,IAAI,CAAC,CAAC,aAAa;QAEtC,aAAO,GAAY,IAAI,CAAC,CAAC,aAAa;QAEtC,aAAO,GAAY,IAAI,CAAC,CAAC,aAAa;QAGtC,aAAO,GAAY,IAAI,CAAC,CAAC,YAAY;QAErC,MAAM;QACN,cAAQ,GAAW,GAAG,CAAC;;QAkDvB,iBAAiB;IACrB,CAAC;IAhDG,eAAe;IACL,yCAAQ,GAAlB;QACI,IAAK,OAAO,GAAG,uBAAU,CAAC,WAAW,EAAE,CAAC,OAAO,CAAA;QAC/C,QAAO,OAAO,EAAC;YACX,KAAK,CAAC;gBACF,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;gBACpC,MAAM;YACV,KAAK,CAAC;gBACF,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;gBACpC,MAAM;YACV,KAAK,CAAC;gBACF,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;gBACpC,MAAM;YACV,KAAK,CAAC;gBACF,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;gBACpC,MAAM;SACb;IAEL,CAAC;IAED,sCAAK,GAAL;QAEI,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,EAAC,CAAC,CAAC,CAAA;QAChC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,EAAC,CAAC,CAAC,CAAA;QAChC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,EAAC,CAAC,CAAC,CAAA;QAChC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,EAAC,CAAC,CAAC,CAAA;IACpC,CAAC;IAED,MAAM;IACN,4CAAW,GAAX,UAAY,UAAmB,EAAC,OAAc;QAA9C,iBAOC;QALG,aAAK,CAAC,aAAa,CAAC,UAAU,EAAE;YAC5B,uBAAU,CAAC,WAAW,EAAE,CAAC,OAAO,GAAG,OAAO,CAAA;YAC1C,KAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAA;QACtC,CAAC,CAAC,CAAA;IAEN,CAAC;IAED,OAAO;IACP,kDAAiB,GAAjB,UAAkB,UAAmB;QACjC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC;aACrB,EAAE,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,QAAQ,EAAE,UAAU,CAAC,QAAQ,EAAE,CAAC;aACpD,KAAK,EAAE,CAAC,CAAC,OAAO;IACrB,CAAC;IA1DD;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;2DACM;IAExB;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;2DACM;IAExB;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;2DACM;IAExB;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;2DACM;IAGxB;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;2DACM;IAZP,sBAAsB;QAD1C,OAAO;OACa,sBAAsB,CAkE1C;IAAD,6BAAC;CAlED,AAkEC,CAlEmD,EAAE,CAAC,SAAS,GAkE/D;kBAlEoB,sBAAsB", "file": "", "sourceRoot": "/", "sourcesContent": ["// Learn TypeScript:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html\n// Learn Attribute:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html\n// Learn life-cycle callbacks:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html\n\nimport { GlobalBean } from \"../bean/GlobalBean\";\nimport { Tools } from \"../util/Tools\";\n\nconst { ccclass, property } = cc._decorator;\n\n@ccclass\nexport default class PlayerLayoutController extends cc.Component {\n\n    @property(cc.Node)\n    player2: cc.Node = null; //玩家人数是 2 的节点\n    @property(cc.Node)\n    player3: cc.Node = null; //玩家人数是 3 的节点\n    @property(cc.Node)\n    player4: cc.Node = null; //玩家人数是 4 的节点\n    @property(cc.Node)\n    player5: cc.Node = null; //玩家人数是 5 的节点\n\n    @property(cc.Node)\n    sliding: cc.Node = null; //选中玩家人数的滑动条\n\n    //移动时间\n    moveTime: number = 0.1;\n\n\n    // onLoad () {}\n    protected onEnable(): void {\n        let  players = GlobalBean.GetInstance().players\n        switch(players){\n            case 2:\n                this.movePlayersliding(this.player2)\n                break;\n            case 3:\n                this.movePlayersliding(this.player3)\n                break;\n            case 4:\n                this.movePlayersliding(this.player4)\n                break;\n            case 5:\n                this.movePlayersliding(this.player5)\n                break;\n        }\n        \n    }\n\n    start() {\n        \n        this.setListener(this.player2,2)\n        this.setListener(this.player3,3)\n        this.setListener(this.player4,4)\n        this.setListener(this.player5,5)\n    }\n\n    //设置监听\n    setListener(playerNode: cc.Node,players:number) {\n\n        Tools.setTouchEvent(playerNode, () => {\n            GlobalBean.GetInstance().players = players\n            this.movePlayersliding(playerNode)\n        })\n\n    }\n\n    //移动滑动条\n    movePlayersliding(playerNode: cc.Node){\n        cc.tween(this.sliding)\n        .to(this.moveTime, { position: playerNode.position })\n        .start(); // 开始动画\n    }\n\n\n\n    // update (dt) {}\n}\n"]}