"use strict";
cc._RF.push(module, '7a33ccGFZVHdIRH0OyFmGFV', 'MatchParentController');
// scripts/hall/MatchParentController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Publish_1 = require("../../meshTools/tools/Publish");
var GlobalBean_1 = require("../bean/GlobalBean");
var EventCenter_1 = require("../common/EventCenter");
var GameMgr_1 = require("../common/GameMgr");
var MessageBaseBean_1 = require("../net/MessageBaseBean");
var MatchItemController_1 = require("../pfb/MatchItemController");
var Config_1 = require("../util/Config");
var Tools_1 = require("../util/Tools");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var MatchParentController = /** @class */ (function (_super) {
    __extends(MatchParentController, _super);
    function MatchParentController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.backBtn = null;
        _this.matchUserLay = null;
        _this.matchUserLay2 = null;
        _this.matchItem = null;
        _this._userListCol = []; //显示用户布局的controller
        _this.normalTime = function () { };
        return _this;
        // update (dt) {}
    }
    MatchParentController.prototype.onLoad = function () {
        if (cc.sys.os === cc.sys.OS_IOS) {
            // 在iOS设备上执行的代码
            if (Publish_1.Publish.GetInstance().gameMode != '2') { //非半屏的话
                this.backBtn.getComponent(cc.Widget).top = 146;
            }
        }
        else {
            // 在其他设备上执行的代码
        }
    };
    MatchParentController.prototype.onEnable = function () {
        this.backBtn.active = true;
    };
    MatchParentController.prototype.start = function () {
        var _this = this;
        //设置返回键的点击事件
        Tools_1.Tools.imageButtonClick(this.backBtn, Config_1.Config.buttonRes + 'board_btn_back_normal', Config_1.Config.buttonRes + 'board_btn_back_pressed', function () {
            if (_this.backClick) {
                _this.backClick();
            }
        });
    };
    MatchParentController.prototype.createMatchView = function () {
        var user_s = [];
        //判断是几人游戏
        var peopleNumber = GlobalBean_1.GlobalBean.GetInstance().players;
        var userInfo = GlobalBean_1.GlobalBean.GetInstance().loginData.userInfo;
        for (var i = 0; i < peopleNumber; i++) {
            if (i == 0) {
                var user = {
                    userId: userInfo.userId,
                    nickName: userInfo.nickname,
                    avatar: userInfo.avatar,
                    pos: 0,
                    coin: 0,
                    status: 0,
                    score: 0,
                    rank: 0
                };
                user_s.push(user);
            }
            else {
                user_s.push(null);
            }
        }
        //这里是添加一个等待的页面匹配的占位
        this.matchUserLay.removeAllChildren();
        this.matchUserLay2.removeAllChildren();
        this._userListCol = [];
        if (user_s.length <= 4) {
            for (var i = 0; i < user_s.length; i++) {
                var item = cc.instantiate(this.matchItem);
                this.matchUserLay.addChild(item);
                var matchingItemController = item.getComponent(MatchItemController_1.default);
                this._userListCol.push(matchingItemController);
                matchingItemController.setData(user_s[i]);
            }
        }
        else {
            var arrayData = Tools_1.Tools.chunkArray(user_s, 3); //根据人数进行分块
            for (var i = 0; i < arrayData[0].length; i++) {
                var item = cc.instantiate(this.matchItem);
                this.matchUserLay.addChild(item);
                var matchingItemController = item.getComponent(MatchItemController_1.default);
                this._userListCol.push(matchingItemController);
                matchingItemController.setData(user_s[i]);
            }
            for (var i = 0; i < arrayData[1].length; i++) {
                var item = cc.instantiate(this.matchItem);
                this.matchUserLay2.addChild(item);
                var matchingItemController = item.getComponent(MatchItemController_1.default);
                this._userListCol.push(matchingItemController);
                matchingItemController.setData(user_s[3 + i]);
            }
        }
    };
    //设置游戏数据
    MatchParentController.prototype.setGameData = function () {
        this.backBtn.active = false; //匹配成功之后 隐藏掉返回键
        var user = GlobalBean_1.GlobalBean.GetInstance().adjustUserData();
        var index = user.findIndex(function (item) { return item.userId === GlobalBean_1.GlobalBean.GetInstance().loginData.userInfo.userId; }); //搜索
        GlobalBean_1.GlobalBean.GetInstance().loginData.userInfo.coin = user[index].coin; //更新自己的最新金币
        //这里是匹配成功之后的真实数据
        for (var i = 0; i < user.length; i++) {
            if (i <= this._userListCol.length) {
                this._userListCol[i].setData(user[i]);
            }
        }
        this.normalTime = function () {
            var autoMessageBean = {
                'msgId': MessageBaseBean_1.AutoMessageId.SwitchGameSceneMsg,
                'data': {}
            };
            GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.AutoMessage, autoMessageBean); //进入游戏的消息
        };
        this.scheduleOnce(this.normalTime, 1);
    };
    MatchParentController.prototype.onDisable = function () {
        this.unschedule(this.normalTime);
    };
    MatchParentController.prototype.setClick = function (backClick) {
        this.backClick = backClick;
    };
    __decorate([
        property(cc.Node)
    ], MatchParentController.prototype, "backBtn", void 0);
    __decorate([
        property(cc.Node)
    ], MatchParentController.prototype, "matchUserLay", void 0);
    __decorate([
        property(cc.Node)
    ], MatchParentController.prototype, "matchUserLay2", void 0);
    __decorate([
        property(cc.Prefab)
    ], MatchParentController.prototype, "matchItem", void 0);
    MatchParentController = __decorate([
        ccclass
    ], MatchParentController);
    return MatchParentController;
}(cc.Component));
exports.default = MatchParentController;

cc._RF.pop();