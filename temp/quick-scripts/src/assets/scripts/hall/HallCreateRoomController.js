"use strict";
cc._RF.push(module, '2c0a4vmyEBEzJ0UUy+JIUO4', 'HallCreateRoomController');
// scripts/hall/HallCreateRoomController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var GlobalBean_1 = require("../bean/GlobalBean");
var SeatItemController_1 = require("../pfb/SeatItemController");
var Config_1 = require("../util/Config");
var Tools_1 = require("../util/Tools");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var ButtonStyle;
(function (ButtonStyle) {
    ButtonStyle[ButtonStyle["StartGray"] = 1] = "StartGray";
    ButtonStyle[ButtonStyle["StartGreen"] = 2] = "StartGreen";
    ButtonStyle[ButtonStyle["Ready"] = 3] = "Ready";
    ButtonStyle[ButtonStyle["Cancel"] = 4] = "Cancel";
})(ButtonStyle || (ButtonStyle = {}));
var HallCreateRoomController = /** @class */ (function (_super) {
    __extends(HallCreateRoomController, _super);
    function HallCreateRoomController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.boardTab = null;
        _this.boardBgInner = null;
        _this.seatLayout = null;
        _this.seatTwoLayout = null; //第二排的座位
        _this.buttonNoClick = null; //灰色的 start 按钮
        _this.buttonStart = null; //可点击的 start 按钮
        _this.buttonReady = null; //准备按钮
        _this.buttonCancel = null; // 取消按钮
        _this.setaItemPfb = null;
        _this.btnCopyNormal = null; //copy按钮  
        _this.btnCopyLabel = null; //copy按钮文案   
        _this.roomNumber = null; //房间号   
        _this.ticketNumber = null; //门票价格 
        _this.countdownTimeLabel = null;
        _this.countdownInterval = null; //倒计时的 id
        _this.seconds = 10; //倒计时 10 秒
        _this._seatListCol = []; //显示用户布局controller
        return _this;
        // update (dt) {}
    }
    HallCreateRoomController.prototype.onLoad = function () {
        this.btnCopyNormal = this.boardTab.getChildByName('btn_copy_normal');
        this.btnCopyLabel = this.btnCopyNormal.getChildByName('label');
        this.roomNumber = this.boardTab.getChildByName('label').getComponent(cc.Label);
        this.ticketNumber = this.boardBgInner.getChildByName('ticket_number').getComponent(cc.Label);
        this.countdownTimeLabel = this.buttonReady.getChildByName('buttonLabel_time').getComponent(cc.Label);
    };
    HallCreateRoomController.prototype.onEnable = function () {
        var _this = this;
        this.scheduleOnce(function () {
            _this.updateCountdownLabel(_this.seconds);
            Tools_1.Tools.setCountDownTimeLabel(_this.buttonReady);
            _this.refreshData(GlobalBean_1.GlobalBean.GetInstance().inviteInfo);
        });
    };
    HallCreateRoomController.prototype.start = function () {
        var _this = this;
        Tools_1.Tools.grayButton(this.buttonNoClick, function () { });
        Tools_1.Tools.greenButton(this.buttonStart, function () {
            if (_this.startCallBack) {
                _this.startCallBack();
            }
        });
        Tools_1.Tools.greenButton(this.buttonReady, function () {
            if (_this.readyCallBack) {
                _this.readyCallBack();
            }
        });
        Tools_1.Tools.redButton(this.buttonCancel, function () {
            if (_this.cancelCallBack) {
                _this.cancelCallBack();
            }
        });
        //copy 按钮的点击事件
        Tools_1.Tools.setTouchEvent(this.btnCopyNormal, function (node) {
            Tools_1.Tools.setNodeSpriteFrame(node, Config_1.Config.hallRes + 'btn_copy_pressed');
            var color = cc.Color.fromHEX(new cc.Color(), '#925333');
            _this.btnCopyLabel.color = color;
            _this.btnCopyLabel.getComponent(cc.Label).fontSize = 26;
            Tools_1.Tools.copyToClipboard(_this.roomNumber.string);
        }, function (node) {
            Tools_1.Tools.setNodeSpriteFrame(node, Config_1.Config.hallRes + 'btn_copy_normal');
            var color = cc.Color.fromHEX(new cc.Color(), '#D07649');
            _this.btnCopyLabel.color = color;
            _this.btnCopyLabel.getComponent(cc.Label).fontSize = 28;
        }, function (node) {
            Tools_1.Tools.setNodeSpriteFrame(node, Config_1.Config.hallRes + 'btn_copy_normal');
            var color = cc.Color.fromHEX(new cc.Color(), '#D07649');
            _this.btnCopyLabel.color = color;
            _this.btnCopyLabel.getComponent(cc.Label).fontSize = 28;
        });
    };
    //刷新数据
    HallCreateRoomController.prototype.refreshData = function (inviteInfo) {
        if (inviteInfo == null) {
            return;
        }
        //这里是门票价格的赋值
        var fees = inviteInfo.fee;
        this.ticketNumber.string = fees === 0 ? window.getLocalizedStr('free') : fees + '';
        this._seatListCol = [];
        this.refreshPlayer(inviteInfo);
        this.roomNumber.string = inviteInfo.inviteCode + ''; //邀请码
        //初始进来就两种状态 房主是准备开始按钮  玩家是 ready 按钮
        if (inviteInfo.creatorId === GlobalBean_1.GlobalBean.GetInstance().loginData.userInfo.userId) { //判断自己是不是房主
            this.btnCopyNormal.active = true;
        }
        else {
            this.btnCopyNormal.active = false;
        }
    };
    //刷新座位玩家
    HallCreateRoomController.prototype.refreshPlayer = function (inviteInfo) {
        var _this = this;
        var peopleNumber = inviteInfo.playerNum;
        if (this._seatListCol.length < 1) {
            this.seatLayout.removeAllChildren();
            this.seatTwoLayout.removeAllChildren();
            if (peopleNumber <= 4) {
                this.schedule(function () {
                    _this.seatLayout.setPosition(_this.seatLayout.x, -90);
                }, 0);
                this.seatTwoLayout.active = false;
                var _loop_1 = function (i) {
                    var item = cc.instantiate(this_1.setaItemPfb);
                    var seatEmptyItemController = item.getComponent(SeatItemController_1.default);
                    this_1._seatListCol.push(seatEmptyItemController);
                    this_1.seatLayout.addChild(item);
                    Tools_1.Tools.setTouchEvent(item, function () {
                        if (seatEmptyItemController.getUsers() != null) {
                            _this.setSetaItemClick(seatEmptyItemController.getUsers().userId, seatEmptyItemController.getUsers().nickname);
                        }
                    });
                };
                var this_1 = this;
                for (var i = 0; i < peopleNumber; i++) {
                    _loop_1(i);
                }
            }
            else {
                this.schedule(function () {
                    _this.seatLayout.setPosition(_this.seatLayout.x, -10);
                }, 0);
                this.seatTwoLayout.active = true;
                var _loop_2 = function (i) {
                    var item = cc.instantiate(this_2.setaItemPfb);
                    var seatEmptyItemController = item.getComponent(SeatItemController_1.default);
                    this_2._seatListCol.push(seatEmptyItemController);
                    this_2.seatLayout.addChild(item);
                    Tools_1.Tools.setTouchEvent(item, function () {
                        if (seatEmptyItemController.getUsers() != null) {
                            _this.setSetaItemClick(seatEmptyItemController.getUsers().userId, seatEmptyItemController.getUsers().nickname);
                        }
                    });
                };
                var this_2 = this;
                for (var i = 0; i < 3; i++) {
                    _loop_2(i);
                }
                var _loop_3 = function (i) {
                    var item = cc.instantiate(this_3.setaItemPfb);
                    var seatEmptyItemController = item.getComponent(SeatItemController_1.default);
                    this_3._seatListCol.push(seatEmptyItemController);
                    this_3.seatTwoLayout.addChild(item);
                    Tools_1.Tools.setTouchEvent(item, function () {
                        if (seatEmptyItemController.getUsers() != null) {
                            _this.setSetaItemClick(seatEmptyItemController.getUsers().userId, seatEmptyItemController.getUsers().nickname);
                        }
                    });
                };
                var this_3 = this;
                for (var i = 0; i < peopleNumber - 3; i++) {
                    _loop_3(i);
                }
            }
        }
        for (var i = 0; i < this._seatListCol.length; i++) {
            if (i < inviteInfo.users.length) {
                this._seatListCol[i].setData(inviteInfo.users[i]);
            }
            else {
                this._seatListCol[i].setData(null);
            }
        }
        if (GlobalBean_1.GlobalBean.GetInstance().inviteInfo.creatorId === GlobalBean_1.GlobalBean.GetInstance().loginData.userInfo.userId) { //判断自己是不是房主
            var readyCount = GlobalBean_1.GlobalBean.GetInstance().inviteInfo.users.filter(function (user) { return user.ready; }).length; //查找准备好的玩家数量
            if (GlobalBean_1.GlobalBean.GetInstance().inviteInfo.playerNum === readyCount) { //准备好的玩家数量和总玩家数量一致的话就可以开始游戏了
                this.setButtonType(ButtonStyle.StartGreen);
            }
            else {
                this.setButtonType(ButtonStyle.StartGray);
            }
        }
        else {
            var index = GlobalBean_1.GlobalBean.GetInstance().inviteInfo.users.findIndex(function (item) { return item.userId === GlobalBean_1.GlobalBean.GetInstance().loginData.userInfo.userId; }); //搜索
            if (GlobalBean_1.GlobalBean.GetInstance().inviteInfo.users[index].ready) {
                this.setButtonType(ButtonStyle.Cancel);
            }
            else {
                this.setButtonType(ButtonStyle.Ready);
                this.startCountdown(this.seconds);
            }
        }
    };
    HallCreateRoomController.prototype.onDisable = function () {
        var _this = this;
        setTimeout(function () {
            // this.ticketNumber.string = ''
            _this.roomNumber.string = '';
            _this.seatLayout.removeAllChildren();
            _this.seatTwoLayout.removeAllChildren();
        }, 100);
        if (this.countdownInterval) {
            clearInterval(this.countdownInterval);
            this.countdownInterval = null;
        }
    };
    //设置按钮显示状态
    HallCreateRoomController.prototype.setButtonType = function (buttonStyle) {
        this.buttonNoClick.active = false;
        this.buttonStart.active = false;
        this.buttonReady.active = false;
        this.buttonCancel.active = false;
        switch (buttonStyle) {
            case ButtonStyle.StartGray:
                this.buttonNoClick.active = true;
                break;
            case ButtonStyle.StartGreen:
                this.buttonStart.active = true;
                break;
            case ButtonStyle.Ready:
                this.buttonReady.active = true;
                break;
            case ButtonStyle.Cancel:
                this.buttonCancel.active = true;
                break;
        }
    };
    //启动倒计时
    HallCreateRoomController.prototype.startCountdown = function (seconds) {
        var _this = this;
        if (this.countdownInterval) {
            // GameMgr.Console.Error('当前存在的定时器 先销毁id：'+this.countdownInterval)
            clearInterval(this.countdownInterval);
            this.countdownInterval = null;
        }
        var remainingSeconds = seconds;
        this.updateCountdownLabel(remainingSeconds);
        this.countdownInterval = setInterval(function () {
            remainingSeconds--;
            if (remainingSeconds <= 0) {
                // GameMgr.Console.Error('自动销毁的定时器 id：'+this.countdownInterval)
                clearInterval(_this.countdownInterval);
                _this.countdownInterval = null;
                // 倒计时结束时的处理逻辑
                if (_this.readyCallBack) {
                    _this.readyCallBack();
                }
                return;
            }
            // GameMgr.Console.Error('执行的定时器 id：'+this.countdownInterval)
            _this.updateCountdownLabel(remainingSeconds);
        }, 1000);
        // GameMgr.Console.Error('创建的定时器 id：'+this.countdownInterval)
    };
    HallCreateRoomController.prototype.updateCountdownLabel = function (seconds) {
        if (this.countdownTimeLabel) {
            this.countdownTimeLabel.string = "\uFF08" + seconds + "s\uFF09";
        }
    };
    //有玩家离开
    HallCreateRoomController.prototype.leavePlayer = function (noticeLeaveInvite) {
        GlobalBean_1.GlobalBean.GetInstance().inviteInfo.users = GlobalBean_1.GlobalBean.GetInstance().inviteInfo.users.filter(function (user) { return user.userId !== noticeLeaveInvite.userId; });
        this.refreshPlayer(GlobalBean_1.GlobalBean.GetInstance().inviteInfo);
    };
    //准备 取消准备
    HallCreateRoomController.prototype.setReadyState = function (noticeUserInviteStatus) {
        if (GlobalBean_1.GlobalBean.GetInstance().inviteInfo == null || GlobalBean_1.GlobalBean.GetInstance().loginData == null) {
            return;
        }
        GlobalBean_1.GlobalBean.GetInstance().inviteInfo.users.forEach(function (user) {
            if (user.userId === noticeUserInviteStatus.userId) {
                user.ready = noticeUserInviteStatus.ready;
            }
        });
        this.refreshPlayer(GlobalBean_1.GlobalBean.GetInstance().inviteInfo);
    };
    //设置 item 的点击事件
    HallCreateRoomController.prototype.setSetaItemClick = function (userId, nickname) {
        //得先判断当前的房间我是不是房主  并且要提出的不是我自己
        if (GlobalBean_1.GlobalBean.GetInstance().inviteInfo.creatorId === GlobalBean_1.GlobalBean.GetInstance().loginData.userInfo.userId
            && userId != GlobalBean_1.GlobalBean.GetInstance().loginData.userInfo.userId) {
            if (this.seatCallback) {
                this.seatCallback(userId, nickname);
            }
        }
    };
    //设置点击的回调
    HallCreateRoomController.prototype.setClick = function (seatCallback, startCallBack, readyCallBack, cancelCallBack) {
        this.seatCallback = seatCallback;
        this.startCallBack = startCallBack;
        this.readyCallBack = readyCallBack;
        this.cancelCallBack = cancelCallBack;
    };
    __decorate([
        property(cc.Node)
    ], HallCreateRoomController.prototype, "boardTab", void 0);
    __decorate([
        property(cc.Node)
    ], HallCreateRoomController.prototype, "boardBgInner", void 0);
    __decorate([
        property(cc.Node)
    ], HallCreateRoomController.prototype, "seatLayout", void 0);
    __decorate([
        property(cc.Node)
    ], HallCreateRoomController.prototype, "seatTwoLayout", void 0);
    __decorate([
        property(cc.Node)
    ], HallCreateRoomController.prototype, "buttonNoClick", void 0);
    __decorate([
        property(cc.Node)
    ], HallCreateRoomController.prototype, "buttonStart", void 0);
    __decorate([
        property(cc.Node)
    ], HallCreateRoomController.prototype, "buttonReady", void 0);
    __decorate([
        property(cc.Node)
    ], HallCreateRoomController.prototype, "buttonCancel", void 0);
    __decorate([
        property(cc.Prefab)
    ], HallCreateRoomController.prototype, "setaItemPfb", void 0);
    HallCreateRoomController = __decorate([
        ccclass
    ], HallCreateRoomController);
    return HallCreateRoomController;
}(cc.Component));
exports.default = HallCreateRoomController;

cc._RF.pop();