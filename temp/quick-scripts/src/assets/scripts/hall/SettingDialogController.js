"use strict";
cc._RF.push(module, '6caf8s2g5RK05G0nSu4NIPc', 'SettingDialogController');
// scripts/hall/SettingDialogController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Publish_1 = require("../../meshTools/tools/Publish");
var AudioManager_1 = require("../util/AudioManager");
var Config_1 = require("../util/Config");
var LocalStorageManager_1 = require("../util/LocalStorageManager");
var Tools_1 = require("../util/Tools");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var SettingDialogController = /** @class */ (function (_super) {
    __extends(SettingDialogController, _super);
    function SettingDialogController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.boardBg = null;
        _this.boardBtnClose = null;
        _this.contentLay = null;
        _this.musicBtn = null;
        _this.soundBtn = null;
        _this.versoion = null; //版本号
        _this.music = true;
        _this.sound = true;
        _this.backCallback = null; //隐藏弹窗的回调
        return _this;
        // update (dt) {}
    }
    SettingDialogController.prototype.onLoad = function () {
        this.music = LocalStorageManager_1.LocalStorageManager.GetInstance().getMusicSwitch();
        this.sound = LocalStorageManager_1.LocalStorageManager.GetInstance().getSoundSwitch();
    };
    SettingDialogController.prototype.start = function () {
        var _this = this;
        this.versoion.string = "V " + Publish_1.Publish.GetInstance().getVersion();
        Tools_1.Tools.imageButtonClick(this.boardBtnClose, Config_1.Config.buttonRes + 'board_btn_close_normal', Config_1.Config.buttonRes + 'board_btn_close_pressed', function () {
            _this.hide();
        });
        this.setSwitch(this.musicBtn, this.music);
        this.setSwitch(this.soundBtn, this.sound);
        Tools_1.Tools.setTouchEvent(this.musicBtn, function (node) {
            _this.music = !_this.music;
            LocalStorageManager_1.LocalStorageManager.GetInstance().setMusicSwitch(_this.music);
            _this.setSwitch(node, _this.music);
            if (_this.music) {
                AudioManager_1.AudioManager.playBgm();
            }
            else {
                AudioManager_1.AudioManager.stopBgm();
            }
        });
        Tools_1.Tools.setTouchEvent(this.soundBtn, function (node) {
            _this.sound = !_this.sound;
            LocalStorageManager_1.LocalStorageManager.GetInstance().setSoundSwitch(_this.sound);
            _this.setSwitch(node, _this.sound);
        });
    };
    SettingDialogController.prototype.setSwitch = function (node, switchType) {
        if (switchType) {
            Tools_1.Tools.setNodeSpriteFrame(node, Config_1.Config.buttonRes + 'btn_switch_02');
        }
        else {
            Tools_1.Tools.setNodeSpriteFrame(node, Config_1.Config.buttonRes + 'btn_switch_01');
        }
    };
    SettingDialogController.prototype.show = function (backCallback) {
        this.backCallback = backCallback;
        this.node.active = true;
        this.boardBg.scale = 0;
        // 执行缩放动画
        cc.tween(this.boardBg)
            .to(Config_1.Config.dialogScaleTime, { scale: 1 })
            .start();
    };
    SettingDialogController.prototype.hide = function () {
        var _this = this;
        if (this.backCallback) {
            this.backCallback();
        }
        // 执行缩放动画
        cc.tween(this.boardBg)
            .to(Config_1.Config.dialogScaleTime, { scale: 0 })
            .call(function () {
            _this.node.active = false;
        })
            .start();
    };
    __decorate([
        property(cc.Node)
    ], SettingDialogController.prototype, "boardBg", void 0);
    __decorate([
        property(cc.Node)
    ], SettingDialogController.prototype, "boardBtnClose", void 0);
    __decorate([
        property(cc.Node)
    ], SettingDialogController.prototype, "contentLay", void 0);
    __decorate([
        property(cc.Node)
    ], SettingDialogController.prototype, "musicBtn", void 0);
    __decorate([
        property(cc.Node)
    ], SettingDialogController.prototype, "soundBtn", void 0);
    __decorate([
        property(cc.Label)
    ], SettingDialogController.prototype, "versoion", void 0);
    SettingDialogController = __decorate([
        ccclass
    ], SettingDialogController);
    return SettingDialogController;
}(cc.Component));
exports.default = SettingDialogController;

cc._RF.pop();