{"version": 3, "sources": ["assets/scripts/hall/HallCreateRoomController.ts"], "names": [], "mappings": ";;;;;AAAA,oBAAoB;AACpB,4EAA4E;AAC5E,mBAAmB;AACnB,sFAAsF;AACtF,8BAA8B;AAC9B,sFAAsF;;;;;;;;;;;;;;;;;;;;;AAGtF,iDAAgD;AAEhD,gEAA2D;AAC3D,yCAAwC;AACxC,uCAAsC;AAEhC,IAAA,KAAwB,EAAE,CAAC,UAAU,EAAnC,OAAO,aAAA,EAAE,QAAQ,cAAkB,CAAC;AAG5C,IAAK,WAKJ;AALD,WAAK,WAAW;IACZ,uDAAa,CAAA;IACb,yDAAc,CAAA;IACd,+CAAS,CAAA;IACT,iDAAU,CAAA;AACd,CAAC,EALI,WAAW,KAAX,WAAW,QAKf;AAGD;IAAsD,4CAAY;IAAlE;QAAA,qEAkUC;QA/TG,cAAQ,GAAY,IAAI,CAAC;QAEzB,kBAAY,GAAY,IAAI,CAAC;QAE7B,gBAAU,GAAY,IAAI,CAAC;QAE3B,mBAAa,GAAY,IAAI,CAAC,CAAE,QAAQ;QAExC,mBAAa,GAAY,IAAI,CAAC,CAAG,cAAc;QAE/C,iBAAW,GAAY,IAAI,CAAC,CAAE,eAAe;QAE7C,iBAAW,GAAY,IAAI,CAAC,CAAC,MAAM;QAEnC,kBAAY,GAAY,IAAI,CAAC,CAAE,OAAO;QAEtC,iBAAW,GAAc,IAAI,CAAC;QAG9B,mBAAa,GAAY,IAAI,CAAA,CAAE,UAAU;QACzC,kBAAY,GAAY,IAAI,CAAA,CAAE,aAAa;QAC3C,gBAAU,GAAa,IAAI,CAAA,CAAE,QAAQ;QACrC,kBAAY,GAAa,IAAI,CAAA,CAAE,OAAO;QAOtC,wBAAkB,GAAa,IAAI,CAAA;QACnC,uBAAiB,GAAW,IAAI,CAAC,CAAA,SAAS;QAC1C,aAAO,GAAW,EAAE,CAAC,CAAA,UAAU;QAEvB,kBAAY,GAAyB,EAAE,CAAC,CAAC,kBAAkB;;QA6RnE,iBAAiB;IACrB,CAAC;IA5RG,yCAAM,GAAN;QACI,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAA;QACpE,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,OAAO,CAAC,CAAA;QAC9D,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,EAAE,CAAC,KAAK,CAAC,CAAA;QAC9E,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC,YAAY,CAAC,EAAE,CAAC,KAAK,CAAC,CAAA;QAC5F,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,kBAAkB,CAAC,CAAC,YAAY,CAAC,EAAE,CAAC,KAAK,CAAC,CAAA;IAExG,CAAC;IACS,2CAAQ,GAAlB;QAAA,iBAMC;QALG,IAAI,CAAC,YAAY,CAAC;YACd,KAAI,CAAC,oBAAoB,CAAC,KAAI,CAAC,OAAO,CAAC,CAAC;YACxC,aAAK,CAAC,qBAAqB,CAAC,KAAI,CAAC,WAAW,CAAC,CAAA;YAC7C,KAAI,CAAC,WAAW,CAAC,uBAAU,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC,CAAA;QACzD,CAAC,CAAC,CAAA;IACN,CAAC;IAED,wCAAK,GAAL;QAAA,iBAwCC;QAtCG,aAAK,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,EAAE,cAAQ,CAAC,CAAC,CAAA;QAE/C,aAAK,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,EAAE;YAChC,IAAI,KAAI,CAAC,aAAa,EAAE;gBACpB,KAAI,CAAC,aAAa,EAAE,CAAA;aACvB;QACL,CAAC,CAAC,CAAA;QACF,aAAK,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,EAAE;YAChC,IAAI,KAAI,CAAC,aAAa,EAAE;gBACpB,KAAI,CAAC,aAAa,EAAE,CAAA;aACvB;QACL,CAAC,CAAC,CAAA;QAEF,aAAK,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,EAAE;YAC/B,IAAI,KAAI,CAAC,cAAc,EAAE;gBACrB,KAAI,CAAC,cAAc,EAAE,CAAA;aACxB;QACL,CAAC,CAAC,CAAA;QAEF,cAAc;QACd,aAAK,CAAC,aAAa,CAAC,IAAI,CAAC,aAAa,EAAE,UAAC,IAAa;YAClD,aAAK,CAAC,kBAAkB,CAAC,IAAI,EAAE,eAAM,CAAC,OAAO,GAAG,kBAAkB,CAAC,CAAC;YACpE,IAAI,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,EAAE,SAAS,CAAC,CAAC;YACxD,KAAI,CAAC,YAAY,CAAC,KAAK,GAAG,KAAK,CAAC;YAChC,KAAI,CAAC,YAAY,CAAC,YAAY,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,QAAQ,GAAG,EAAE,CAAC;YACvD,aAAK,CAAC,eAAe,CAAC,KAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QAClD,CAAC,EAAE,UAAC,IAAa;YACb,aAAK,CAAC,kBAAkB,CAAC,IAAI,EAAE,eAAM,CAAC,OAAO,GAAG,iBAAiB,CAAC,CAAC;YACnE,IAAI,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,EAAE,SAAS,CAAC,CAAC;YACxD,KAAI,CAAC,YAAY,CAAC,KAAK,GAAG,KAAK,CAAC;YAChC,KAAI,CAAC,YAAY,CAAC,YAAY,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,QAAQ,GAAG,EAAE,CAAC;QAC3D,CAAC,EAAE,UAAC,IAAa;YACb,aAAK,CAAC,kBAAkB,CAAC,IAAI,EAAE,eAAM,CAAC,OAAO,GAAG,iBAAiB,CAAC,CAAC;YACnE,IAAI,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,EAAE,SAAS,CAAC,CAAC;YACxD,KAAI,CAAC,YAAY,CAAC,KAAK,GAAG,KAAK,CAAC;YAChC,KAAI,CAAC,YAAY,CAAC,YAAY,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,QAAQ,GAAG,EAAE,CAAC;QAC3D,CAAC,CAAC,CAAC;IAEP,CAAC;IAED,MAAM;IACN,8CAAW,GAAX,UAAY,UAAsB;QAE9B,IAAI,UAAU,IAAI,IAAI,EAAE;YACpB,OAAO;SACV;QAED,YAAY;QACZ,IAAI,IAAI,GAAG,UAAU,CAAC,GAAG,CAAC;QAC1B,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE,CAAC;QAGnF,IAAI,CAAC,YAAY,GAAG,EAAE,CAAA;QACtB,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAA;QAC9B,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,UAAU,CAAC,UAAU,GAAG,EAAE,CAAA,CAAC,KAAK;QAGzD,mCAAmC;QACnC,IAAI,UAAU,CAAC,SAAS,KAAK,uBAAU,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAC,WAAW;YACzF,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,IAAI,CAAC;SACpC;aAAM;YACH,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,KAAK,CAAC;SACrC;IAEL,CAAC;IAGD,QAAQ;IACR,gDAAa,GAAb,UAAc,UAAsB;QAApC,iBA6EC;QA5EG,IAAI,YAAY,GAAW,UAAU,CAAC,SAAS,CAAC;QAChD,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE;YAC9B,IAAI,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAC;YACpC,IAAI,CAAC,aAAa,CAAC,iBAAiB,EAAE,CAAC;YAEvC,IAAI,YAAY,IAAI,CAAC,EAAE;gBACnB,IAAI,CAAC,QAAQ,CAAC;oBACV,KAAI,CAAC,UAAU,CAAC,WAAW,CAAC,KAAI,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;gBACxD,CAAC,EAAE,CAAC,CAAC,CAAA;gBACL,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,KAAK,CAAA;wCACxB,CAAC;oBACN,IAAM,IAAI,GAAG,EAAE,CAAC,WAAW,CAAC,OAAK,WAAW,CAAC,CAAA;oBAC7C,IAAI,uBAAuB,GAAG,IAAI,CAAC,YAAY,CAAC,4BAAkB,CAAC,CAAA;oBACnE,OAAK,YAAY,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAA;oBAC/C,OAAK,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;oBAC/B,aAAK,CAAC,aAAa,CAAC,IAAI,EAAE;wBACtB,IAAI,uBAAuB,CAAC,QAAQ,EAAE,IAAI,IAAI,EAAE;4BAC5C,KAAI,CAAC,gBAAgB,CAAC,uBAAuB,CAAC,QAAQ,EAAE,CAAC,MAAM,EAAE,uBAAuB,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAA;yBAChH;oBACL,CAAC,CAAC,CAAA;;;gBATN,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,EAAE,CAAC,EAAE;4BAA5B,CAAC;iBAUT;aACJ;iBAAM;gBACH,IAAI,CAAC,QAAQ,CAAC;oBACV,KAAI,CAAC,UAAU,CAAC,WAAW,CAAC,KAAI,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;gBACxD,CAAC,EAAE,CAAC,CAAC,CAAA;gBACL,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,IAAI,CAAA;wCACvB,CAAC;oBACN,IAAM,IAAI,GAAG,EAAE,CAAC,WAAW,CAAC,OAAK,WAAW,CAAC,CAAA;oBAC7C,IAAI,uBAAuB,GAAG,IAAI,CAAC,YAAY,CAAC,4BAAkB,CAAC,CAAA;oBACnE,OAAK,YAAY,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAA;oBAC/C,OAAK,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;oBAC/B,aAAK,CAAC,aAAa,CAAC,IAAI,EAAE;wBACtB,IAAI,uBAAuB,CAAC,QAAQ,EAAE,IAAI,IAAI,EAAE;4BAC5C,KAAI,CAAC,gBAAgB,CAAC,uBAAuB,CAAC,QAAQ,EAAE,CAAC,MAAM,EAAE,uBAAuB,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAA;yBAChH;oBACL,CAAC,CAAC,CAAA;;;gBATN,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;4BAAjB,CAAC;iBAUT;wCACQ,CAAC;oBACN,IAAM,IAAI,GAAG,EAAE,CAAC,WAAW,CAAC,OAAK,WAAW,CAAC,CAAA;oBAC7C,IAAI,uBAAuB,GAAG,IAAI,CAAC,YAAY,CAAC,4BAAkB,CAAC,CAAA;oBACnE,OAAK,YAAY,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAA;oBAC/C,OAAK,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;oBAClC,aAAK,CAAC,aAAa,CAAC,IAAI,EAAE;wBACtB,IAAI,uBAAuB,CAAC,QAAQ,EAAE,IAAI,IAAI,EAAE;4BAC5C,KAAI,CAAC,gBAAgB,CAAC,uBAAuB,CAAC,QAAQ,EAAE,CAAC,MAAM,EAAE,uBAAuB,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAA;yBAChH;oBACL,CAAC,CAAC,CAAA;;;gBATN,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,GAAG,CAAC,EAAE,CAAC,EAAE;4BAAhC,CAAC;iBAUT;aACJ;SACJ;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC/C,IAAI,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,MAAM,EAAE;gBAC7B,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;aACpD;iBAAM;gBACH,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;aACrC;SACJ;QAED,IAAI,uBAAU,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC,SAAS,KAAK,uBAAU,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAC,WAAW;YAClH,IAAM,UAAU,GAAG,uBAAU,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,KAAK,EAAV,CAAU,CAAC,CAAC,MAAM,CAAC,CAAA,YAAY;YAC3G,IAAI,uBAAU,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC,SAAS,KAAK,UAAU,EAAE,EAAC,4BAA4B;gBAC3F,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,UAAU,CAAC,CAAA;aAC7C;iBAAM;gBACH,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,SAAS,CAAC,CAAA;aAC5C;SACJ;aAAM;YACH,IAAM,KAAK,GAAG,uBAAU,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC,UAAC,IAAI,IAAK,OAAA,IAAI,CAAC,MAAM,KAAK,uBAAU,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,EAAlE,CAAkE,CAAC,CAAC,CAAA,IAAI;YACpJ,IAAI,uBAAU,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE;gBACxD,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,MAAM,CAAC,CAAA;aACzC;iBAAM;gBACH,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,KAAK,CAAC,CAAA;gBACrC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;aACpC;SACJ;IAEL,CAAC;IAES,4CAAS,GAAnB;QAAA,iBAYC;QAXG,UAAU,CAAC;YACP,gCAAgC;YAChC,KAAI,CAAC,UAAU,CAAC,MAAM,GAAG,EAAE,CAAA;YAC3B,KAAI,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAA;YACnC,KAAI,CAAC,aAAa,CAAC,iBAAiB,EAAE,CAAA;QAC1C,CAAC,EAAE,GAAG,CAAC,CAAC;QAER,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,aAAa,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YACtC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAA;SAChC;IACL,CAAC;IAED,UAAU;IACV,gDAAa,GAAb,UAAc,WAAwB;QAClC,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,KAAK,CAAC;QAClC,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,KAAK,CAAC;QAChC,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,KAAK,CAAC;QAChC,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,KAAK,CAAC;QAEjC,QAAQ,WAAW,EAAE;YACjB,KAAK,WAAW,CAAC,SAAS;gBACtB,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,IAAI,CAAC;gBACjC,MAAM;YACV,KAAK,WAAW,CAAC,UAAU;gBACvB,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,IAAI,CAAC;gBAC/B,MAAM;YACV,KAAK,WAAW,CAAC,KAAK;gBAClB,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,IAAI,CAAC;gBAC/B,MAAM;YACV,KAAK,WAAW,CAAC,MAAM;gBACnB,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC;gBAChC,MAAM;SACb;IACL,CAAC;IAED,OAAO;IACP,iDAAc,GAAd,UAAe,OAAe;QAA9B,iBA4BC;QA1BG,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,kEAAkE;YAClE,aAAa,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YACtC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAA;SAChC;QAED,IAAI,gBAAgB,GAAG,OAAO,CAAC;QAC/B,IAAI,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,CAAC;QAE5C,IAAI,CAAC,iBAAiB,GAAG,WAAW,CAAC;YACjC,gBAAgB,EAAE,CAAC;YAEnB,IAAI,gBAAgB,IAAI,CAAC,EAAE;gBACvB,+DAA+D;gBAC/D,aAAa,CAAC,KAAI,CAAC,iBAAiB,CAAC,CAAC;gBACtC,KAAI,CAAC,iBAAiB,GAAG,IAAI,CAAA;gBAC7B,cAAc;gBACd,IAAI,KAAI,CAAC,aAAa,EAAE;oBACpB,KAAI,CAAC,aAAa,EAAE,CAAA;iBACvB;gBACD,OAAM;aACT;YACD,6DAA6D;YAC7D,KAAI,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,CAAC;QAChD,CAAC,EAAE,IAAI,CAAC,CAAC;QACT,6DAA6D;IACjE,CAAC;IAED,uDAAoB,GAApB,UAAqB,OAAe;QAChC,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAG,WAAI,OAAO,YAAI,CAAC;SACpD;IACL,CAAC;IAED,OAAO;IACP,8CAAW,GAAX,UAAY,iBAAoC;QAC5C,uBAAU,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC,KAAK,GAAG,uBAAU,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,MAAM,KAAK,iBAAiB,CAAC,MAAM,EAAxC,CAAwC,CAAC,CAAC;QAC/I,IAAI,CAAC,aAAa,CAAC,uBAAU,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC,CAAC;IAC5D,CAAC;IAED,SAAS;IACT,gDAAa,GAAb,UAAc,sBAA8C;QACxD,IAAI,uBAAU,CAAC,WAAW,EAAE,CAAC,UAAU,IAAI,IAAI,IAAI,uBAAU,CAAC,WAAW,EAAE,CAAC,SAAS,IAAI,IAAI,EAAE;YAC3F,OAAM;SACT;QAED,uBAAU,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,IAAI;YAClD,IAAI,IAAI,CAAC,MAAM,KAAK,sBAAsB,CAAC,MAAM,EAAE;gBAC/C,IAAI,CAAC,KAAK,GAAG,sBAAsB,CAAC,KAAK,CAAC;aAC7C;QACL,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,aAAa,CAAC,uBAAU,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC,CAAA;IAC3D,CAAC;IAGD,eAAe;IACf,mDAAgB,GAAhB,UAAiB,MAAc,EAAE,QAAgB;QAE7C,8BAA8B;QAC9B,IAAI,uBAAU,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC,SAAS,KAAK,uBAAU,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM;eACjG,MAAM,IAAI,uBAAU,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,EACjE;YACE,IAAI,IAAI,CAAC,YAAY,EAAE;gBACnB,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAA;aACtC;SACJ;IAEL,CAAC;IAED,SAAS;IACT,2CAAQ,GAAR,UAAS,YAAsB,EAAE,aAAuB,EAAE,aAAuB,EAAE,cAAwB;QACvG,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;IAEzC,CAAC;IA5TD;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;8DACO;IAEzB;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;kEACW;IAE7B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;gEACS;IAE3B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;mEACY;IAE9B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;mEACY;IAE9B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;iEACU;IAE5B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;iEACU;IAE5B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;kEACW;IAE7B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;iEACU;IAnBb,wBAAwB;QAD5C,OAAO;OACa,wBAAwB,CAkU5C;IAAD,+BAAC;CAlUD,AAkUC,CAlUqD,EAAE,CAAC,SAAS,GAkUjE;kBAlUoB,wBAAwB", "file": "", "sourceRoot": "/", "sourcesContent": ["// Learn TypeScript:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html\n// Learn Attribute:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html\n// Learn life-cycle callbacks:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html\n\nimport { InviteInfo, NoticeLeaveInvite, NoticeUserInviteStatus } from \"../bean/GameBean\";\nimport { GlobalBean } from \"../bean/GlobalBean\";\nimport { GameMgr } from \"../common/GameMgr\";\nimport SeatItemController from \"../pfb/SeatItemController\";\nimport { Config } from \"../util/Config\";\nimport { Tools } from \"../util/Tools\";\n\nconst { ccclass, property } = cc._decorator;\n\n\nenum ButtonStyle {\n    StartGray = 1,\n    StartGreen = 2,\n    Ready = 3,\n    Cancel = 4,\n}\n\n@ccclass\nexport default class HallCreateRoomController extends cc.Component {\n\n    @property(cc.Node)\n    boardTab: cc.Node = null;\n    @property(cc.Node)\n    boardBgInner: cc.Node = null;\n    @property(cc.Node)\n    seatLayout: cc.Node = null;\n    @property(cc.Node)\n    seatTwoLayout: cc.Node = null;  //第二排的座位\n    @property(cc.Node)\n    buttonNoClick: cc.Node = null;   //灰色的 start 按钮\n    @property(cc.Node)\n    buttonStart: cc.Node = null;  //可点击的 start 按钮\n    @property(cc.Node)\n    buttonReady: cc.Node = null; //准备按钮\n    @property(cc.Node)\n    buttonCancel: cc.Node = null;  // 取消按钮\n    @property(cc.Prefab)\n    setaItemPfb: cc.Prefab = null;\n\n\n    btnCopyNormal: cc.Node = null  //copy按钮  \n    btnCopyLabel: cc.Node = null  //copy按钮文案   \n    roomNumber: cc.Label = null  //房间号   \n    ticketNumber: cc.Label = null  //门票价格 \n\n    private seatCallback: Function\n    private startCallBack: Function\n    private readyCallBack: Function\n    private cancelCallBack: Function\n\n    countdownTimeLabel: cc.Label = null\n    countdownInterval: number = null;//倒计时的 id\n    seconds: number = 10;//倒计时 10 秒\n\n    private _seatListCol: SeatItemController[] = []; //显示用户布局controller\n\n    onLoad() {\n        this.btnCopyNormal = this.boardTab.getChildByName('btn_copy_normal')\n        this.btnCopyLabel = this.btnCopyNormal.getChildByName('label')\n        this.roomNumber = this.boardTab.getChildByName('label').getComponent(cc.Label)\n        this.ticketNumber = this.boardBgInner.getChildByName('ticket_number').getComponent(cc.Label)\n        this.countdownTimeLabel = this.buttonReady.getChildByName('buttonLabel_time').getComponent(cc.Label)\n\n    }\n    protected onEnable(): void {\n        this.scheduleOnce(() => {\n            this.updateCountdownLabel(this.seconds);\n            Tools.setCountDownTimeLabel(this.buttonReady)\n            this.refreshData(GlobalBean.GetInstance().inviteInfo)\n        })\n    }\n\n    start() {\n\n        Tools.grayButton(this.buttonNoClick, () => { })\n\n        Tools.greenButton(this.buttonStart, () => {\n            if (this.startCallBack) {\n                this.startCallBack()\n            }\n        })\n        Tools.greenButton(this.buttonReady, () => {\n            if (this.readyCallBack) {\n                this.readyCallBack()\n            }\n        })\n\n        Tools.redButton(this.buttonCancel, () => {\n            if (this.cancelCallBack) {\n                this.cancelCallBack()\n            }\n        })\n\n        //copy 按钮的点击事件\n        Tools.setTouchEvent(this.btnCopyNormal, (node: cc.Node) => {\n            Tools.setNodeSpriteFrame(node, Config.hallRes + 'btn_copy_pressed');\n            let color = cc.Color.fromHEX(new cc.Color(), '#925333');\n            this.btnCopyLabel.color = color;\n            this.btnCopyLabel.getComponent(cc.Label).fontSize = 26;\n            Tools.copyToClipboard(this.roomNumber.string);\n        }, (node: cc.Node) => {\n            Tools.setNodeSpriteFrame(node, Config.hallRes + 'btn_copy_normal');\n            let color = cc.Color.fromHEX(new cc.Color(), '#D07649');\n            this.btnCopyLabel.color = color;\n            this.btnCopyLabel.getComponent(cc.Label).fontSize = 28;\n        }, (node: cc.Node) => {\n            Tools.setNodeSpriteFrame(node, Config.hallRes + 'btn_copy_normal');\n            let color = cc.Color.fromHEX(new cc.Color(), '#D07649');\n            this.btnCopyLabel.color = color;\n            this.btnCopyLabel.getComponent(cc.Label).fontSize = 28;\n        });\n\n    }\n\n    //刷新数据\n    refreshData(inviteInfo: InviteInfo) {\n\n        if (inviteInfo == null) {\n            return;\n        }\n\n        //这里是门票价格的赋值\n        let fees = inviteInfo.fee;\n        this.ticketNumber.string = fees === 0 ? window.getLocalizedStr('free') : fees + '';\n\n\n        this._seatListCol = []\n        this.refreshPlayer(inviteInfo)\n        this.roomNumber.string = inviteInfo.inviteCode + '' //邀请码\n\n\n        //初始进来就两种状态 房主是准备开始按钮  玩家是 ready 按钮\n        if (inviteInfo.creatorId === GlobalBean.GetInstance().loginData.userInfo.userId) {//判断自己是不是房主\n            this.btnCopyNormal.active = true;\n        } else {\n            this.btnCopyNormal.active = false;\n        }\n\n    }\n\n\n    //刷新座位玩家\n    refreshPlayer(inviteInfo: InviteInfo) {\n        let peopleNumber: number = inviteInfo.playerNum;\n        if (this._seatListCol.length < 1) {\n            this.seatLayout.removeAllChildren();\n            this.seatTwoLayout.removeAllChildren();\n\n            if (peopleNumber <= 4) {\n                this.schedule(() => {\n                    this.seatLayout.setPosition(this.seatLayout.x, -90);\n                }, 0)\n                this.seatTwoLayout.active = false\n                for (let i = 0; i < peopleNumber; i++) {\n                    const item = cc.instantiate(this.setaItemPfb)\n                    let seatEmptyItemController = item.getComponent(SeatItemController)\n                    this._seatListCol.push(seatEmptyItemController)\n                    this.seatLayout.addChild(item);\n                    Tools.setTouchEvent(item, () => {\n                        if (seatEmptyItemController.getUsers() != null) {\n                            this.setSetaItemClick(seatEmptyItemController.getUsers().userId, seatEmptyItemController.getUsers().nickname)\n                        }\n                    })\n                }\n            } else {\n                this.schedule(() => {\n                    this.seatLayout.setPosition(this.seatLayout.x, -10);\n                }, 0)\n                this.seatTwoLayout.active = true\n                for (let i = 0; i < 3; i++) {\n                    const item = cc.instantiate(this.setaItemPfb)\n                    let seatEmptyItemController = item.getComponent(SeatItemController)\n                    this._seatListCol.push(seatEmptyItemController)\n                    this.seatLayout.addChild(item);\n                    Tools.setTouchEvent(item, () => {\n                        if (seatEmptyItemController.getUsers() != null) {\n                            this.setSetaItemClick(seatEmptyItemController.getUsers().userId, seatEmptyItemController.getUsers().nickname)\n                        }\n                    })\n                }\n                for (let i = 0; i < peopleNumber - 3; i++) {\n                    const item = cc.instantiate(this.setaItemPfb)\n                    let seatEmptyItemController = item.getComponent(SeatItemController)\n                    this._seatListCol.push(seatEmptyItemController)\n                    this.seatTwoLayout.addChild(item);\n                    Tools.setTouchEvent(item, () => {\n                        if (seatEmptyItemController.getUsers() != null) {\n                            this.setSetaItemClick(seatEmptyItemController.getUsers().userId, seatEmptyItemController.getUsers().nickname)\n                        }\n                    })\n                }\n            }\n        }\n\n        for (let i = 0; i < this._seatListCol.length; i++) {\n            if (i < inviteInfo.users.length) {\n                this._seatListCol[i].setData(inviteInfo.users[i])\n            } else {\n                this._seatListCol[i].setData(null)\n            }\n        }\n\n        if (GlobalBean.GetInstance().inviteInfo.creatorId === GlobalBean.GetInstance().loginData.userInfo.userId) {//判断自己是不是房主\n            const readyCount = GlobalBean.GetInstance().inviteInfo.users.filter(user => user.ready).length;//查找准备好的玩家数量\n            if (GlobalBean.GetInstance().inviteInfo.playerNum === readyCount) {//准备好的玩家数量和总玩家数量一致的话就可以开始游戏了\n                this.setButtonType(ButtonStyle.StartGreen)\n            } else {\n                this.setButtonType(ButtonStyle.StartGray)\n            }\n        } else {\n            const index = GlobalBean.GetInstance().inviteInfo.users.findIndex((item) => item.userId === GlobalBean.GetInstance().loginData.userInfo.userId);//搜索\n            if (GlobalBean.GetInstance().inviteInfo.users[index].ready) {\n                this.setButtonType(ButtonStyle.Cancel)\n            } else {\n                this.setButtonType(ButtonStyle.Ready)\n                this.startCountdown(this.seconds)\n            }\n        }\n\n    }\n\n    protected onDisable(): void {\n        setTimeout(() => {\n            // this.ticketNumber.string = ''\n            this.roomNumber.string = ''\n            this.seatLayout.removeAllChildren()\n            this.seatTwoLayout.removeAllChildren()\n        }, 100);\n\n        if (this.countdownInterval) {\n            clearInterval(this.countdownInterval);\n            this.countdownInterval = null\n        }\n    }\n\n    //设置按钮显示状态\n    setButtonType(buttonStyle: ButtonStyle) {\n        this.buttonNoClick.active = false;\n        this.buttonStart.active = false;\n        this.buttonReady.active = false;\n        this.buttonCancel.active = false;\n\n        switch (buttonStyle) {\n            case ButtonStyle.StartGray:\n                this.buttonNoClick.active = true;\n                break;\n            case ButtonStyle.StartGreen:\n                this.buttonStart.active = true;\n                break;\n            case ButtonStyle.Ready:\n                this.buttonReady.active = true;\n                break;\n            case ButtonStyle.Cancel:\n                this.buttonCancel.active = true;\n                break;\n        }\n    }\n\n    //启动倒计时\n    startCountdown(seconds: number) {\n\n        if (this.countdownInterval) {\n            // GameMgr.Console.Error('当前存在的定时器 先销毁id：'+this.countdownInterval)\n            clearInterval(this.countdownInterval);\n            this.countdownInterval = null\n        }\n\n        let remainingSeconds = seconds;\n        this.updateCountdownLabel(remainingSeconds);\n\n        this.countdownInterval = setInterval(() => {\n            remainingSeconds--;\n\n            if (remainingSeconds <= 0) {\n                // GameMgr.Console.Error('自动销毁的定时器 id：'+this.countdownInterval)\n                clearInterval(this.countdownInterval);\n                this.countdownInterval = null\n                // 倒计时结束时的处理逻辑\n                if (this.readyCallBack) {\n                    this.readyCallBack()\n                }\n                return\n            }\n            // GameMgr.Console.Error('执行的定时器 id：'+this.countdownInterval)\n            this.updateCountdownLabel(remainingSeconds);\n        }, 1000);\n        // GameMgr.Console.Error('创建的定时器 id：'+this.countdownInterval)\n    }\n\n    updateCountdownLabel(seconds: number) {\n        if (this.countdownTimeLabel) {\n            this.countdownTimeLabel.string = `（${seconds}s）`;\n        }\n    }\n\n    //有玩家离开\n    leavePlayer(noticeLeaveInvite: NoticeLeaveInvite) {\n        GlobalBean.GetInstance().inviteInfo.users = GlobalBean.GetInstance().inviteInfo.users.filter(user => user.userId !== noticeLeaveInvite.userId);\n        this.refreshPlayer(GlobalBean.GetInstance().inviteInfo);\n    }\n\n    //准备 取消准备\n    setReadyState(noticeUserInviteStatus: NoticeUserInviteStatus) {\n        if (GlobalBean.GetInstance().inviteInfo == null || GlobalBean.GetInstance().loginData == null) {\n            return\n        }\n\n        GlobalBean.GetInstance().inviteInfo.users.forEach(user => {\n            if (user.userId === noticeUserInviteStatus.userId) {\n                user.ready = noticeUserInviteStatus.ready;\n            }\n        });\n        this.refreshPlayer(GlobalBean.GetInstance().inviteInfo)\n    }\n\n\n    //设置 item 的点击事件\n    setSetaItemClick(userId: string, nickname: string) {\n\n        //得先判断当前的房间我是不是房主  并且要提出的不是我自己\n        if (GlobalBean.GetInstance().inviteInfo.creatorId === GlobalBean.GetInstance().loginData.userInfo.userId\n            && userId != GlobalBean.GetInstance().loginData.userInfo.userId\n        ) {\n            if (this.seatCallback) {\n                this.seatCallback(userId, nickname)\n            }\n        }\n\n    }\n\n    //设置点击的回调\n    setClick(seatCallback: Function, startCallBack: Function, readyCallBack: Function, cancelCallBack: Function) {\n        this.seatCallback = seatCallback;\n        this.startCallBack = startCallBack;\n        this.readyCallBack = readyCallBack;\n        this.cancelCallBack = cancelCallBack;\n\n    }\n\n    // update (dt) {}\n}\n"]}