"use strict";
cc._RF.push(module, 'e30e9U3AbZJ1Yq4QqLLdUm7', 'HallCenterLayController');
// scripts/hall/HallCenterLayController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CenterLaytouType = void 0;
var GlobalBean_1 = require("../bean/GlobalBean");
var MessageId_1 = require("../net/MessageId");
var WebSocketManager_1 = require("../net/WebSocketManager");
var ToastController_1 = require("../ToastController");
var HallAutoController_1 = require("./HallAutoController");
var HallCreateRoomController_1 = require("./HallCreateRoomController");
var HallJoinRoomController_1 = require("./HallJoinRoomController");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var CenterLaytouType;
(function (CenterLaytouType) {
    CenterLaytouType[CenterLaytouType["HALL_AUTO_VIEW"] = 0] = "HALL_AUTO_VIEW";
    CenterLaytouType[CenterLaytouType["HALL_CREAT_ROOM_VIEW"] = 1] = "HALL_CREAT_ROOM_VIEW";
    CenterLaytouType[CenterLaytouType["HALL_JOIN_ROOM_VIEW"] = 2] = "HALL_JOIN_ROOM_VIEW";
})(CenterLaytouType = exports.CenterLaytouType || (exports.CenterLaytouType = {}));
var HallCenterLayController = /** @class */ (function (_super) {
    __extends(HallCenterLayController, _super);
    function HallCenterLayController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.hallAutoView = null; //快速开始view
        _this.hallCreateRoomView = null; //创建房间 view
        _this.hallJoinRoomView = null; //加入房间的 view
        _this.toastController = null; //toast 的布局
        _this.hallAutoController = null;
        _this.hallCreateRoomController = null;
        _this.hallJoinRoomController = null;
        _this.centerLaytouType = null; //当前展示的哪一个 view
        return _this;
        // update (dt) {}
    }
    HallCenterLayController.prototype.onLoad = function () {
        this.hallAutoController = this.hallAutoView.getComponent(HallAutoController_1.default);
        this.hallCreateRoomController = this.hallCreateRoomView.getComponent(HallCreateRoomController_1.default);
        this.hallJoinRoomController = this.hallJoinRoomView.getComponent(HallJoinRoomController_1.default);
    };
    HallCenterLayController.prototype.onEnable = function () {
        this.setCenterLaytouType(CenterLaytouType.HALL_AUTO_VIEW);
    };
    HallCenterLayController.prototype.start = function () {
        var _this = this;
        this.hallAutoController.setButtonClick(function () {
            //start 按钮的点击回调
            if (_this.startClick) {
                _this.startClick();
            }
        }, function () {
            //create 按钮的点击回调
            if (_this.createClick) {
                _this.createClick();
            }
        }, function () {
            //join 按钮的点击回调
            _this.setCenterLaytouType(CenterLaytouType.HALL_JOIN_ROOM_VIEW);
        });
        this.hallCreateRoomController.setClick(function (userId, nickname) {
            //房间内点击玩家头像
            if (_this.seatCallback) {
                _this.seatCallback(userId, nickname);
            }
        }, function () {
            //点击 start 的回调
            //发送开始游戏的消息
            WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeInviteStart, {});
        }, function () {
            //ready的回调
            //发送游戏准备的消息
            WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeInviteReady, { 'ready': true });
        }, function () {
            //cancel 的回调
            //发送取消游戏准备的消息
            WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeInviteReady, { 'ready': false });
        });
        this.hallJoinRoomController.setButtonClick(function (inviteCode) {
            //加入房间的按钮
            WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeAcceptInvite, { 'inviteCode': Number(inviteCode) });
        });
    };
    //设置显示哪一个 view
    HallCenterLayController.prototype.setCenterLaytouType = function (centerLaytouType) {
        if (this.centerLaytouType === centerLaytouType) {
            return;
        }
        this.centerLaytouType = centerLaytouType;
        this.hallAutoView.active = false;
        this.hallCreateRoomView.active = false;
        this.hallJoinRoomView.active = false;
        switch (centerLaytouType) {
            case CenterLaytouType.HALL_AUTO_VIEW:
                this.hallAutoView.active = true;
                break;
            case CenterLaytouType.HALL_CREAT_ROOM_VIEW:
                this.hallCreateRoomView.active = true;
                break;
            case CenterLaytouType.HALL_JOIN_ROOM_VIEW:
                this.hallJoinRoomView.active = true;
                break;
        }
    };
    //获取当前正在显示的那个页面
    HallCenterLayController.prototype.getCenterLaytouType = function () {
        return this.centerLaytouType;
    };
    //设置按钮的点击回调
    HallCenterLayController.prototype.setClick = function (startClick, createClick, seatCallback) {
        this.startClick = startClick;
        this.createClick = createClick;
        this.seatCallback = seatCallback;
    };
    //设置接受邀请成功
    HallCenterLayController.prototype.setAcceptInvite = function (acceptInvite) {
        //如果正在，接受邀请页面的话
        if (this.centerLaytouType == CenterLaytouType.HALL_JOIN_ROOM_VIEW) {
            //进入创建房间页面
            this.setCenterLaytouType(CenterLaytouType.HALL_CREAT_ROOM_VIEW);
        }
        else if (this.centerLaytouType == CenterLaytouType.HALL_CREAT_ROOM_VIEW) {
            //如果已经在房间页面，就刷新数据
            this.hallCreateRoomController.refreshPlayer(acceptInvite.inviteInfo);
        }
        else {
            //还在大厅收到重链接的消息
            //进入创建房间页面
            this.setCenterLaytouType(CenterLaytouType.HALL_CREAT_ROOM_VIEW);
        }
    };
    //离开房间
    HallCenterLayController.prototype.leaveRoom = function (noticeLeaveInvite) {
        //判断是不是自己离开了房间，或者是房主离开（房主离开就是解散房间了）
        if (noticeLeaveInvite.userId === GlobalBean_1.GlobalBean.GetInstance().loginData.userInfo.userId || noticeLeaveInvite.isCreator) {
            if (noticeLeaveInvite.userId != GlobalBean_1.GlobalBean.GetInstance().loginData.userInfo.userId) {
                this.toastController.showContent(window.getLocalizedStr('LeaveRoom'));
            }
            //当前展示的是加入房间 或者创建房间的话  返回键返回大厅
            this.setCenterLaytouType(CenterLaytouType.HALL_AUTO_VIEW);
        }
        else {
            //刷新数据
            this.hallCreateRoomController.leavePlayer(noticeLeaveInvite);
        }
    };
    //设置门票
    HallCenterLayController.prototype.setFees = function () {
        this.hallAutoController.setFees();
    };
    //进入私人房间
    HallCenterLayController.prototype.joinCreateRoom = function () {
        this.setCenterLaytouType(CenterLaytouType.HALL_CREAT_ROOM_VIEW);
    };
    HallCenterLayController.prototype.joinError = function () {
        this.hallJoinRoomController.joinError();
    };
    //准备 取消准备
    HallCenterLayController.prototype.setReadyState = function (noticeUserInviteStatus) {
        this.hallCreateRoomController.setReadyState(noticeUserInviteStatus);
    };
    __decorate([
        property(cc.Node)
    ], HallCenterLayController.prototype, "hallAutoView", void 0);
    __decorate([
        property(cc.Node)
    ], HallCenterLayController.prototype, "hallCreateRoomView", void 0);
    __decorate([
        property(cc.Node)
    ], HallCenterLayController.prototype, "hallJoinRoomView", void 0);
    __decorate([
        property(ToastController_1.default)
    ], HallCenterLayController.prototype, "toastController", void 0);
    HallCenterLayController = __decorate([
        ccclass
    ], HallCenterLayController);
    return HallCenterLayController;
}(cc.Component));
exports.default = HallCenterLayController;

cc._RF.pop();