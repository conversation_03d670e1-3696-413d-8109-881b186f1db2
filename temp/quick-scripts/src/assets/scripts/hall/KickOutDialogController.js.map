{"version": 3, "sources": ["assets/scripts/hall/KickOutDialogController.ts"], "names": [], "mappings": ";;;;;AAAA,oBAAoB;AACpB,4EAA4E;AAC5E,mBAAmB;AACnB,sFAAsF;AACtF,8BAA8B;AAC9B,sFAAsF;;;;;;;;;;;;;;;;;;;;;AAGtF,8CAA6C;AAC7C,4DAA2D;AAC3D,yCAAwC;AACxC,uCAAsC;AAEhC,IAAA,KAAwB,EAAE,CAAC,UAAU,EAAnC,OAAO,aAAA,EAAE,QAAQ,cAAkB,CAAC;AAG5C;IAAqD,2CAAY;IAAjE;QAAA,qEA0EC;QAvEG,aAAO,GAAY,IAAI,CAAA;QAEvB,mBAAa,GAAY,IAAI,CAAA;QAE7B,aAAO,GAAY,IAAI,CAAA;QAEvB,eAAS,GAAY,IAAI,CAAA;QAEzB,gBAAU,GAAY,IAAI,CAAA;QAE1B,eAAS,GAAa,IAAI,CAAA;QAC1B,YAAM,GAAW,EAAE,CAAA;;QA2DnB,iBAAiB;IACrB,CAAC;IAzDG,wCAAM,GAAN;QACI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC,KAAK,CAAC,CAAA;QACpD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAA;IACrE,CAAC;IAED,uCAAK,GAAL;QAAA,iBAeC;QAdG,aAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,aAAa,EAAE,eAAM,CAAC,SAAS,GAAG,wBAAwB,EAAE,eAAM,CAAC,SAAS,GAAG,yBAAyB,EAAE;YAClI,KAAI,CAAC,IAAI,EAAE,CAAA;QACf,CAAC,CAAC,CAAC;QACH,eAAe;QACf,aAAK,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,EAAE;YAC/B,KAAI,CAAC,IAAI,EAAE,CAAA;QACf,CAAC,CAAC,CAAA;QACF,gBAAgB;QAChB,aAAK,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,EAAE;YAC7B,KAAI,CAAC,IAAI,EAAE,CAAA;YACX,YAAY;YACZ,mCAAgB,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,qBAAS,CAAC,oBAAoB,EAAE,EAAE,QAAQ,EAAE,KAAI,CAAC,MAAM,EAAE,CAAC,CAAC;QACtG,CAAC,CAAC,CAAA;IAEN,CAAC;IAED,sCAAI,GAAJ,UAAK,MAAc,EAAE,QAAgB;QACjC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAA;QACvB,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,CAAC,CAAA;QACtB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;QAEpB,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,MAAM,CAAC,eAAe,CAAC,UAAU,CAAC,CAAA;QAC1D,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,CAAA;QAE5D,SAAS;QACT,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC;aACjB,EAAE,CAAC,eAAM,CAAC,eAAe,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;aACxC,KAAK,EAAE,CAAC;IAGjB,CAAC;IAED,sCAAI,GAAJ;QAAA,iBAQC;QAPG,SAAS;QACT,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC;aACjB,EAAE,CAAC,eAAM,CAAC,eAAe,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;aACxC,IAAI,CAAC;YACF,KAAI,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAA;QAC5B,CAAC,CAAC;aACD,KAAK,EAAE,CAAC;IACjB,CAAC;IAED,gDAAc,GAAd,UAAe,GAAW;QACtB,IAAI,GAAG,CAAC,MAAM,GAAG,EAAE,EAAE;YACjB,OAAO,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC;SACnC;aAAM;YACH,OAAO,GAAG,CAAC;SACd;IACL,CAAC;IApED;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;4DACK;IAEvB;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;kEACW;IAE7B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;4DACK;IAEvB;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;8DACO;IAEzB;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;+DACQ;IAXT,uBAAuB;QAD3C,OAAO;OACa,uBAAuB,CA0E3C;IAAD,8BAAC;CA1ED,AA0EC,CA1EoD,EAAE,CAAC,SAAS,GA0EhE;kBA1EoB,uBAAuB", "file": "", "sourceRoot": "/", "sourcesContent": ["// Learn TypeScript:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html\n// Learn Attribute:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html\n// Learn life-cycle callbacks:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html\n\nimport { LocalizedLabel } from \"../../../packages/2.4.9+版本多语言插件i18n/script/LocalizedLabel\";\nimport { MessageId } from \"../net/MessageId\";\nimport { WebSocketManager } from \"../net/WebSocketManager\";\nimport { Config } from \"../util/Config\";\nimport { Tools } from \"../util/Tools\";\n\nconst { ccclass, property } = cc._decorator;\n\n@ccclass\nexport default class KickOutDialogController extends cc.Component {\n\n    @property(cc.Node)\n    boardBg: cc.Node = null\n    @property(cc.Node)\n    boardBtnClose: cc.Node = null\n    @property(cc.Node)\n    content: cc.Node = null\n    @property(cc.Node)\n    cancelBtn: cc.Node = null\n    @property(cc.Node)\n    kickOutBtn: cc.Node = null\n\n    brownText: cc.Label = null\n    userId: string = ''\n    localizedLabel: LocalizedLabel\n\n    onLoad() {\n        this.brownText = this.content.getComponent(cc.Label)\n        this.localizedLabel = this.content.getComponent('LocalizedLabel')\n    }\n\n    start() {\n        Tools.imageButtonClick(this.boardBtnClose, Config.buttonRes + 'board_btn_close_normal', Config.buttonRes + 'board_btn_close_pressed', () => {\n            this.hide()\n        });\n        //cancel 按钮点击事件\n        Tools.yellowButton(this.cancelBtn, () => {\n            this.hide()\n        })\n        //kickOut 按钮点击事件\n        Tools.redButton(this.kickOutBtn, () => {\n            this.hide()\n            //发送玩家被踢出的消息\n            WebSocketManager.GetInstance().sendMsg(MessageId.MsgTypeInviteKickOut, { 'userId': this.userId });\n        })\n\n    }\n\n    show(userId: string, nickname: string) {\n        this.node.active = true\n        this.boardBg.scale = 0\n        this.userId = userId\n\n        this.brownText.string = window.getLocalizedStr('kickout2')\n        this.localizedLabel.bindParam(this.truncateString(nickname))\n\n        // 执行缩放动画\n        cc.tween(this.boardBg)\n            .to(Config.dialogScaleTime, { scale: 1 })\n            .start();\n\n\n    }\n\n    hide() {\n        // 执行缩放动画\n        cc.tween(this.boardBg)\n            .to(Config.dialogScaleTime, { scale: 0 })\n            .call(() => {\n                this.node.active = false\n            })\n            .start();\n    }\n\n    truncateString(str: string): string {\n        if (str.length > 10) {\n            return str.slice(0, 10) + '...';\n        } else {\n            return str;\n        }\n    }\n\n    // update (dt) {}\n}\n"]}