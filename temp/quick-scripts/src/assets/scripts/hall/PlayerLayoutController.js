"use strict";
cc._RF.push(module, '22e43laKY5NvZOL2nMmyHfN', 'PlayerLayoutController');
// scripts/hall/PlayerLayoutController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var GlobalBean_1 = require("../bean/GlobalBean");
var Tools_1 = require("../util/Tools");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var PlayerLayoutController = /** @class */ (function (_super) {
    __extends(PlayerLayoutController, _super);
    function PlayerLayoutController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.player2 = null; //玩家人数是 2 的节点
        _this.player3 = null; //玩家人数是 3 的节点
        _this.player4 = null; //玩家人数是 4 的节点
        _this.player5 = null; //玩家人数是 5 的节点
        _this.sliding = null; //选中玩家人数的滑动条
        //移动时间
        _this.moveTime = 0.1;
        return _this;
        // update (dt) {}
    }
    // onLoad () {}
    PlayerLayoutController.prototype.onEnable = function () {
        var players = GlobalBean_1.GlobalBean.GetInstance().players;
        switch (players) {
            case 2:
                this.movePlayersliding(this.player2);
                break;
            case 3:
                this.movePlayersliding(this.player3);
                break;
            case 4:
                this.movePlayersliding(this.player4);
                break;
            case 5:
                this.movePlayersliding(this.player5);
                break;
        }
    };
    PlayerLayoutController.prototype.start = function () {
        this.setListener(this.player2, 2);
        this.setListener(this.player3, 3);
        this.setListener(this.player4, 4);
        this.setListener(this.player5, 5);
    };
    //设置监听
    PlayerLayoutController.prototype.setListener = function (playerNode, players) {
        var _this = this;
        Tools_1.Tools.setTouchEvent(playerNode, function () {
            GlobalBean_1.GlobalBean.GetInstance().players = players;
            _this.movePlayersliding(playerNode);
        });
    };
    //移动滑动条
    PlayerLayoutController.prototype.movePlayersliding = function (playerNode) {
        cc.tween(this.sliding)
            .to(this.moveTime, { position: playerNode.position })
            .start(); // 开始动画
    };
    __decorate([
        property(cc.Node)
    ], PlayerLayoutController.prototype, "player2", void 0);
    __decorate([
        property(cc.Node)
    ], PlayerLayoutController.prototype, "player3", void 0);
    __decorate([
        property(cc.Node)
    ], PlayerLayoutController.prototype, "player4", void 0);
    __decorate([
        property(cc.Node)
    ], PlayerLayoutController.prototype, "player5", void 0);
    __decorate([
        property(cc.Node)
    ], PlayerLayoutController.prototype, "sliding", void 0);
    PlayerLayoutController = __decorate([
        ccclass
    ], PlayerLayoutController);
    return PlayerLayoutController;
}(cc.Component));
exports.default = PlayerLayoutController;

cc._RF.pop();