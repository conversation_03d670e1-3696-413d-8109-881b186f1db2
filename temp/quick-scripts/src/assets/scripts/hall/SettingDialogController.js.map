{"version": 3, "sources": ["assets/scripts/hall/SettingDialogController.ts"], "names": [], "mappings": ";;;;;AAAA,oBAAoB;AACpB,4EAA4E;AAC5E,mBAAmB;AACnB,sFAAsF;AACtF,8BAA8B;AAC9B,sFAAsF;;;;;;;;;;;;;;;;;;;;;AAEtF,yDAAwD;AACxD,qDAAoD;AACpD,yCAAwC;AACxC,mEAAkE;AAClE,uCAAsC;AAEhC,IAAA,KAAsB,EAAE,CAAC,UAAU,EAAlC,OAAO,aAAA,EAAE,QAAQ,cAAiB,CAAC;AAG1C;IAAqD,2CAAY;IAAjE;QAAA,qEAqFC;QAlFG,aAAO,GAAY,IAAI,CAAA;QAEvB,mBAAa,GAAY,IAAI,CAAA;QAE7B,gBAAU,GAAY,IAAI,CAAA;QAE1B,cAAQ,GAAY,IAAI,CAAA;QAExB,cAAQ,GAAY,IAAI,CAAA;QAExB,cAAQ,GAAa,IAAI,CAAA,CAAC,KAAK;QAE/B,WAAK,GAAY,IAAI,CAAC;QACtB,WAAK,GAAY,IAAI,CAAC;QACtB,kBAAY,GAAa,IAAI,CAAA,CAAC,SAAS;;QAmEvC,iBAAiB;IACrB,CAAC;IAlEG,wCAAM,GAAN;QACI,IAAI,CAAC,KAAK,GAAG,yCAAmB,CAAC,WAAW,EAAE,CAAC,cAAc,EAAE,CAAC;QAChE,IAAI,CAAC,KAAK,GAAG,yCAAmB,CAAC,WAAW,EAAE,CAAC,cAAc,EAAE,CAAC;IACpE,CAAC;IAED,uCAAK,GAAL;QAAA,iBA0BC;QAxBG,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,OAAK,iBAAO,CAAC,WAAW,EAAE,CAAC,UAAU,EAAI,CAAC;QAEjE,aAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,aAAa,EAAE,eAAM,CAAC,SAAS,GAAG,wBAAwB,EAAE,eAAM,CAAC,SAAS,GAAG,yBAAyB,EAAE;YAClI,KAAI,CAAC,IAAI,EAAE,CAAA;QACf,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1C,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QAE1C,aAAK,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,UAAC,IAAa;YAC7C,KAAI,CAAC,KAAK,GAAG,CAAC,KAAI,CAAC,KAAK,CAAC;YACzB,yCAAmB,CAAC,WAAW,EAAE,CAAC,cAAc,CAAC,KAAI,CAAC,KAAK,CAAC,CAAC;YAC7D,KAAI,CAAC,SAAS,CAAC,IAAI,EAAE,KAAI,CAAC,KAAK,CAAC,CAAC;YACjC,IAAI,KAAI,CAAC,KAAK,EAAE;gBACZ,2BAAY,CAAC,OAAO,EAAE,CAAC;aAC1B;iBAAM;gBACH,2BAAY,CAAC,OAAO,EAAE,CAAC;aAC1B;QACL,CAAC,CAAC,CAAC;QAEH,aAAK,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,UAAC,IAAa;YAC7C,KAAI,CAAC,KAAK,GAAG,CAAC,KAAI,CAAC,KAAK,CAAC;YACzB,yCAAmB,CAAC,WAAW,EAAE,CAAC,cAAc,CAAC,KAAI,CAAC,KAAK,CAAC,CAAC;YAC7D,KAAI,CAAC,SAAS,CAAC,IAAI,EAAE,KAAI,CAAC,KAAK,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;IACP,CAAC;IAED,2CAAS,GAAT,UAAU,IAAa,EAAE,UAAmB;QACxC,IAAI,UAAU,EAAE;YACZ,aAAK,CAAC,kBAAkB,CAAC,IAAI,EAAE,eAAM,CAAC,SAAS,GAAG,eAAe,CAAC,CAAC;SACtE;aAAM;YACH,aAAK,CAAC,kBAAkB,CAAC,IAAI,EAAE,eAAM,CAAC,SAAS,GAAG,eAAe,CAAC,CAAC;SACtE;IAEL,CAAC;IAED,sCAAI,GAAJ,UAAK,YAAqB;QACtB,IAAI,CAAC,YAAY,GAAG,YAAY,CAAA;QAChC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAA;QACvB,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,CAAC,CAAA;QACtB,SAAS;QACT,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC;aACjB,EAAE,CAAC,eAAM,CAAC,eAAe,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;aACxC,KAAK,EAAE,CAAC;IACjB,CAAC;IACD,sCAAI,GAAJ;QAAA,iBAWC;QAVG,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,YAAY,EAAE,CAAA;SACtB;QACD,SAAS;QACT,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC;aACjB,EAAE,CAAC,eAAM,CAAC,eAAe,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;aACxC,IAAI,CAAC;YACF,KAAI,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAA;QAC5B,CAAC,CAAC;aACD,KAAK,EAAE,CAAC;IACjB,CAAC;IA9ED;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;4DACK;IAEvB;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;kEACW;IAE7B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;+DACQ;IAE1B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;6DACM;IAExB;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;6DACM;IAExB;QADC,QAAQ,CAAC,EAAE,CAAC,KAAK,CAAC;6DACM;IAbR,uBAAuB;QAD3C,OAAO;OACa,uBAAuB,CAqF3C;IAAD,8BAAC;CArFD,AAqFC,CArFoD,EAAE,CAAC,SAAS,GAqFhE;kBArFoB,uBAAuB", "file": "", "sourceRoot": "/", "sourcesContent": ["// Learn TypeScript:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html\n// Learn Attribute:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html\n// Learn life-cycle callbacks:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html\n\nimport { Publish } from \"../../meshTools/tools/Publish\";\nimport { AudioManager } from \"../util/AudioManager\";\nimport { Config } from \"../util/Config\";\nimport { LocalStorageManager } from \"../util/LocalStorageManager\";\nimport { Tools } from \"../util/Tools\";\n\nconst {ccclass, property} = cc._decorator;\n\n@ccclass\nexport default class SettingDialogController extends cc.Component {\n\n    @property(cc.Node)\n    boardBg: cc.Node = null\n    @property(cc.Node)\n    boardBtnClose: cc.Node = null\n    @property(cc.Node)\n    contentLay: cc.Node = null\n    @property(cc.Node)\n    musicBtn: cc.Node = null\n    @property(cc.Node)\n    soundBtn: cc.Node = null\n    @property(cc.Label)\n    versoion: cc.Label = null //版本号\n\n    music: boolean = true;\n    sound: boolean = true;\n    backCallback: Function = null //隐藏弹窗的回调\n\n    onLoad () {\n        this.music = LocalStorageManager.GetInstance().getMusicSwitch();\n        this.sound = LocalStorageManager.GetInstance().getSoundSwitch();\n    }\n\n    start () {\n\n        this.versoion.string = `V ${Publish.GetInstance().getVersion()}`;\n\n        Tools.imageButtonClick(this.boardBtnClose, Config.buttonRes + 'board_btn_close_normal', Config.buttonRes + 'board_btn_close_pressed', () => {\n            this.hide()\n        });\n        this.setSwitch(this.musicBtn, this.music);\n        this.setSwitch(this.soundBtn, this.sound);\n\n        Tools.setTouchEvent(this.musicBtn, (node: cc.Node) => {\n            this.music = !this.music;\n            LocalStorageManager.GetInstance().setMusicSwitch(this.music);\n            this.setSwitch(node, this.music);\n            if (this.music) {\n                AudioManager.playBgm();\n            } else {\n                AudioManager.stopBgm();\n            }\n        });\n\n        Tools.setTouchEvent(this.soundBtn, (node: cc.Node) => {\n            this.sound = !this.sound;\n            LocalStorageManager.GetInstance().setSoundSwitch(this.sound);\n            this.setSwitch(node, this.sound);\n        });\n    }\n\n    setSwitch(node: cc.Node, switchType: boolean) {\n        if (switchType) {\n            Tools.setNodeSpriteFrame(node, Config.buttonRes + 'btn_switch_02');\n        } else {\n            Tools.setNodeSpriteFrame(node, Config.buttonRes + 'btn_switch_01');\n        }\n\n    }\n\n    show(backCallback:Function ) {\n        this.backCallback = backCallback\n        this.node.active = true\n        this.boardBg.scale = 0\n        // 执行缩放动画\n        cc.tween(this.boardBg)\n            .to(Config.dialogScaleTime, { scale: 1 })\n            .start();\n    }\n    hide() {\n        if (this.backCallback) {\n            this.backCallback()\n        }\n        // 执行缩放动画\n        cc.tween(this.boardBg)\n            .to(Config.dialogScaleTime, { scale: 0 })\n            .call(() => {\n                this.node.active = false\n            })\n            .start();\n    }\n\n\n    // update (dt) {}\n}\n"]}