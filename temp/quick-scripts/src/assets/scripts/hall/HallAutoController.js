"use strict";
cc._RF.push(module, 'fc7bflryAdCHaxASp4dUNIP', 'HallAutoController');
// scripts/hall/HallAutoController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AutoOrRoom = void 0;
var GlobalBean_1 = require("../bean/GlobalBean");
var Config_1 = require("../util/Config");
var Tools_1 = require("../util/Tools");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var AutoOrRoom;
(function (AutoOrRoom) {
    AutoOrRoom[AutoOrRoom["AUTO"] = 0] = "AUTO";
    AutoOrRoom[AutoOrRoom["ROOM"] = 1] = "ROOM";
})(AutoOrRoom = exports.AutoOrRoom || (exports.AutoOrRoom = {}));
var HallAutoController = /** @class */ (function (_super) {
    __extends(HallAutoController, _super);
    function HallAutoController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.autoLay = null;
        _this.roomLay = null;
        _this.boardTicketLay = null;
        _this.startButton = null;
        _this.createButton = null;
        _this.joinButton = null;
        _this.boardTabAuto = null;
        _this.boardTabRoom = null;
        _this.boardTicketBtnMinus = null; //门票➖
        _this.boardTicketBtnPlus = null; //门票➕
        _this.ticketNumber = null; //门票价格
        _this.autoOrRoom = null;
        _this.startClick = null;
        _this.createClick = null;
        _this.joinClick = null;
        _this.autoFeesPosition = 0;
        _this.createPosition = 0;
        return _this;
        // update (dt) {}
    }
    HallAutoController.prototype.onLoad = function () {
        this.boardTabAuto = this.autoLay.getChildByName('board_tab_02');
        this.boardTabRoom = this.roomLay.getChildByName('board_tab_02');
        this.boardTicketBtnMinus = this.boardTicketLay.getChildByName('board_ticket_btn_minus_normal');
        this.boardTicketBtnPlus = this.boardTicketLay.getChildByName('board_ticket_btn_plus_normal');
        this.ticketNumber = this.boardTicketLay.getChildByName('ticket_number').getComponent(cc.Label);
    };
    //设置游戏开始数据
    HallAutoController.prototype.setFees = function () {
        var roomConfig = GlobalBean_1.GlobalBean.GetInstance().loginData.roomConfigs;
        //房间类型 1-普通场 2-私人场
        for (var i = 0; i < roomConfig.length; i++) {
            if (roomConfig[i].id == 1) {
                this.autoRoomConfig = roomConfig[i];
            }
            if (roomConfig[i].id == 2) {
                this.createRoomConfig = roomConfig[i];
            }
        }
        this.setAutoOrRoom(AutoOrRoom.AUTO); //设置初始值
    };
    HallAutoController.prototype.start = function () {
        var _this = this;
        Tools_1.Tools.setTouchEvent(this.autoLay, function () {
            _this.setAutoOrRoom(AutoOrRoom.AUTO);
        });
        Tools_1.Tools.setTouchEvent(this.roomLay, function () {
            _this.setAutoOrRoom(AutoOrRoom.ROOM);
        });
        //start 按钮点击事件
        Tools_1.Tools.greenButton(this.startButton, function () {
            if (_this.startClick) {
                _this.startClick();
            }
        });
        //create 按钮点击事件
        Tools_1.Tools.greenButton(this.createButton, function () {
            if (_this.createClick) {
                _this.createClick();
            }
        });
        //join 按钮点击事件
        Tools_1.Tools.yellowButton(this.joinButton, function () {
            if (_this.joinClick) {
                _this.joinClick();
            }
        });
        //点击按钮减
        Tools_1.Tools.imageButtonClick(this.boardTicketBtnMinus, Config_1.Config.buttonRes + 'board_ticket_btn_minus_normal', Config_1.Config.buttonRes + 'board_ticket_btn_minus_pressed', function () {
            if (GlobalBean_1.GlobalBean.GetInstance().autoAndRoom == AutoOrRoom.AUTO) {
                if (_this.autoFeesPosition === 0) {
                    return;
                }
                _this.autoFeesPosition--;
                _this.setTicketsNum(_this.autoRoomConfig, _this.autoFeesPosition);
            }
            else {
                if (_this.createPosition === 0) {
                    return;
                }
                _this.createPosition--;
                _this.setTicketsNum(_this.createRoomConfig, _this.createPosition);
            }
        });
        //点击按钮加
        Tools_1.Tools.imageButtonClick(this.boardTicketBtnPlus, Config_1.Config.buttonRes + 'board_ticket_btn_plus_normal', Config_1.Config.buttonRes + 'board_ticket_btn_plus_pressed', function () {
            if (GlobalBean_1.GlobalBean.GetInstance().autoAndRoom == AutoOrRoom.AUTO) {
                if (_this.autoFeesPosition === _this.autoRoomConfig.fees.length - 1) {
                    return;
                }
                _this.autoFeesPosition++;
                _this.setTicketsNum(_this.autoRoomConfig, _this.autoFeesPosition);
            }
            else {
                if (_this.createPosition === _this.createRoomConfig.fees.length - 1) {
                    return;
                }
                _this.createPosition++;
                _this.setTicketsNum(_this.createRoomConfig, _this.createPosition);
            }
        });
    };
    //赋值门票价格
    HallAutoController.prototype.setTicketsNum = function (roomConfig, position) {
        var fees = roomConfig.fees[position];
        this.ticketNumber.string = fees === 0 ? window.getLocalizedStr('free') : fees + '';
        GlobalBean_1.GlobalBean.GetInstance().ticketsNum = fees;
    };
    //设置展示 auto 还是 room 的view
    HallAutoController.prototype.setAutoOrRoom = function (autoOrRoom) {
        if (this.autoOrRoom === autoOrRoom) {
            return;
        }
        this.autoOrRoom = autoOrRoom;
        GlobalBean_1.GlobalBean.GetInstance().autoAndRoom = autoOrRoom;
        this.boardTabAuto.active = false;
        this.boardTabRoom.active = false;
        this.startButton.active = false;
        this.createButton.active = false;
        this.joinButton.active = false;
        switch (autoOrRoom) {
            case AutoOrRoom.AUTO:
                this.boardTabAuto.active = true;
                this.startButton.active = true;
                this.createButton.active = false;
                this.joinButton.active = false;
                break;
            case AutoOrRoom.ROOM:
                this.boardTabRoom.active = true;
                this.startButton.active = false;
                this.createButton.active = true;
                this.joinButton.active = true;
                break;
        }
        if (GlobalBean_1.GlobalBean.GetInstance().autoAndRoom == AutoOrRoom.AUTO) {
            if (this.autoRoomConfig) {
                this.setTicketsNum(this.autoRoomConfig, this.autoFeesPosition);
            }
            else {
                this.ticketNumber.string = window.getLocalizedStr('free');
            }
        }
        else {
            if (this.createRoomConfig) {
                this.setTicketsNum(this.createRoomConfig, this.createPosition);
            }
            else {
                this.ticketNumber.string = window.getLocalizedStr('free');
            }
        }
    };
    //设置点击按钮的回调
    HallAutoController.prototype.setButtonClick = function (startClick, createClick, joinClick) {
        this.startClick = startClick;
        this.createClick = createClick;
        this.joinClick = joinClick;
    };
    __decorate([
        property(cc.Node)
    ], HallAutoController.prototype, "autoLay", void 0);
    __decorate([
        property(cc.Node)
    ], HallAutoController.prototype, "roomLay", void 0);
    __decorate([
        property(cc.Node)
    ], HallAutoController.prototype, "boardTicketLay", void 0);
    __decorate([
        property(cc.Node)
    ], HallAutoController.prototype, "startButton", void 0);
    __decorate([
        property(cc.Node)
    ], HallAutoController.prototype, "createButton", void 0);
    __decorate([
        property(cc.Node)
    ], HallAutoController.prototype, "joinButton", void 0);
    HallAutoController = __decorate([
        ccclass
    ], HallAutoController);
    return HallAutoController;
}(cc.Component));
exports.default = HallAutoController;

cc._RF.pop();