"use strict";
cc._RF.push(module, '63546mbf2xAv6Hj/p5rHgOb', 'HallPageController');
// scripts/hall/HallPageController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HallOrMatch = void 0;
var GlobalBean_1 = require("../bean/GlobalBean");
var GameMgr_1 = require("../common/GameMgr");
var MessageId_1 = require("../net/MessageId");
var WebSocketManager_1 = require("../net/WebSocketManager");
var WebSocketTool_1 = require("../net/WebSocketTool");
var ToastController_1 = require("../ToastController");
var AudioManager_1 = require("../util/AudioManager");
var HallParentController_1 = require("./HallParentController");
var InfoDialogController_1 = require("./InfoDialogController");
var KickOutDialogController_1 = require("./KickOutDialogController");
var LeaveDialogController_1 = require("./LeaveDialogController");
var LevelSelectPageController_1 = require("./Level/LevelSelectPageController");
var MatchParentController_1 = require("./MatchParentController");
var SettingDialogController_1 = require("./SettingDialogController");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var HallOrMatch;
(function (HallOrMatch) {
    HallOrMatch[HallOrMatch["HALL_PARENT"] = 0] = "HALL_PARENT";
    HallOrMatch[HallOrMatch["MATCH_PARENT"] = 1] = "MATCH_PARENT";
})(HallOrMatch = exports.HallOrMatch || (exports.HallOrMatch = {}));
var HallPageController = /** @class */ (function (_super) {
    __extends(HallPageController, _super);
    function HallPageController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.hallParentNode = null;
        _this.matchParentNode = null;
        _this.infoDialogController = null; //道具简介弹窗
        _this.leaveDialogController = null; // 退出游戏弹窗
        _this.settingDialogController = null; //设置弹窗
        _this.kickOutDialogController = null; //踢出用户的 dialog
        _this.toastController = null; //toast 的布局
        _this.levelSelectPageController = null; //关卡选择页面控制器
        _this.hallOrMatch = null;
        return _this;
        // update (dt) {}
    }
    HallPageController.prototype.onLoad = function () {
        this.hallParentController = this.hallParentNode.getComponent(HallParentController_1.default);
        this.matchParentController = this.matchParentNode.getComponent(MatchParentController_1.default);
    };
    HallPageController.prototype.onEnable = function () {
        AudioManager_1.AudioManager.playBgm();
        this.setHallOrMatch(HallOrMatch.HALL_PARENT);
    };
    HallPageController.prototype.start = function () {
        var _this = this;
        this.hallParentController.setClick(function () {
            //返回键的回调
            _this.leaveDialogController.show(0, function () { });
        }, function () {
            //info 的回调
            _this.infoDialogController.show(function () { });
        }, function () {
            //设置键的回调
            _this.settingDialogController.show(function () { });
        }, function () {
            //start 按钮点击
            _this.startOrCreate(MessageId_1.MessageId.MsgTypePairRequest);
        }, function () {
            //create 点击创建房间
            _this.startOrCreate(MessageId_1.MessageId.MsgTypeCreateInvite);
        }, function (userId, nickname) {
            //点击创建房间内的 点击玩家头像 弹出的踢出房间弹窗
            _this.kickOutDialogController.show(userId, nickname);
        });
        this.matchParentController.setClick(function () {
            //匹配页面的返回键的回调
            WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeCancelPair, {});
        });
    };
    HallPageController.prototype.updateGold = function () {
        this.hallParentController.updateGold();
    };
    //开始匹配 或者创建房间
    HallPageController.prototype.startOrCreate = function (msgId) {
        //判断是否链接成功，并且还得有登录成功的数据返回 ，不成功的话就不允许执行下面的操作
        if (WebSocketManager_1.WebSocketManager.GetInstance().webState != WebSocketTool_1.WebSocketToolState.Connected || GlobalBean_1.GlobalBean.GetInstance().loginData == null) {
            return;
        }
        //点击 快速开始游戏 start 的回调
        var pairRequest = {
            playerNum: GlobalBean_1.GlobalBean.GetInstance().players,
            fee: GlobalBean_1.GlobalBean.GetInstance().ticketsNum,
        };
        //发送请求开始游戏的消息
        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(msgId, pairRequest);
    };
    //设置是大厅 还是匹配页面
    HallPageController.prototype.setHallOrMatch = function (hallOrMatch) {
        if (this.hallOrMatch === hallOrMatch) {
            return;
        }
        this.hallOrMatch = hallOrMatch;
        this.hallParentNode.active = false;
        this.matchParentNode.active = false;
        switch (hallOrMatch) {
            case HallOrMatch.HALL_PARENT:
                this.hallParentNode.active = true;
                break;
            case HallOrMatch.MATCH_PARENT:
                this.matchParentNode.active = true;
                break;
        }
    };
    HallPageController.prototype.LoginSuccess = function () {
        //登录成功后 执行的操作
        GameMgr_1.GameMgr.Console.Log("登录成功");
        var loginData = GlobalBean_1.GlobalBean.GetInstance().loginData;
        if (loginData) {
            if (loginData.roomId > 0) { //正在游戏中
                WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeEnterRoom, {}); //重连进来的 玩家请求进入房间
            }
            if (loginData.inviteCode > 0) { //正在私人房间
                WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeAcceptInvite, { 'inviteCode': Number(loginData.inviteCode) });
            }
            else {
                //房间已经解散了  但是我还留在私人房间
                this.hallParentController.exitTheRoom();
            }
            //重连的时候 被遗留在匹配页面的话 就回到大厅
            if (loginData.roomId == 0 && loginData.inviteCode == 0 && this.hallOrMatch == HallOrMatch.MATCH_PARENT) {
                this.setHallOrMatch(HallOrMatch.HALL_PARENT);
            }
        }
        this.setFees();
        // 登录成功后请求关卡进度
        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeExtendLevelProgress, {});
    };
    //设置接受邀请成功
    HallPageController.prototype.setAcceptInvite = function (acceptInvite) {
        this.hallParentController.setAcceptInvite(acceptInvite);
    };
    //离开房间
    HallPageController.prototype.leaveRoom = function (noticeLeaveInvite) {
        this.hallParentController.leaveRoom(noticeLeaveInvite);
    };
    //设置门票
    HallPageController.prototype.setFees = function () {
        this.hallParentController.setFees();
    };
    //初始化 match 页面
    HallPageController.prototype.createMatchView = function () {
        this.matchParentController.createMatchView();
    };
    //设置匹配数据
    HallPageController.prototype.setGameData = function () {
        this.matchParentController.setGameData();
    };
    //进入私人房间
    HallPageController.prototype.joinCreateRoom = function () {
        this.hallParentController.joinCreateRoom();
    };
    //房间号无效
    HallPageController.prototype.joinError = function () {
        this.hallParentController.joinError();
    };
    //准备 取消准备
    HallPageController.prototype.setReadyState = function (noticeUserInviteStatus) {
        if (this.hallParentController) {
            this.hallParentController.setReadyState(noticeUserInviteStatus);
        }
    };
    /**
     * 设置关卡进度（从后端获取数据后调用）
     * @param levelProgressData 关卡进度数据，包含clearedLevels, currentLevel, totalLevels
     */
    HallPageController.prototype.setLevelProgress = function (levelProgressData) {
        if (this.levelSelectPageController) {
            this.levelSelectPageController.setLevelProgress(levelProgressData);
        }
    };
    __decorate([
        property(cc.Node)
    ], HallPageController.prototype, "hallParentNode", void 0);
    __decorate([
        property(cc.Node)
    ], HallPageController.prototype, "matchParentNode", void 0);
    __decorate([
        property(InfoDialogController_1.default)
    ], HallPageController.prototype, "infoDialogController", void 0);
    __decorate([
        property(LeaveDialogController_1.default)
    ], HallPageController.prototype, "leaveDialogController", void 0);
    __decorate([
        property(SettingDialogController_1.default)
    ], HallPageController.prototype, "settingDialogController", void 0);
    __decorate([
        property(KickOutDialogController_1.default)
    ], HallPageController.prototype, "kickOutDialogController", void 0);
    __decorate([
        property(ToastController_1.default)
    ], HallPageController.prototype, "toastController", void 0);
    __decorate([
        property(LevelSelectPageController_1.default)
    ], HallPageController.prototype, "levelSelectPageController", void 0);
    HallPageController = __decorate([
        ccclass
    ], HallPageController);
    return HallPageController;
}(cc.Component));
exports.default = HallPageController;

cc._RF.pop();