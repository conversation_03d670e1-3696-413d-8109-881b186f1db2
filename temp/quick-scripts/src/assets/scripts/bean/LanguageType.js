"use strict";
cc._RF.push(module, '83c31BPQVZMSJTrcsJTL4Yt', 'LanguageType');
// scripts/bean/LanguageType.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var LanguageType;
(function (LanguageType) {
    /** 简体中文 */
    LanguageType["SimplifiedChinese"] = "0";
    LanguageType["SimplifiedChinese_type"] = "zh_CN";
    /** 繁体中文 */
    LanguageType["TraditionalChinese"] = "1";
    LanguageType["TraditionalChinese_type"] = "zh_HK";
    /** 英文 */
    LanguageType["English"] = "2";
    LanguageType["English_type"] = "en";
    /** 印尼语 */
    LanguageType["Indonesian"] = "3";
    LanguageType["Indonesian_type"] = "id";
    /** 马来语 */
    LanguageType["Malay"] = "4";
    LanguageType["Malay_type"] = "ms_MY";
    /** 泰语 */
    LanguageType["Thai"] = "5";
    LanguageType["Thai_type"] = "th";
    /** 越南语 */
    LanguageType["Vietnamese"] = "6";
    LanguageType["Vietnamese_type"] = "vi_VN";
    /** 阿拉伯语 */
    LanguageType["Arabic"] = "7";
    LanguageType["Arabic_type"] = "ar";
    /** 菲律宾语 */
    LanguageType["Filipino"] = "8";
    LanguageType["Filipino_type"] = "fil";
    /** 葡萄牙语 */
    LanguageType["Portuguese"] = "9";
    LanguageType["Portuguese_type"] = "pt_br";
    /** 土耳其语 */
    LanguageType["Turkish"] = "10";
    LanguageType["Turkish_type"] = "tur";
    /** 乌尔都语 */
    LanguageType["Urdu"] = "11";
    LanguageType["Urdu_type"] = "ur";
    /** 日语 */
    LanguageType["Japanese"] = "12";
    LanguageType["Japanese_type"] = "ja";
    /** 俄语 */
    LanguageType["Russian"] = "13";
    LanguageType["Russian_type"] = "ru";
    /** 西班牙语 */
    LanguageType["Spanish"] = "14";
    LanguageType["Spanish_type"] = "es";
})(LanguageType || (LanguageType = {}));
exports.default = LanguageType;

cc._RF.pop();