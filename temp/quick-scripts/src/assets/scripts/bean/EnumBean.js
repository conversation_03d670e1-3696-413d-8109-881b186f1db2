"use strict";
cc._RF.push(module, 'a3026CGwJlCfJrx0ZY7AvdX', 'EnumBean');
// scripts/bean/EnumBean.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MoveType = exports.RewardType = exports.Obstacle = exports.BlockType = exports.BlockColor = exports.GameStatus = exports.UserStatus = exports.RoomType = void 0;
// RoomType 房间类型
var RoomType;
(function (RoomType) {
    RoomType[RoomType["RoomTypeCommon"] = 1] = "RoomTypeCommon";
    RoomType[RoomType["RoomTypePrivate"] = 2] = "RoomTypePrivate";
    RoomType[RoomType["RoomTypeVoice"] = 3] = "RoomTypeVoice";
})(RoomType = exports.RoomType || (exports.RoomType = {}));
// UserStatus 用户状态
var UserStatus;
(function (UserStatus) {
    UserStatus[UserStatus["UserStatusStand"] = 1] = "UserStatusStand";
    UserStatus[UserStatus["UserStatusSit"] = 2] = "UserStatusSit";
})(UserStatus = exports.UserStatus || (exports.UserStatus = {}));
// GameStatus 游戏状态
var GameStatus;
(function (GameStatus) {
    GameStatus[GameStatus["GameStatusMove"] = 1] = "GameStatusMove";
})(GameStatus = exports.GameStatus || (exports.GameStatus = {}));
// BlockColor 块颜色
var BlockColor;
(function (BlockColor) {
    BlockColor[BlockColor["BlockColorRed"] = 1] = "BlockColorRed";
    BlockColor[BlockColor["BlockColorBlue"] = 2] = "BlockColorBlue";
    BlockColor[BlockColor["BlockColorGreen"] = 3] = "BlockColorGreen";
    BlockColor[BlockColor["BlockColorYellow"] = 4] = "BlockColorYellow";
    BlockColor[BlockColor["BlockColorBrown"] = 5] = "BlockColorBrown";
})(BlockColor = exports.BlockColor || (exports.BlockColor = {}));
// BlockType 块类型(普通块、被动技能块)
var BlockType;
(function (BlockType) {
    BlockType[BlockType["BlockTypeCommon"] = 1] = "BlockTypeCommon";
    BlockType[BlockType["BlockTypeArrowX"] = 2] = "BlockTypeArrowX";
    BlockType[BlockType["BlockTypeArrowY"] = 3] = "BlockTypeArrowY";
    BlockType[BlockType["BlockTypeBomb"] = 4] = "BlockTypeBomb";
    BlockType[BlockType["BlockTypeRainbow"] = 5] = "BlockTypeRainbow";
    BlockType[BlockType["BlockTypeSuperArrow"] = 6] = "BlockTypeSuperArrow";
    BlockType[BlockType["BlockTypeBombArrow"] = 7] = "BlockTypeBombArrow";
    BlockType[BlockType["BlockTypeRainbowArrow"] = 8] = "BlockTypeRainbowArrow";
    BlockType[BlockType["BlockTypeSuperBomb"] = 9] = "BlockTypeSuperBomb";
    BlockType[BlockType["BlockTypeRainbowBomb"] = 10] = "BlockTypeRainbowBomb";
    BlockType[BlockType["BlockTypeSuperRain"] = 11] = "BlockTypeSuperRain";
})(BlockType = exports.BlockType || (exports.BlockType = {}));
// Obstacle 障碍物类型
var Obstacle;
(function (Obstacle) {
    Obstacle[Obstacle["ObstacleDefault"] = 0] = "ObstacleDefault";
    Obstacle[Obstacle["ObstacleChain"] = 1000] = "ObstacleChain";
    Obstacle[Obstacle["ObstacleIce"] = 1001] = "ObstacleIce";
    Obstacle[Obstacle["ObstacleChainIce"] = 1002] = "ObstacleChainIce";
})(Obstacle = exports.Obstacle || (exports.Obstacle = {}));
//实时任务的类型
var RewardType;
(function (RewardType) {
    // (1-彩虹、2-冰块、3-锁链、4-箭头X、5-箭头Y)
    RewardType[RewardType["RAINBOW"] = 1] = "RAINBOW";
    RewardType[RewardType["ICE"] = 2] = "ICE";
    RewardType[RewardType["LOCK"] = 3] = "LOCK";
    RewardType[RewardType["ARROW_X"] = 4] = "ARROW_X";
    RewardType[RewardType["ARROW_Y"] = 5] = "ARROW_Y";
})(RewardType = exports.RewardType || (exports.RewardType = {}));
var MoveType;
(function (MoveType) {
    MoveType[MoveType["MoveDefault"] = 0] = "MoveDefault";
    MoveType[MoveType["MoveStart"] = 1] = "MoveStart";
    MoveType[MoveType["MoveEnd"] = 2] = "MoveEnd";
})(MoveType = exports.MoveType || (exports.MoveType = {}));

cc._RF.pop();