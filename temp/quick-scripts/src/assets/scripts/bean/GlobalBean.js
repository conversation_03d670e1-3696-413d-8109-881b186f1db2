"use strict";
cc._RF.push(module, 'adb11KURoVCvZ/ECr2VcXvT', 'GlobalBean');
// scripts/bean/GlobalBean.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.GlobalBean = void 0;
var Singleton_1 = require("../../meshTools/Singleton");
var HallAutoController_1 = require("../hall/HallAutoController");
var GlobalBean = /** @class */ (function (_super) {
    __extends(GlobalBean, _super);
    function GlobalBean() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.loginData = null; //用户数据
        _this.autoAndRoom = HallAutoController_1.AutoOrRoom.AUTO; //当前选中的是哪种游戏模式
        _this.players = 2; //玩家人数 默认两个人
        _this.ticketsNum = 0; //门票价格
        return _this;
    }
    GlobalBean.prototype.cleanData = function () {
        this.noticeStartGame = null;
        this.inviteInfo = null;
    };
    //调整 users 数据的展示顺序把自己的数据调整到第一位， 并不是调整座位数据
    GlobalBean.prototype.adjustUserData = function () {
        var user = this.noticeStartGame.users;
        var index = user.findIndex(function (item) { return item.userId === GlobalBean.GetInstance().loginData.userInfo.userId; });
        if (index !== -1) {
            var element = this.noticeStartGame.users.splice(index, 1)[0];
            user.unshift(element);
        }
        // 确保所有用户都有score字段
        user.forEach(function (u) {
            if (u.score === undefined || u.score === null) {
                u.score = 0;
            }
        });
        return user;
    };
    GlobalBean.prototype.getQueue = function () {
        return BlockingQueue.getInstance(1000);
    };
    return GlobalBean;
}(Singleton_1.Singleton));
exports.GlobalBean = GlobalBean;

cc._RF.pop();