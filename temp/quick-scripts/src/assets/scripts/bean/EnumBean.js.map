{"version": 3, "sources": ["assets/scripts/bean/EnumBean.ts"], "names": [], "mappings": ";;;;;;;AACA,gBAAgB;AAChB,IAAY,QAIX;AAJD,WAAY,QAAQ;IAChB,2DAAkB,CAAA;IAClB,6DAAmB,CAAA;IACnB,yDAAiB,CAAA;AACrB,CAAC,EAJW,QAAQ,GAAR,gBAAQ,KAAR,gBAAQ,QAInB;AAED,kBAAkB;AAClB,IAAY,UAGX;AAHD,WAAY,UAAU;IAClB,iEAAmB,CAAA;IACnB,6DAAiB,CAAA;AACrB,CAAC,EAHW,UAAU,GAAV,kBAAU,KAAV,kBAAU,QAGrB;AAGD,kBAAkB;AAClB,IAAY,UAGX;AAHD,WAAY,UAAU;IAClB,+DAAkB,CAAA;AAEtB,CAAC,EAHW,UAAU,GAAV,kBAAU,KAAV,kBAAU,QAGrB;AAED,iBAAiB;AACjB,IAAY,UAMX;AAND,WAAY,UAAU;IAClB,6DAAiB,CAAA;IACjB,+DAAkB,CAAA;IAClB,iEAAmB,CAAA;IACnB,mEAAoB,CAAA;IACpB,iEAAmB,CAAA;AACvB,CAAC,EANW,UAAU,GAAV,kBAAU,KAAV,kBAAU,QAMrB;AAGD,2BAA2B;AAC3B,IAAY,SAaX;AAbD,WAAY,SAAS;IACjB,+DAAmB,CAAA;IACnB,+DAAmB,CAAA;IACnB,+DAAmB,CAAA;IACnB,2DAAiB,CAAA;IACjB,iEAAoB,CAAA;IACpB,uEAAuB,CAAA;IACvB,qEAAsB,CAAA;IACtB,2EAAyB,CAAA;IACzB,qEAAsB,CAAA;IACtB,0EAAyB,CAAA;IACzB,sEAAuB,CAAA;AAE3B,CAAC,EAbW,SAAS,GAAT,iBAAS,KAAT,iBAAS,QAapB;AAED,iBAAiB;AACjB,IAAY,QAKX;AALD,WAAY,QAAQ;IAChB,6DAAmB,CAAA;IACnB,4DAAoB,CAAA;IACpB,wDAAkB,CAAA;IAClB,kEAAuB,CAAA;AAC3B,CAAC,EALW,QAAQ,GAAR,gBAAQ,KAAR,gBAAQ,QAKnB;AAED,SAAS;AACT,IAAY,UAOV;AAPF,WAAY,UAAU;IAClB,+BAA+B;IAC9B,iDAAW,CAAA;IACX,yCAAO,CAAA;IACP,2CAAQ,CAAA;IACR,iDAAW,CAAA;IACX,iDAAW,CAAA;AACf,CAAC,EAPU,UAAU,GAAV,kBAAU,KAAV,kBAAU,QAOpB;AAED,IAAY,QAIX;AAJD,WAAY,QAAQ;IACjB,qDAAe,CAAA;IACf,iDAAa,CAAA;IACb,6CAAW,CAAA;AACd,CAAC,EAJW,QAAQ,GAAR,gBAAQ,KAAR,gBAAQ,QAInB", "file": "", "sourceRoot": "/", "sourcesContent": ["\n// RoomType 房间类型\nexport enum RoomType{\n    RoomTypeCommon = 1, // 普通场\n    RoomTypePrivate = 2, // 私人场\n    RoomTypeVoice = 3, // 语聊房场\n}\n\n// UserStatus 用户状态\nexport enum UserStatus {\n    UserStatusStand = 1, // 站立(旁观者)\n    UserStatusSit = 2, // 已坐下(参与者，已分配座位号)\n}\n\n\n// GameStatus 游戏状态\nexport enum GameStatus{\n    GameStatusMove = 1, // 移动消除\n\n}\n\n// BlockColor 块颜色\nexport enum BlockColor{\n    BlockColorRed = 1, // 红色\n    BlockColorBlue = 2, // 蓝色\n    BlockColorGreen = 3, // 绿色\n    BlockColorYellow = 4, // 黄色\n    BlockColorBrown = 5, // 棕色\n}\n\n\n// BlockType 块类型(普通块、被动技能块)\nexport enum BlockType{\n    BlockTypeCommon = 1, // 普通块\n    BlockTypeArrowX = 2, // 箭头X\n    BlockTypeArrowY = 3, // 箭头Y\n    BlockTypeBomb = 4, // 炸弹\n    BlockTypeRainbow = 5, // 彩虹\n    BlockTypeSuperArrow = 6, // 超级箭头\n    BlockTypeBombArrow = 7, // 炸弹箭头\n    BlockTypeRainbowArrow = 8, // 彩虹箭头\n    BlockTypeSuperBomb = 9, // 超级炸弹\n    BlockTypeRainbowBomb = 10, // 彩虹炸弹\n    BlockTypeSuperRain = 11, // 超级彩虹\n\n}\n\n// Obstacle 障碍物类型\nexport enum Obstacle{\n    ObstacleDefault = 0, // 默认正常\n    ObstacleChain = 1000, // 锁链-不能移动 能参与消除 相邻块爆炸1次解除(箭头不算爆炸)\n    ObstacleIce = 1001, // 冰块-不能移动，不参与消除，相邻块爆炸1次解除(箭头不算爆炸)\n    ObstacleChainIce = 1002, // 锁链+冰块-不能移动，不参与消除，相邻块爆炸2次解除(箭头不算爆炸)\n}\n\n//实时任务的类型\nexport enum RewardType{\n    // (1-彩虹、2-冰块、3-锁链、4-箭头X、5-箭头Y)\n     RAINBOW = 1,\n     ICE = 2,\n     LOCK = 3,\n     ARROW_X = 4,\n     ARROW_Y = 5\n }\n\n export enum MoveType{\n    MoveDefault = 0, //默认没有任何操作 \n    MoveStart = 1, //发起移动操作\n    MoveEnd = 2,  //收到移动结果的回执\n }\n\n\n"]}