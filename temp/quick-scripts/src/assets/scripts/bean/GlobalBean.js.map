{"version": 3, "sources": ["assets/scripts/bean/GlobalBean.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA,uDAAsD;AACtD,iEAAwD;AAGxD;IAAgC,8BAAS;IAAzC;QAAA,qEA0CC;QAxCG,eAAS,GAAa,IAAI,CAAC,CAAE,MAAM;QAE5B,iBAAW,GAAG,+BAAU,CAAC,IAAI,CAAC,CAAC,cAAc;QAC7C,aAAO,GAAG,CAAC,CAAC,CAAE,YAAY;QAC1B,gBAAU,GAAG,CAAC,CAAC,CAAE,MAAM;;IAoClC,CAAC;IA5BG,8BAAS,GAAT;QACI,IAAI,CAAC,eAAe,GAAG,IAAI,CAAA;QAC3B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAA;IAC1B,CAAC;IAEA,yCAAyC;IACzC,mCAAc,GAAd;QACG,IAAI,IAAI,GAAe,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC;QAClD,IAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,UAAC,IAAI,IAAK,OAAA,IAAI,CAAC,MAAM,KAAK,UAAU,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,EAAlE,CAAkE,CAAC,CAAC;QAC3G,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;YACd,IAAM,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/D,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;SACzB;QAED,kBAAkB;QAClB,IAAI,CAAC,OAAO,CAAC,UAAA,CAAC;YACV,IAAI,CAAC,CAAC,KAAK,KAAK,SAAS,IAAI,CAAC,CAAC,KAAK,KAAK,IAAI,EAAE;gBAC3C,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC;aACf;QACL,CAAC,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,6BAAQ,GAAR;QACI,OAAO,aAAa,CAAC,WAAW,CAAS,IAAI,CAAC,CAAC;IACnD,CAAC;IAEL,iBAAC;AAAD,CA1CA,AA0CC,CA1C+B,qBAAS,GA0CxC;AA1CY,gCAAU", "file": "", "sourceRoot": "/", "sourcesContent": ["import { Singleton } from \"../../meshTools/Singleton\";\nimport { AutoOrRoom } from \"../hall/HallAutoController\";\nimport { CurTask, InviteInfo, LoginData, NoticeStartGame, ObstacleBlock, RoomUser } from \"./GameBean\";\n\nexport class GlobalBean extends Singleton{\n\n    loginData:LoginData = null;  //用户数据\n\n    public autoAndRoom = AutoOrRoom.AUTO; //当前选中的是哪种游戏模式\n    public players = 2;  //玩家人数 默认两个人\n    public ticketsNum = 0;  //门票价格\n\n\n     //这个数据是游戏开始之后 服务器返回的数据\n    public noticeStartGame: NoticeStartGame //开始游戏的数据\n    public inviteInfo: InviteInfo;//这个是创建完房间返回的数据\n    \n\n    cleanData(){\n        this.noticeStartGame = null\n        this.inviteInfo = null\n    }\n\n     //调整 users 数据的展示顺序把自己的数据调整到第一位， 并不是调整座位数据\n     adjustUserData(): RoomUser[] {\n        let user: RoomUser[] = this.noticeStartGame.users;\n        const index = user.findIndex((item) => item.userId === GlobalBean.GetInstance().loginData.userInfo.userId);\n        if (index !== -1) {\n            const element = this.noticeStartGame.users.splice(index, 1)[0];\n            user.unshift(element);\n        }\n\n        // 确保所有用户都有score字段\n        user.forEach(u => {\n            if (u.score === undefined || u.score === null) {\n                u.score = 0;\n            }\n        });\n\n        return user;\n    }\n\n    getQueue(){\n        return BlockingQueue.getInstance<number>(1000);\n    }\n\n}"]}