"use strict";
cc._RF.push(module, '18f0ckDFwBGKYEa+6+oQZ/M', 'ErrorCode');
// scripts/net/ErrorCode.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ErrorCode = void 0;
//错误码
var ErrorCode;
(function (ErrorCode) {
    ErrorCode[ErrorCode["OK"] = 0] = "OK";
    ErrorCode[ErrorCode["ErrGameId"] = 1] = "ErrGameId";
    ErrorCode[ErrorCode["ErrGameMode"] = 3] = "ErrGameMode";
    ErrorCode[ErrorCode["ErrRequestUser"] = 5] = "ErrRequestUser";
    ErrorCode[ErrorCode["ErrInPair"] = 6] = "ErrInPair";
    ErrorCode[ErrorCode["ErrNotEnoughCoin"] = 7] = "ErrNotEnoughCoin";
    ErrorCode[ErrorCode["ErrChangeBalance"] = 8] = "ErrChangeBalance";
    ErrorCode[ErrorCode["ErrNotFoundRoom"] = 9] = "ErrNotFoundRoom";
    ErrorCode[ErrorCode["ErrNotFoundUser"] = 10] = "ErrNotFoundUser";
    ErrorCode[ErrorCode["ErrRoomConfig"] = 12] = "ErrRoomConfig";
    ErrorCode[ErrorCode["ErrParams"] = 13] = "ErrParams";
    ErrorCode[ErrorCode["ErrDefend"] = 16] = "ErrDefend";
    ErrorCode[ErrorCode["ErrSitHaveUser"] = 18] = "ErrSitHaveUser";
    ErrorCode[ErrorCode["ErrHaveSit"] = 19] = "ErrHaveSit";
    ErrorCode[ErrorCode["ErrUserPlaying"] = 20] = "ErrUserPlaying";
    ErrorCode[ErrorCode["ErrInInvite"] = 31] = "ErrInInvite";
    ErrorCode[ErrorCode["ErrPlaying"] = 32] = "ErrPlaying";
    ErrorCode[ErrorCode["ErrInvalidInviteCode"] = 33] = "ErrInvalidInviteCode";
    ErrorCode[ErrorCode["ErrEnoughUser"] = 34] = "ErrEnoughUser";
    ErrorCode[ErrorCode["ErrNotInvite"] = 35] = "ErrNotInvite";
    ErrorCode[ErrorCode["ErrChgInvite"] = 36] = "ErrChgInvite";
    ErrorCode[ErrorCode["ErrNotInviteCreator"] = 37] = "ErrNotInviteCreator";
    ErrorCode[ErrorCode["ErrForbidKickSelf"] = 38] = "ErrForbidKickSelf";
    ErrorCode[ErrorCode["ErrInviteNotAllReady"] = 40] = "ErrInviteNotAllReady";
    ErrorCode[ErrorCode["ErrInviteStart"] = 41] = "ErrInviteStart";
    ErrorCode[ErrorCode["ErrNotInPair"] = 44] = "ErrNotInPair";
})(ErrorCode = exports.ErrorCode || (exports.ErrorCode = {}));

cc._RF.pop();