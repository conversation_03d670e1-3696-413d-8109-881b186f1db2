{"version": 3, "sources": ["assets/scripts/net/WebSocketManager.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAEA,uDAAsD;AACtD,qDAAkD;AAClD,6CAA4C;AAG5C,iDAAoE;AAEpE;IAAsC,oCAAS;IAA/C;QAAA,qEAwCC;QArCG,cAAQ,GAAuB,kCAAkB,CAAC,OAAO,CAAC;;IAqC9D,CAAC;IApCG,WAAW;IACX,kCAAO,GAAP;QACI,6BAAa,CAAC,WAAW,EAAE,CAAC,iBAAiB,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QAC9E,6BAAa,CAAC,WAAW,EAAE,CAAC,OAAO,EAAE,CAAC,CAAA,OAAO;IACjD,CAAC;IAED,mBAAmB;IACX,kCAAO,GAAf,UAAgB,GAAW;QACvB,IAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAwB,CAAC;QAC1D,IAAI,UAAU,CAAC,IAAI,IAAI,CAAC,EAAE,EAAC,MAAM;YAC7B,iBAAO,CAAC,KAAK,CAAC,IAAI,CAAC,uBAAS,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC,CAAA,YAAY;SACxE;aAAM;YACH,iBAAO,CAAC,KAAK,CAAC,IAAI,CAAC,uBAAS,CAAC,mBAAmB,EAAE,UAAU,CAAC,CAAC,CAAA,YAAY;SAC7E;IACL,CAAC;IAED,qBAAqB;IACb,sCAAW,GAAnB,UAAoB,QAA4B;QAC5C,iBAAO,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,GAAG,QAAQ,CAAC,CAAC;QAC9C,gBAAgB,CAAC,WAAW,EAAE,CAAC,QAAQ,GAAG,QAAQ,CAAC;IACvD,CAAC;IAGD,wBAAwB;IACxB,kCAAO,GAAP,UAAQ,KAAgB,EAAE,IAAS;QAE/B,IAAI,eAAe,GAAoB;YACnC,KAAK,EAAE,KAAK;YACZ,IAAI,EAAE,IAAI;SACb,CAAC;QACF,IAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;QAEnD,6BAAa,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IACjD,CAAC;IAGL,uBAAC;AAAD,CAxCA,AAwCC,CAxCqC,qBAAS,GAwC9C;AAxCY,4CAAgB", "file": "", "sourceRoot": "/", "sourcesContent": ["\n\nimport { Singleton } from \"../../meshTools/Singleton\";\nimport { EventType } from \"../common/EventCenter\";\nimport { GameMgr } from \"../common/GameMgr\";\nimport { ReceivedMessageBean, SendMessageBean } from \"./MessageBaseBean\";\nimport { MessageId } from \"./MessageId\";\nimport { WebSocketTool, WebSocketToolState } from \"./WebSocketTool\";\n\nexport class WebSocketManager extends Singleton {\n\n    \n    webState: WebSocketToolState = WebSocketToolState.Closing;\n    //连接 socket\n    connect() {\n        WebSocketTool.GetInstance().setSocketFunction(this.msgData, this.socketState);\n        WebSocketTool.GetInstance().connect();//连接长链接\n    }\n\n    //这个是用来接收服务器返回回来的数据\n    private msgData(msg: string) {\n        const parsedData = JSON.parse(msg) as ReceivedMessageBean;\n        if (parsedData.code == 0) {//正常数据\n            GameMgr.Event.Send(EventType.ReceiveMessage, parsedData);//将收到的消息发送出去\n        } else {\n            GameMgr.Event.Send(EventType.ReceiveErrorMessage, parsedData);//将收到的消息发送出去\n        }\n    }\n\n    //这个是接收 websocket 状态的\n    private socketState(webState: WebSocketToolState) {\n        GameMgr.Console.Log('webSocket状态' + webState);\n        WebSocketManager.GetInstance().webState = webState;\n    }\n\n\n    //发送消息  data 请求参数 是个对象{}\n    sendMsg(msgId: MessageId, data: any) {\n\n        let sendMessageBean: SendMessageBean = {\n            msgId: msgId,\n            data: data,\n        };\n        const jsonString = JSON.stringify(sendMessageBean);\n\n        WebSocketTool.GetInstance().send(jsonString);\n    }\n\n\n}"]}