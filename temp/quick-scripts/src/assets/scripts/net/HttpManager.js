"use strict";
cc._RF.push(module, '8405dpDwZlPW5cASlfBvNdT', 'HttpManager');
// scripts/net/HttpManager.ts

"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HttpManager = void 0;
var HttpUtils_1 = require("./HttpUtils");
var MessageBaseBean_1 = require("./MessageBaseBean");
var GameServerUrl_1 = require("./GameServerUrl");
var MeshTools_1 = require("../../meshTools/MeshTools");
var GameMgr_1 = require("../common/GameMgr");
var EventCenter_1 = require("../common/EventCenter");
var GameRouteUrlDef = {
    0: {
        101: "https://game-dev.jieyou.shop/game_route/"
    },
    1: {
        101: "https://game-cn-test.jieyou.shop/game_route/",
        8001: "https://gameapi-test.soofun.online/game_route/"
    },
    2: {
        101: "https://mesh-gameapi.jieyou.shop/game_route/",
        201: "https://aws-gameapi.jieyou.shop/game_route/",
        301: "https://bysk.gameapi.gg.jieyou.shop/game_route/",
        401: "https://gameapi.fra.jieyou.shop/game_route/",
        8001: "https://gameapi.soofun.online/game_route/"
    }
};
var InterfaceDef;
(function (InterfaceDef) {
    InterfaceDef["GET_ADDRESS"] = "get_addr";
    InterfaceDef["UPDATE_TIME"] = "update_time";
})(InterfaceDef || (InterfaceDef = {}));
var HttpManager = /** @class */ (function () {
    function HttpManager() {
        this.CheckUrlTimer = null;
    }
    Object.defineProperty(HttpManager, "Instance", {
        get: function () {
            if (this._inst == null) {
                this._inst = new HttpManager();
            }
            return this._inst;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(HttpManager.prototype, "Url", {
        get: function () {
            var env = MeshTools_1.MeshTools.Publish.getEnv();
            return GameRouteUrlDef[env][MeshTools_1.MeshTools.Publish.gsp];
        },
        enumerable: false,
        configurable: true
    });
    HttpManager.prototype.ReqServerUrl = function (callBack) {
        return __awaiter(this, void 0, void 0, function () {
            var params, url, data, autoMessageBean_1, autoMessageBean;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        params = {};
                        params.user_id = MeshTools_1.MeshTools.Publish.userId;
                        params.room_id = MeshTools_1.MeshTools.Publish.roomId;
                        params.game_mode = MeshTools_1.MeshTools.Publish.gameMode;
                        params.game_id = MeshTools_1.MeshTools.Publish.getGameId().toString();
                        params.app_id = MeshTools_1.MeshTools.Publish.appId.toString();
                        params.app_channel = MeshTools_1.MeshTools.Publish.appChannel;
                        url = this.Url + InterfaceDef.GET_ADDRESS;
                        return [4 /*yield*/, HttpUtils_1.HttpUtils.SyncGet(url, params)];
                    case 1:
                        data = _a.sent();
                        if (data.code == 200) {
                            GameServerUrl_1.GameServerUrl.Http = data.data.http_addr;
                            GameServerUrl_1.GameServerUrl.Ws = data.data.ws_addr;
                            this.StartCheckServerUrlState();
                            callBack();
                            autoMessageBean_1 = {
                                'msgId': MessageBaseBean_1.AutoMessageId.HttpSucceed,
                                'data': {}
                            };
                            GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.AutoMessage, autoMessageBean_1);
                            return [2 /*return*/];
                        }
                        autoMessageBean = {
                            'msgId': MessageBaseBean_1.AutoMessageId.GameRouteNotFoundMsg,
                            'data': { code: window.getLocalizedStr('GameRouteNotFound') }
                        };
                        GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.AutoMessage, autoMessageBean);
                        return [2 /*return*/];
                }
            });
        });
    };
    HttpManager.prototype.ReqServerUrlState = function () {
        var _this = this;
        if (!window.navigator.onLine) {
            setTimeout(function () {
                GameMgr_1.GameMgr.Console.Error('短链接重连');
                _this.ReqServerUrlState();
            }, 1000);
            return;
        }
        var params = {};
        params.user_id = MeshTools_1.MeshTools.Publish.userId;
        params.room_id = MeshTools_1.MeshTools.Publish.roomId;
        params.game_mode = MeshTools_1.MeshTools.Publish.gameMode;
        params.game_id = MeshTools_1.MeshTools.Publish.getGameId().toString();
        params.app_id = MeshTools_1.MeshTools.Publish.appId.toString();
        params.app_channel = MeshTools_1.MeshTools.Publish.appChannel;
        params.curr_http_addr = GameServerUrl_1.GameServerUrl.Http;
        params.curr_ws_addr = GameServerUrl_1.GameServerUrl.Ws;
        var url = this.Url + InterfaceDef.UPDATE_TIME;
        HttpUtils_1.HttpUtils.Get(url, params, function (data) {
            if (data.code == 200) {
                _this.StartCheckServerUrlState();
            }
            else {
                _this.StopCheckServerUrlState();
                _this.ReqServerUrl(function () { });
            }
        });
    };
    HttpManager.prototype.StartCheckServerUrlState = function () {
        var _this = this;
        this.CheckUrlTimer = setTimeout(function () {
            _this.ReqServerUrlState();
        }, 180000); // 这里是三分钟
    };
    HttpManager.prototype.StopCheckServerUrlState = function () {
        if (this.CheckUrlTimer != null) {
            clearInterval(this.CheckUrlTimer);
            this.CheckUrlTimer = null;
        }
    };
    HttpManager._inst = null;
    return HttpManager;
}());
exports.HttpManager = HttpManager;

cc._RF.pop();