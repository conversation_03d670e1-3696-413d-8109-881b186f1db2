{"version": 3, "sources": ["assets/scripts/net/MessageBaseBean.ts"], "names": [], "mappings": ";;;;;;;AAsBA,IAAY,aAmBX;AAnBD,WAAY,aAAa;IAErB,4CAA2B,CAAA;IAC3B,+DAA8C,CAAA;IAC9C,sDAAqC,CAAA;IACrC,8DAA6C,CAAA;IAC7C,8CAA6B,CAAA;IAM7B,uDAAsC,CAAA;IACtC,oDAAmC,CAAA;IACnC,4DAA2C,CAAA;IAE3C,0DAAyC,CAAA;IACzC,wEAAuD,CAAA;AAE3D,CAAC,EAnBW,aAAa,GAAb,qBAAa,KAAb,qBAAa,QAmBxB", "file": "", "sourceRoot": "/", "sourcesContent": ["//发消息的对象\nexport interface SendMessageBean {\n    msgId: string;\n    data:  any;\n}\n\n//收消息的对象\nexport interface ReceivedMessageBean {\n    msgId: string;\n    code:  number;\n    msg:   string;\n    data:  any;\n}\n\n\n\n//程序内部的通知\nexport interface AutoMessageBean {\n    msgId: string;\n    data:  any;\n}\n\nexport enum AutoMessageId {\n\n    HttpSucceed = 'httpSucceed',//短链链接成功\n    ReconnectionFailureMsg = 'reconnectionFailure',//长链接重连失败\n    LinkExceptionMsg = 'LinkExceptionMsg',//长链接异常\n    GameRouteNotFoundMsg = 'gameRouteNotFoundMsg',//游戏线路异常的通知\n    JumpHallPage = 'jumpHallPage', //跳转进大厅页面\n\n\n\n\n\n    SwitchGameSceneMsg = 'switchGameScene',//切换游戏场景\n    WalletUpdateMsg = 'walletUpdateMsg',//更新金豆余额的通知\n    ServerCodeUpdateMsg = 'serverCodeUpdateMsg',//更新 code 的通知\n    \n    LoginSuccessfulMsg = 'LoginSuccessfulMsg',//登录成功\n    FailedToDeductGoldCoins = 'Failed to deduct gold coins',//扣除金币失败\n\n}\n\n"]}