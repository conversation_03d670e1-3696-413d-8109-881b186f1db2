{"version": 3, "sources": ["assets/scripts/net/HttpManager.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,yCAAwC;AACxC,qDAAmE;AACnE,iDAAgD;AAChD,uDAAsD;AACtD,6CAA4C;AAC5C,qDAAkD;AAElD,IAAM,eAAe,GAAG;IACpB,CAAC,EAAE;QACC,GAAG,EAAE,0CAA0C;KAClD;IAED,CAAC,EAAE;QACC,GAAG,EAAE,8CAA8C;QACnD,IAAI,EAAE,gDAAgD;KACzD;IAED,CAAC,EAAE;QACC,GAAG,EAAE,8CAA8C;QACnD,GAAG,EAAE,6CAA6C;QAClD,GAAG,EAAE,iDAAiD;QACtD,GAAG,EAAE,6CAA6C;QAClD,IAAI,EAAE,2CAA2C;KACpD;CACJ,CAAA;AAID,IAAK,YAGJ;AAHD,WAAK,YAAY;IACb,wCAAwB,CAAA;IACxB,2CAA2B,CAAA;AAC/B,CAAC,EAHI,YAAY,KAAZ,YAAY,QAGhB;AAED;IAAA;QAYW,kBAAa,GAAQ,IAAI,CAAC;IAiFrC,CAAC;IAzFG,sBAAkB,uBAAQ;aAA1B;YACI,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,EAAE;gBACpB,IAAI,CAAC,KAAK,GAAG,IAAI,WAAW,EAAE,CAAC;aAClC;YAED,OAAO,IAAI,CAAC,KAAK,CAAC;QACtB,CAAC;;;OAAA;IAID,sBAAW,4BAAG;aAAd;YACI,IAAI,GAAG,GAAW,qBAAS,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YAC7C,OAAO,eAAe,CAAC,GAAG,CAAC,CAAC,qBAAS,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QACvD,CAAC;;;OAAA;IAEY,kCAAY,GAAzB,UAA0B,QAAiB;;;;;;wBACnC,MAAM,GAAoC,EAAqC,CAAC;wBACpF,MAAM,CAAC,OAAO,GAAG,qBAAS,CAAC,OAAO,CAAC,MAAM,CAAC;wBAC1C,MAAM,CAAC,OAAO,GAAG,qBAAS,CAAC,OAAO,CAAC,MAAM,CAAC;wBAC1C,MAAM,CAAC,SAAS,GAAG,qBAAS,CAAC,OAAO,CAAC,QAAQ,CAAC;wBAC9C,MAAM,CAAC,OAAO,GAAG,qBAAS,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC,QAAQ,EAAE,CAAC;wBAC1D,MAAM,CAAC,MAAM,GAAG,qBAAS,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;wBACnD,MAAM,CAAC,WAAW,GAAG,qBAAS,CAAC,OAAO,CAAC,UAAU,CAAC;wBAE9C,GAAG,GAAW,IAAI,CAAC,GAAG,GAAG,YAAY,CAAC,WAAW,CAAC;wBACV,qBAAM,qBAAS,CAAC,OAAO,CAAC,GAAG,EAAE,MAAM,CAAC,EAAA;;wBAA5E,IAAI,GAAoC,SAAoC;wBAChF,IAAI,IAAI,CAAC,IAAI,IAAI,GAAG,EAAE;4BAClB,6BAAa,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;4BACzC,6BAAa,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;4BACrC,IAAI,CAAC,wBAAwB,EAAE,CAAC;4BAChC,QAAQ,EAAE,CAAA;4BACN,oBAAmC;gCACnC,OAAO,EAAE,+BAAa,CAAC,WAAW;gCAClC,MAAM,EAAE,EAAE;6BACb,CAAA;4BACD,iBAAO,CAAC,KAAK,CAAC,IAAI,CAAC,uBAAS,CAAC,WAAW,EAAE,iBAAe,CAAC,CAAC;4BAC3D,sBAAO;yBACV;wBAEG,eAAe,GAAoB;4BACnC,OAAO,EAAE,+BAAa,CAAC,oBAAoB;4BAC3C,MAAM,EAAE,EAAC,IAAI,EAAE,MAAM,CAAC,eAAe,CAAC,mBAAmB,CAAC,EAAC;yBAC9D,CAAA;wBACD,iBAAO,CAAC,KAAK,CAAC,IAAI,CAAC,uBAAS,CAAC,WAAW,EAAE,eAAe,CAAC,CAAC;;;;;KAC9D;IAEM,uCAAiB,GAAxB;QAAA,iBA6BC;QA5BG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE;YAC1B,UAAU,CAAC;gBACP,iBAAO,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;gBAC9B,KAAI,CAAC,iBAAiB,EAAE,CAAC;YAC7B,CAAC,EAAE,IAAI,CAAC,CAAC;YACT,OAAO;SACV;QAED,IAAI,MAAM,GAAgC,EAAiC,CAAC;QAC5E,MAAM,CAAC,OAAO,GAAG,qBAAS,CAAC,OAAO,CAAC,MAAM,CAAC;QAC1C,MAAM,CAAC,OAAO,GAAG,qBAAS,CAAC,OAAO,CAAC,MAAM,CAAC;QAC1C,MAAM,CAAC,SAAS,GAAG,qBAAS,CAAC,OAAO,CAAC,QAAQ,CAAC;QAC9C,MAAM,CAAC,OAAO,GAAG,qBAAS,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC,QAAQ,EAAE,CAAC;QAC1D,MAAM,CAAC,MAAM,GAAG,qBAAS,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;QACnD,MAAM,CAAC,WAAW,GAAG,qBAAS,CAAC,OAAO,CAAC,UAAU,CAAC;QAClD,MAAM,CAAC,cAAc,GAAG,6BAAa,CAAC,IAAI,CAAC;QAC3C,MAAM,CAAC,YAAY,GAAG,6BAAa,CAAC,EAAE,CAAC;QAEvC,IAAI,GAAG,GAAW,IAAI,CAAC,GAAG,GAAG,YAAY,CAAC,WAAW,CAAC;QACtD,qBAAS,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,EAAE,UAAC,IAA2B;YACnD,IAAI,IAAI,CAAC,IAAI,IAAI,GAAG,EAAE;gBAClB,KAAI,CAAC,wBAAwB,EAAE,CAAC;aACnC;iBACI;gBACD,KAAI,CAAC,uBAAuB,EAAE,CAAC;gBAC/B,KAAI,CAAC,YAAY,CAAC,cAAK,CAAC,CAAC,CAAC;aAC7B;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAEM,8CAAwB,GAA/B;QAAA,iBAIC;QAHG,IAAI,CAAC,aAAa,GAAG,UAAU,CAAC;YAC5B,KAAI,CAAC,iBAAiB,EAAE,CAAC;QAC7B,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,SAAS;IACzB,CAAC;IAEM,6CAAuB,GAA9B;QACI,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,EAAE;YAC5B,aAAa,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAClC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;SAC7B;IACL,CAAC;IA1Fc,iBAAK,GAAgB,IAAI,CAAC;IA2F7C,kBAAC;CA7FD,AA6FC,IAAA;AA7FY,kCAAW", "file": "", "sourceRoot": "/", "sourcesContent": ["\r\n\r\nimport { IHttpMsgBody } from \"./IHttpMsgBody\";\r\nimport { HttpUtils } from \"./HttpUtils\";\r\nimport { AutoMessageBean, AutoMessageId } from \"./MessageBaseBean\";\r\nimport { GameServerUrl } from \"./GameServerUrl\";\r\nimport { MeshTools } from \"../../meshTools/MeshTools\";\r\nimport { GameMgr } from \"../common/GameMgr\";\r\nimport { EventType } from \"../common/EventCenter\";\r\n\r\nconst GameRouteUrlDef = {\r\n    0: {\r\n        101: \"https://game-dev.jieyou.shop/game_route/\"\r\n    },\r\n\r\n    1: {\r\n        101: \"https://game-cn-test.jieyou.shop/game_route/\",\r\n        8001: \"https://gameapi-test.soofun.online/game_route/\"\r\n    },\r\n\r\n    2: {\r\n        101: \"https://mesh-gameapi.jieyou.shop/game_route/\",\r\n        201: \"https://aws-gameapi.jieyou.shop/game_route/\",\r\n        301: \"https://bysk.gameapi.gg.jieyou.shop/game_route/\",\r\n        401: \"https://gameapi.fra.jieyou.shop/game_route/\",\r\n        8001: \"https://gameapi.soofun.online/game_route/\"\r\n    }\r\n}\r\n\r\n\r\n\r\nenum InterfaceDef {\r\n    GET_ADDRESS = \"get_addr\",\r\n    UPDATE_TIME = \"update_time\"\r\n}\r\n\r\nexport class HttpManager {\r\n\r\n    private static _inst: HttpManager = null;\r\n\r\n    public static get Instance(): HttpManager {\r\n        if (this._inst == null) {\r\n            this._inst = new HttpManager();\r\n        }\r\n\r\n        return this._inst;\r\n    }\r\n\r\n    public CheckUrlTimer: any = null;\r\n\r\n    public get Url(): string {\r\n        let env: number = MeshTools.Publish.getEnv();\r\n        return GameRouteUrlDef[env][MeshTools.Publish.gsp];\r\n    }\r\n\r\n    public async ReqServerUrl(callBack:Function) {\r\n        let params: IHttpMsgBody.IReqGetGameAddress = {} as IHttpMsgBody.IReqGetGameAddress;\r\n        params.user_id = MeshTools.Publish.userId;\r\n        params.room_id = MeshTools.Publish.roomId;\r\n        params.game_mode = MeshTools.Publish.gameMode;\r\n        params.game_id = MeshTools.Publish.getGameId().toString();\r\n        params.app_id = MeshTools.Publish.appId.toString();\r\n        params.app_channel = MeshTools.Publish.appChannel;\r\n\r\n        let url: string = this.Url + InterfaceDef.GET_ADDRESS;\r\n        let data: IHttpMsgBody.IResGetGameAddress = await HttpUtils.SyncGet(url, params);\r\n        if (data.code == 200) {\r\n            GameServerUrl.Http = data.data.http_addr;\r\n            GameServerUrl.Ws = data.data.ws_addr;\r\n            this.StartCheckServerUrlState();\r\n            callBack()\r\n            let autoMessageBean: AutoMessageBean = {\r\n                'msgId': AutoMessageId.HttpSucceed,//短链链接成功\r\n                'data': {}\r\n            }\r\n            GameMgr.Event.Send(EventType.AutoMessage, autoMessageBean);\r\n            return;\r\n        }\r\n\r\n        let autoMessageBean: AutoMessageBean = {\r\n            'msgId': AutoMessageId.GameRouteNotFoundMsg,//游戏线路异常的通知\r\n            'data': {code: window.getLocalizedStr('GameRouteNotFound')}\r\n        }\r\n        GameMgr.Event.Send(EventType.AutoMessage, autoMessageBean);\r\n    }\r\n\r\n    public ReqServerUrlState(): void {\r\n        if (!window.navigator.onLine) {\r\n            setTimeout(() => {\r\n                GameMgr.Console.Error('短链接重连')\r\n                this.ReqServerUrlState();\r\n            }, 1000);\r\n            return;\r\n        }\r\n\r\n        let params: IHttpMsgBody.IReqUpdateTime = {} as IHttpMsgBody.IReqUpdateTime;\r\n        params.user_id = MeshTools.Publish.userId;\r\n        params.room_id = MeshTools.Publish.roomId;\r\n        params.game_mode = MeshTools.Publish.gameMode;\r\n        params.game_id = MeshTools.Publish.getGameId().toString();\r\n        params.app_id = MeshTools.Publish.appId.toString();\r\n        params.app_channel = MeshTools.Publish.appChannel;\r\n        params.curr_http_addr = GameServerUrl.Http;\r\n        params.curr_ws_addr = GameServerUrl.Ws;\r\n\r\n        let url: string = this.Url + InterfaceDef.UPDATE_TIME;\r\n        HttpUtils.Get(url, params, (data: IHttpMsgBody.IResBase) => {\r\n            if (data.code == 200) {\r\n                this.StartCheckServerUrlState();\r\n            }\r\n            else {\r\n                this.StopCheckServerUrlState();\r\n                this.ReqServerUrl(()=>{});\r\n            }\r\n        });\r\n    }\r\n\r\n    public StartCheckServerUrlState(): void {\r\n        this.CheckUrlTimer = setTimeout(() => {\r\n            this.ReqServerUrlState();\r\n        }, 180000); // 这里是三分钟\r\n    }\r\n\r\n    public StopCheckServerUrlState(): void {\r\n        if (this.CheckUrlTimer != null) {\r\n            clearInterval(this.CheckUrlTimer);\r\n            this.CheckUrlTimer = null;\r\n        }\r\n    }\r\n}"]}