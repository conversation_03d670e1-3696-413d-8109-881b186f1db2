"use strict";
cc._RF.push(module, '2cfadyBGwdHeYPuuZnDcrEv', 'MessageBaseBean');
// scripts/net/MessageBaseBean.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AutoMessageId = void 0;
var AutoMessageId;
(function (AutoMessageId) {
    AutoMessageId["HttpSucceed"] = "httpSucceed";
    AutoMessageId["ReconnectionFailureMsg"] = "reconnectionFailure";
    AutoMessageId["LinkExceptionMsg"] = "LinkExceptionMsg";
    AutoMessageId["GameRouteNotFoundMsg"] = "gameRouteNotFoundMsg";
    AutoMessageId["JumpHallPage"] = "jumpHallPage";
    AutoMessageId["SwitchGameSceneMsg"] = "switchGameScene";
    AutoMessageId["WalletUpdateMsg"] = "walletUpdateMsg";
    AutoMessageId["ServerCodeUpdateMsg"] = "serverCodeUpdateMsg";
    AutoMessageId["LoginSuccessfulMsg"] = "LoginSuccessfulMsg";
    AutoMessageId["FailedToDeductGoldCoins"] = "Failed to deduct gold coins";
})(AutoMessageId = exports.AutoMessageId || (exports.AutoMessageId = {}));

cc._RF.pop();