{"version": 3, "sources": ["assets/scripts/net/WebSocketTool.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA,qDAAyG;AACzG,yCAAwC;AACxC,uCAAsC;AACtC,uDAAsD;AACtD,qDAAkD;AAClD,6CAA4C;AAE5C,IAAY,kBAMX;AAND,WAAY,kBAAkB;IAC1B,+CAAyB,CAAA;IACzB,6CAAuB,CAAA;IACvB,yCAAmB,CAAA;IACnB,mDAA6B,CAAA;IAC7B,yCAAmB,CAAA;AACvB,CAAC,EANW,kBAAkB,GAAlB,0BAAkB,KAAlB,0BAAkB,QAM7B;AAGD;IAAmC,iCAAS;IAA5C;QAAA,qEAiMC;QA/LW,cAAQ,GAAuB,kBAAkB,CAAC,OAAO,CAAC,CAAA,QAAQ;QAClE,uBAAiB,GAAW,IAAI,CAAC,CAAC,YAAY;QAC9C,uBAAiB,GAAW,IAAI,CAAC,CAAC,YAAY;QAC9C,oBAAc,GAAW,IAAI,CAAC,CAAC,WAAW;QAC1C,uBAAiB,GAAW,CAAC,CAAC,CAAC,SAAS;QACxC,oBAAc,GAAW,CAAC,CAAC,CAAC,SAAS;QAErC,uBAAiB,GAAW,IAAI,CAAC,CAAC,YAAY;QAC9C,0BAAoB,GAAW,KAAK,CAAC,CAAC,aAAa;QAEnD,iBAAW,GAAa,IAAI,CAAC,CAAA,WAAW;QACxC,mBAAa,GAAa,IAAI,CAAC,CAAA,kBAAkB;;IAoL7D,CAAC;IAlLU,yCAAiB,GAAxB,UAAyB,WAAqB,EAAE,aAAuB;QACnE,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;IACvC,CAAC;IAED,IAAI;IACG,+BAAO,GAAd;QAAA,iBAgDC;QA/CG,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;QAC5B,IAAI,IAAI,CAAC,QAAQ,IAAI,kBAAkB,CAAC,UAAU,IAAI,IAAI,CAAC,QAAQ,IAAI,kBAAkB,CAAC,SAAS;YAAE,OAAO;QAC5G,IAAI,GAAG,GAAW,iBAAO,CAAC,QAAQ,CAAC,SAAS,CAAC;QAC7C,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;QAChD,IAAI,CAAC,EAAE,GAAG,IAAI,SAAS,CAAC,GAAG,CAAC,CAAC;QAC7B,iBAAO,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,GAAG,GAAG,CAAC,CAAC;QAC5C,MAAM;QACN,IAAI,CAAC,EAAE,CAAC,MAAM,GAAG;YACb,KAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;YAC/C,aAAa;YACb,KAAI,CAAC,aAAa,EAAE,CAAC;YACrB,KAAI,CAAC,gBAAgB,EAAE,CAAA;YACvB,KAAI,CAAC,cAAc,GAAG,CAAC,CAAC,CAAA,cAAc;QAC1C,CAAC,CAAC;QAEF,MAAM;QACN,IAAI,CAAC,EAAE,CAAC,SAAS,GAAG,UAAC,KAAK;YACtB,WAAW;YACX,iBAAO,CAAC,OAAO,CAAC,GAAG,CAAC,wCAAkB,aAAK,CAAC,8BAA8B,EAAE,UAAK,KAAK,CAAC,IAAM,CAAC,CAAC;YAC/F,IAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAwB,CAAC;YACjE,IAAI,UAAU,CAAC,KAAK,IAAI,qBAAS,CAAC,gBAAgB,EAAE;gBAChD,KAAI,CAAC,gBAAgB,EAAE,CAAA;aAC1B;YAGD,IAAI,KAAI,CAAC,WAAW,EAAE;gBAClB,KAAI,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;aAChC;QACL,CAAC,CAAC;QAEF,MAAM;QACN,IAAI,CAAC,EAAE,CAAC,OAAO,GAAG;YACd,IAAI,KAAI,CAAC,QAAQ,IAAI,kBAAkB,CAAC,YAAY,EAAC;gBACjD,kDAAkD;gBAClD,OAAO,CAAC,KAAK,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAC;gBAC3C,OAAM;aACT;YAED,OAAO,CAAC,KAAK,CAAC,kBAAkB,EAAE,MAAM,CAAC,CAAC;YAC1C,KAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;YAC7C,KAAI,CAAC,aAAa,EAAE,CAAA;QACxB,CAAC,CAAC;QAEF,MAAM;QACN,IAAI,CAAC,EAAE,CAAC,OAAO,GAAG,UAAC,KAAK;YACpB,OAAO,CAAC,KAAK,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;QAC7C,CAAC,CAAC;IACN,CAAC;IAED,QAAQ;IACD,kCAAU,GAAjB;QACI,OAAO,CAAC,KAAK,CAAC,kBAAkB,EAAE,MAAM,CAAC,CAAC;QAC1C,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC3B,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC;IACpB,CAAC;IAGD,YAAY;IACJ,iCAAS,GAAjB;QAAA,iBAmBC;QAlBG,IAAI,IAAI,CAAC,QAAQ,IAAI,kBAAkB,CAAC,YAAY,IAAI,IAAI,CAAC,QAAQ,IAAI,kBAAkB,CAAC,UAAU;YAAE,OAAO;QAE/G,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,iBAAiB,EAAE;YAC/C,OAAO,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;YAChC,IAAI,eAAe,GAAoB;gBACnC,OAAO,EAAE,+BAAa,CAAC,sBAAsB;gBAC7C,MAAM,EAAE,EAAE;aACb,CAAA;YACD,iBAAO,CAAC,KAAK,CAAC,IAAI,CAAC,uBAAS,CAAC,WAAW,EAAE,eAAe,CAAC,CAAC;YAC3D,OAAO;SACV;QACD,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC;QAElD,YAAY;QACZ,UAAU,CAAC;YACP,KAAI,CAAC,OAAO,EAAE,CAAC;QACnB,CAAC,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;IAC/B,CAAC;IAED,YAAY;IACL,uCAAe,GAAtB;QACI,IAAI,CAAC,OAAO,EAAE,CAAC;IACnB,CAAC;IAED,MAAM;IACE,qCAAa,GAArB;QAAA,iBAgBC;QAfG,IAAI,IAAI,CAAC,QAAQ,IAAI,kBAAkB,CAAC,SAAS;YAAE,OAAO;QAE1D,IAAI,eAAe,GAAoB;YACnC,KAAK,EAAE,qBAAS,CAAC,gBAAgB;YACjC,IAAI,EAAE,EAAE;SACX,CAAC;QACF,IAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;QAEnD,QAAQ;QACR,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAEtB,gBAAgB;QAChB,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC;YAC7B,KAAI,CAAC,aAAa,EAAE,CAAC;QACzB,CAAC,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;IAC/B,CAAC;IAGO,wCAAgB,GAAxB;QAAA,iBAUC;QATG,IAAI,CAAC,aAAa,EAAE,CAAC;QACrB,IAAI,CAAC,iBAAiB,GAAG,UAAU,CAAC;YAChC,IAAI,KAAI,CAAC,QAAQ,IAAI,kBAAkB,CAAC,SAAS,EAAE;gBAC/C,OAAO,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;gBAChC,KAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;gBAC/C,KAAI,CAAC,aAAa,EAAE,CAAA;aACrB;QAEL,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,WAAW;IAC9C,CAAC;IAED,MAAM;IACN,qCAAa,GAAb;QACI,IAAI,CAAC,aAAa,EAAE,CAAC;QACrB,IAAI,CAAC,aAAa,EAAE,CAAC;QACrB,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACtB,IAAI,eAAe,GAAoB;gBACnC,OAAO,EAAE,+BAAa,CAAC,gBAAgB;gBACvC,MAAM,EAAE,EAAE;aACb,CAAA;YACD,iBAAO,CAAC,KAAK,CAAC,IAAI,CAAC,uBAAS,CAAC,WAAW,EAAE,eAAe,CAAC,CAAC;YAC3D,YAAY;YACZ,IAAI,CAAC,SAAS,EAAE,CAAC;SACpB;IACL,CAAC;IAGD,MAAM;IACE,qCAAa,GAArB;QACI,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,UAAU;YACV,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAClC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;SAC9B;IAEL,CAAC;IAED,QAAQ;IACA,qCAAa,GAArB;QACI,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,YAAY,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YACrC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAA;SAChC;IACL,CAAC;IAED,MAAM;IACC,4BAAI,GAAX,UAAY,OAAe;QACvB,iBAAO,CAAC,OAAO,CAAC,GAAG,CAAC,wCAAkB,aAAK,CAAC,8BAA8B,EAAE,UAAK,OAAS,CAAC,CAAC;QAC5F,IAAI,IAAI,CAAC,QAAQ,IAAI,kBAAkB,CAAC,SAAS,EAAE;YAC/C,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SACzB;aAAM;YACH,iBAAO,CAAC,OAAO,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAC;SACvD;IACL,CAAC;IAED,iBAAiB;IACjB,mCAAW,GAAX,UAAY,QAA4B;QACpC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAA;IAChC,CAAC;IAED,iCAAS,GAAT;QACI,UAAU;QACV,IAAI,CAAC,aAAa,EAAE,CAAC;QACrB,IAAI,CAAC,aAAa,EAAE,CAAC;IACzB,CAAC;IACL,oBAAC;AAAD,CAjMA,AAiMC,CAjMkC,qBAAS,GAiM3C;AAjMY,sCAAa", "file": "", "sourceRoot": "/", "sourcesContent": ["import { AutoMessageBean, AutoMessageId, ReceivedMessageBean, SendMessageBean } from \"./MessageBaseBean\";\nimport { MessageId } from \"./MessageId\";\nimport { Tools } from \"../util/Tools\";\nimport { Singleton } from \"../../meshTools/Singleton\";\nimport { EventType } from \"../common/EventCenter\";\nimport { GameMgr } from \"../common/GameMgr\";\n\nexport enum WebSocketToolState {\n    Connecting = 'connecting', //连接中\n    Connected = 'connected', //连接成功\n    Closing = 'closing', //关闭\n    Reconnecting = 'reconnecting',//重连中\n    Timeout = 'Timeout',//链接超时\n}\n\n\nexport class WebSocketTool extends Singleton {\n    private ws: WebSocket;\n    private webState: WebSocketToolState = WebSocketToolState.Closing;//默认是关闭的\n    private reconnectInterval: number = 5000; // 重连间隔，单位毫秒\n    private heartbeatInterval: number = 5000; // 心跳间隔，单位毫秒\n    private heartbeatTimer: number = null; //心跳定时器的 id\n    private reconnectMaxCount: number = 5; // 最大重连次数\n    private reconnectCount: number = 0; // 当前重连次数\n    private activeShutdown: boolean;//是不是主动关闭的\n    private heartTimeOutTimer: number = null; //心跳超时定时器 id\n    private heartTimeOutInterval: number = 11000; //心跳超时间隔，单位毫秒\n\n    private msgFunction: Function = null;//这个是回调消息的的\n    private stateFunction: Function = null;//这个是回调 socket 状态的\n\n    public setSocketFunction(msgFunction: Function, stateFunction: Function) {\n        this.msgFunction = msgFunction;\n        this.stateFunction = stateFunction;\n    }\n\n    //连接\n    public connect(): void {\n        this.activeShutdown = false;\n        if (this.webState == WebSocketToolState.Connecting || this.webState == WebSocketToolState.Connected) return;\n        let url: string = GameMgr.GameData.ServerUrl;\n        this.setWebState(WebSocketToolState.Connecting);\n        this.ws = new WebSocket(url);\n        GameMgr.Console.Log('webSocket 链接地址' + url);\n        //连接成功\n        this.ws.onopen = () => {\n            this.setWebState(WebSocketToolState.Connected);\n            // 连接成功后发送心跳包\n            this.sendHeartbeat();\n            this.sendHeartTimeOut()\n            this.reconnectCount = 0;//连接上之后 重置重连次数\n        };\n\n        //收到消息\n        this.ws.onmessage = (event) => {\n            // 处理接收到的消息\n            GameMgr.Console.Log(`webSocket 收到消息(${Tools.getCurrentTimeWithMilliseconds()}):${event.data}`);\n            const parsedData = JSON.parse(event.data) as ReceivedMessageBean;\n            if (parsedData.msgId == MessageId.MsgTypeHeartbeat) {\n                this.sendHeartTimeOut()\n            }\n\n\n            if (this.msgFunction) {\n                this.msgFunction(event.data);\n            }\n        };\n\n        //关闭连接\n        this.ws.onclose = () => {\n            if( this.webState == WebSocketToolState.Reconnecting){\n                //在 ios 系统中 断网不会断开 websocket，所以在这状态下收到的 链接关闭都是上一个的\n                console.error('WebSocket error:', '旧链接关闭');\n                return\n            }\n\n            console.error('WebSocket error:', '连接关闭');\n            this.setWebState(WebSocketToolState.Closing);\n            this.linkException()\n        };\n\n        //连接异常\n        this.ws.onerror = (event) => {\n            console.error('WebSocket error:', event);\n        };\n    }\n\n    //主动断开连接\n    public disconnect(): void {\n        console.error('WebSocket error:', '主动断开');\n        this.activeShutdown = true;\n        this.ws.close();\n    }\n\n\n    //重连，内部自动调用的\n    private reconnect(): void {\n        if (this.webState == WebSocketToolState.Reconnecting || this.webState == WebSocketToolState.Connecting) return;\n\n        if (this.reconnectCount >= this.reconnectMaxCount) {\n            console.error('WebSocket 重连失败');\n            let autoMessageBean: AutoMessageBean = {\n                'msgId': AutoMessageId.ReconnectionFailureMsg,//长链接重连失败\n                'data': {}\n            }\n            GameMgr.Event.Send(EventType.AutoMessage, autoMessageBean);\n            return;\n        }\n        this.reconnectCount++;\n        this.setWebState(WebSocketToolState.Reconnecting);\n\n        // 重连间隔后重新连接\n        setTimeout(() => {\n            this.connect();\n        }, this.reconnectInterval);\n    }\n\n    //给外部调用的马上重连\n    public atOnceReconnect() {\n        this.connect();\n    }\n\n    //启动心跳\n    private sendHeartbeat(): void {\n        if (this.webState != WebSocketToolState.Connected) return;\n\n        let sendMessageBean: SendMessageBean = {\n            msgId: MessageId.MsgTypeHeartbeat,\n            data: {},\n        };\n        const jsonString = JSON.stringify(sendMessageBean);\n\n        // 发送心跳包\n        this.send(jsonString);\n\n        // 每隔心跳间隔发送一次心跳包\n        this.heartbeatTimer = setTimeout(() => {\n            this.sendHeartbeat();\n        }, this.heartbeatInterval);\n    }\n\n\n    private sendHeartTimeOut(): void {\n        this.stopHeartTime();\n        this.heartTimeOutTimer = setTimeout(() => {\n            if (this.webState == WebSocketToolState.Connected) {\n                console.error('WebSocket 连接超时');\n                this.setWebState(WebSocketToolState.Timeout);\n              this.linkException()\n            }\n\n        }, this.heartTimeOutInterval); // 11 秒心跳超时\n    }\n\n    //链接异常\n    linkException() {\n        this.stopHeartbeat();\n        this.stopHeartTime();\n        if (!this.activeShutdown) {\n            let autoMessageBean: AutoMessageBean = {\n                'msgId': AutoMessageId.LinkExceptionMsg,//长链接异常\n                'data': {}\n            }\n            GameMgr.Event.Send(EventType.AutoMessage, autoMessageBean);\n            // 连接关闭后尝试重连\n            this.reconnect();\n        }\n    }\n\n\n    //停止心跳\n    private stopHeartbeat() {\n        if (this.heartbeatTimer) {\n            // 清除心跳定时器\n            clearTimeout(this.heartbeatTimer);\n            this.heartbeatTimer = null;\n        }\n\n    }\n\n    //停止超时监听\n    private stopHeartTime() {\n        if (this.heartTimeOutTimer) {\n            clearTimeout(this.heartTimeOutTimer);\n            this.heartTimeOutTimer = null\n        }\n    }\n\n    //发送消息\n    public send(message: string): void {\n        GameMgr.Console.Log(`webSocket 发送消息(${Tools.getCurrentTimeWithMilliseconds()}):${message}`);\n        if (this.webState == WebSocketToolState.Connected) {\n            this.ws.send(message);\n        } else {\n            GameMgr.Console.Error('WebSocket is not connected');\n        }\n    }\n\n    //赋值 websocket 状态\n    setWebState(webState: WebSocketToolState) {\n        this.webState = webState;\n        this.stateFunction(webState)\n    }\n\n    onDestroy() {\n        // 清除心跳定时器\n        this.stopHeartbeat();\n        this.stopHeartTime();\n    }\n}"]}