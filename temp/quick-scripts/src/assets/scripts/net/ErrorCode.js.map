{"version": 3, "sources": ["assets/scripts/net/ErrorCode.ts"], "names": [], "mappings": ";;;;;;;AAAA,KAAK;AACL,IAAY,SA+BX;AA/BD,WAAY,SAAS;IACjB,qCAAM,CAAA;IACN,mDAAa,CAAA;IACb,uDAAe,CAAA;IACf,6DAAkB,CAAA;IAClB,mDAAa,CAAA;IACb,iEAAoB,CAAA;IACpB,iEAAoB,CAAA;IACpB,+DAAmB,CAAA;IACnB,gEAAoB,CAAA;IACpB,4DAAkB,CAAA;IAClB,oDAAc,CAAA;IACd,oDAAc,CAAA;IACd,8DAAmB,CAAA;IACnB,sDAAe,CAAA;IACf,8DAAmB,CAAA;IAEnB,wDAAgB,CAAA;IAChB,sDAAe,CAAA;IACf,0EAAyB,CAAA;IACzB,4DAAkB,CAAA;IAClB,0DAAiB,CAAA;IACjB,0DAAiB,CAAA;IACjB,wEAAwB,CAAA;IACxB,oEAAsB,CAAA;IACtB,0EAAyB,CAAA;IACzB,8DAAmB,CAAA;IACnB,0DAAiB,CAAA;AAIrB,CAAC,EA/BW,SAAS,GAAT,iBAAS,KAAT,iBAAS,QA+BpB", "file": "", "sourceRoot": "/", "sourcesContent": ["//错误码\nexport enum ErrorCode {\n    OK = 0, // 操作成功\n    ErrGameId = 1, // 错误的GameId\n    ErrGameMode = 3, // 无效的游戏模式\n    ErrRequestUser = 5, // 从平台请求用户信息失败\n    ErrInPair = 6,// 玩家已经在匹配队列中\n    ErrNotEnoughCoin = 7, // 没有足够的金币\n    ErrChangeBalance = 8, // 扣除金币失败\n    ErrNotFoundRoom = 9, // 没有找到指定的房间\n    ErrNotFoundUser = 10, // 没有找到玩家信息\n    ErrRoomConfig = 12, // 房间配置出错\n    ErrParams = 13, // 请求参数错误\n    ErrDefend = 16, // 系统维护中\n    ErrSitHaveUser = 18, // 座位上已有其他玩家\n    ErrHaveSit = 19, // 玩家已坐下\n    ErrUserPlaying = 20, // 玩家游戏中\n\n    ErrInInvite = 31,// 已经在邀请中了\n    ErrPlaying = 32,// 玩家已经在游戏中了\n    ErrInvalidInviteCode = 33,// 无效的邀请码\n    ErrEnoughUser = 34,// 人够了,不能接受邀请\n    ErrNotInvite = 35,// 不在邀请队列中\n    ErrChgInvite = 36,// 修改邀请配置失败\n    ErrNotInviteCreator = 37,// 不是邀请的创建者\n    ErrForbidKickSelf = 38,// 不能踢除自己\n    ErrInviteNotAllReady = 40,// 还有玩家没有准备好\n    ErrInviteStart = 41,// 开始游戏失败\n    ErrNotInPair = 44,// 不在匹配队列中\n\n\n\n}"]}