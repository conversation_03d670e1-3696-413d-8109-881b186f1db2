{"version": 3, "sources": ["assets/scripts/net/HttpUtils.ts"], "names": [], "mappings": ";;;;;;;AAEA;IAAA;IAwGA,CAAC;IArGiB,iBAAO,GAArB,UAAyB,OAAe,EAAE,MAAW;QAArD,iBAyBC;QAxBG,OAAO,IAAI,OAAO,CAAC,UAAC,OAAO,EAAE,MAAM;YAC/B,IAAI,GAAG,GAAmB,IAAI,cAAc,EAAE,CAAC;YAC/C,IAAI,GAAG,GAAW,OAAO,GAAG,KAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YACxD,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC,OAAO;YAEnC,IAAI,aAAa,GAAW,KAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;YAC9D,KAAI,CAAC,UAAU,IAAI,OAAO,CAAC,GAAG,CAAC,aAAa,GAAG,QAAQ,EAAE,GAAG,CAAC,CAAC;YAE9D,GAAG,CAAC,kBAAkB,GAAG;gBACrB,IAAI,GAAG,CAAC,UAAU,KAAK,CAAC,EAAE,EAAE,OAAO;oBAC/B,IAAI,CAAC,GAAG,CAAC,MAAM,IAAI,GAAG,IAAI,GAAG,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,KAAK,GAAG,EAAE;wBAC/D,IAAM,IAAI,GAAM,KAAI,CAAC,WAAW,CAAC,GAAG,CAAM,CAAC;wBAC3C,KAAI,CAAC,UAAU,IAAI,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;wBACpD,OAAO,CAAC,IAAI,CAAC,CAAC;qBACjB;yBAAM;wBACH,IAAM,SAAS,GAAM,KAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,GAAG,CAAC,MAAM,CAAM,CAAC;wBACvE,KAAI,CAAC,UAAU,IAAI,OAAO,CAAC,GAAG,CAAC,aAAa,GAAG,KAAK,EAAE,SAAS,CAAC,CAAC;wBACjE,MAAM,CAAC,SAAS,CAAC,CAAC;qBACrB;iBACJ;YACL,CAAC,CAAC;YAEF,GAAG,CAAC,IAAI,EAAE,CAAC;QACf,CAAC,CAAC,CAAC;IACP,CAAC;IAEa,aAAG,GAAjB,UAAqB,OAAe,EAAE,MAAW,EAAE,EAAuB;QAA1E,iBA2BC;QAzBG,IAAI,aAAa,GAAW,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;QAE9D,IAAI,GAAG,GAAmB,IAAI,cAAc,EAAE,CAAC;QAC/C,GAAG,CAAC,kBAAkB,GAAG,UAAC,EAAS;YAE/B,IAAI,GAAG,CAAC,UAAU,IAAI,CAAC,EACvB;gBACI,IAAI,IAAI,GAAM,IAAI,CAAC;gBACnB,IAAI,CAAC,GAAG,CAAC,MAAM,IAAI,GAAG,IAAI,GAAG,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,IAAI,GAAG,EAChE;oBACI,IAAI,GAAG,KAAI,CAAC,WAAW,CAAC,GAAG,CAAM,CAAC;iBACrC;qBAED;oBACI,IAAI,GAAG,KAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,GAAG,CAAC,MAAM,CAAM,CAAC;iBAC5D;gBACD,KAAI,CAAC,UAAU,IAAI,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;gBACpD,EAAE,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC;aAClB;QACL,CAAC,CAAA;QAED,IAAI,GAAG,GAAW,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QACxD,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;QACrB,GAAG,CAAC,IAAI,EAAE,CAAC;QACX,IAAI,CAAC,UAAU,IAAI,OAAO,CAAC,GAAG,CAAC,aAAa,GAAG,QAAQ,EAAE,GAAG,CAAC,CAAC;IAClE,CAAC;IAEa,wBAAc,GAA5B,UAA8B,MAAW;QAErC,IAAI,GAAG,GAAa,EAAE,CAAC;QACvB,KAAK,IAAI,GAAG,IAAI,MAAM,EACtB;YACI,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;SACrC;QACD,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,EAClB;YACI,OAAO,MAAI,GAAG,CAAC,IAAI,CAAC,GAAG,CAAG,CAAC;SAC9B;QAED,OAAO,EAAE,CAAC;IACd,CAAC;IAEa,qBAAW,GAAzB,UAA0B,GAAmB;QAEzC,IAAI,OAAO,GAAQ,EAAE,CAAC;QACtB,IACA;YACI,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;SACtC;QACD,OAAO,GAAG,EACV;YACI,OAAO,GAAG,GAAG,CAAC,QAAQ,CAAC;SAC1B;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAEa,6BAAmB,GAAjC,UAAmC,GAAW;QAE1C,IAAI,MAAM,GAAW,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACvC,IAAI,MAAM,GAAa,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACzC,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IACrC,CAAC;IAEa,4BAAkB,GAAhC,UAAkC,OAAe,EAAE,MAAc;QAE7D,IAAI,aAAa,GAAW,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;QAC9D,IAAI,OAAO,GAA0B,EAA2B,CAAC;QACjE,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC;QACtB,OAAO,CAAC,IAAI,GAAM,aAAa,iBAAY,MAAQ,CAAC;QACpD,OAAO,OAAO,CAAC;IACnB,CAAC;IArGc,oBAAU,GAAY,IAAI,CAAC;IAsG9C,gBAAC;CAxGD,AAwGC,IAAA;AAxGY,8BAAS", "file": "", "sourceRoot": "/", "sourcesContent": ["import { IHttpMsgBody } from \"./IHttpMsgBody\";\r\n\r\nexport class HttpUtils\r\n{\r\n    private static _isShowLog: boolean = true;\r\n    public static SyncGet<T>(address: string, params?: {}): Promise<T> {\r\n        return new Promise((resolve, reject) => {\r\n            let xhr: XMLHttpRequest = new XMLHttpRequest();\r\n            let url: string = address + this.ParamsToString(params);\r\n            xhr.open(\"GET\", url, true); // 异步模式\r\n\r\n            let interfaceName: string = this.GetUrlInterfaceName(address);\r\n            this._isShowLog && console.log(interfaceName + \" url: \", url);\r\n\r\n            xhr.onreadystatechange = () => {\r\n                if (xhr.readyState === 4) { // 请求完成\r\n                    if ((xhr.status >= 200 && xhr.status < 300) || xhr.status === 304) {\r\n                        const data: T = this.GetHttpData(xhr) as T;\r\n                        this._isShowLog && console.log(interfaceName, data);\r\n                        resolve(data);\r\n                    } else {\r\n                        const errorData: T = this.CreateGetErrorData(address, xhr.status) as T;\r\n                        this._isShowLog && console.log(interfaceName + \" 错误\", errorData);\r\n                        reject(errorData);\r\n                    }\r\n                }\r\n            };\r\n\r\n            xhr.send();\r\n        });\r\n    }\r\n\r\n    public static Get<T>(address: string, params?: {}, cb?: (value: T) => void): void\r\n    {\r\n        let interfaceName: string = this.GetUrlInterfaceName(address);\r\n        \r\n        let xhr: XMLHttpRequest = new XMLHttpRequest();\r\n        xhr.onreadystatechange = (ev: Event) =>\r\n        {\r\n            if (xhr.readyState == 4)\r\n            {\r\n                let data: T = null;\r\n                if ((xhr.status >= 200 && xhr.status < 300) || xhr.status == 304)\r\n                {\r\n                    data = this.GetHttpData(xhr) as T;\r\n                }\r\n                else\r\n                {\r\n                    data = this.CreateGetErrorData(address, xhr.status) as T;\r\n                }\r\n                this._isShowLog && console.log(interfaceName, data);\r\n                cb && cb(data);\r\n            }\r\n        }\r\n\r\n        let url: string = address + this.ParamsToString(params);\r\n        xhr.open(\"GET\", url);\r\n        xhr.send();\r\n        this._isShowLog && console.log(interfaceName + \" url: \", url);\r\n    }\r\n\r\n    public static ParamsToString (params: any): string\r\n    {\r\n        let arr: string[] = [];\r\n        for (let key in params)\r\n        {\r\n            arr.push(key + \"=\" + params[key]);\r\n        }\r\n        if (arr.length > 0)\r\n        {\r\n            return `?${arr.join(\"&\")}`;\r\n        }\r\n        \r\n        return \"\";\r\n    }\r\n\r\n    public static GetHttpData(xhr: XMLHttpRequest): any\r\n    {\r\n        let outData: any = {};\r\n        try\r\n        {\r\n            outData = JSON.parse(xhr.response);\r\n        }\r\n        catch (err)\r\n        {\r\n            outData = xhr.response;\r\n        }\r\n\r\n        return outData;\r\n    }\r\n\r\n    public static GetUrlInterfaceName (url: string): string\r\n    {\r\n        let adress: string = url.split(\"?\")[0];\r\n        let strArr: string[] = adress.split(\"/\");\r\n        return strArr[strArr.length - 1];\r\n    }\r\n\r\n    public static CreateGetErrorData (address: string, status: number): IHttpMsgBody.IResBase\r\n    {\r\n        let interfaceName: string = this.GetUrlInterfaceName(address);\r\n        let resData: IHttpMsgBody.IResBase = {} as IHttpMsgBody.IResBase;\r\n        resData.code = status;\r\n        resData.docs = `${interfaceName} status: ${status}`;\r\n        return resData;\r\n    }\r\n}"]}