{"version": 3, "sources": ["assets/scripts/net/IHttpMsgBody.ts"], "names": [], "mappings": "", "file": "", "sourceRoot": "/", "sourcesContent": ["export namespace IHttpMsgBody\r\n{\r\n    export interface IResBase\r\n    {\r\n        code: number,\r\n        docs: string,\r\n    }\r\n\r\n    export interface IResGetGameAddress extends IResBase\r\n    {\r\n        data: IGetAddressData\r\n    }\r\n\r\n    interface IGetAddressData \r\n    {\r\n        http_addr: string,\r\n        ws_addr: string,\r\n    }\r\n\r\n    export interface IReqGetGameAddress\r\n    {\r\n        user_id: string,\r\n        app_id: string,\r\n        app_channel: string,\r\n        game_id: string,\r\n        game_mode: string,\r\n        room_id: string\r\n    }\r\n\r\n    export interface IReqUpdateTime extends IReqGetGameAddress\r\n    {\r\n        curr_http_addr: string,\r\n        curr_ws_addr: string\r\n    }\r\n}"]}