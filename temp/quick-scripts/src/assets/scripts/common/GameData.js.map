{"version": 3, "sources": ["assets/scripts/common/GameData.ts"], "names": [], "mappings": ";;;;;AAAA;;;GAGG;;;;;;;;;;;;;;;;AAEH,uDAAsD;AACtD,uDAAsD;AACtD,sDAAqD;AAErD;IAA8B,4BAAS;IAAvC;QAAA,qEAiCC;QA/BU,mBAAa,GAAY,IAAI,CAAC,CAAA,6BAA6B;;IA+BtE,CAAC;IAhBG,sBAAW,+BAAS;QAbpB,SAAS;QACT,gBAAgB;QAChB,eAAe;QACf,qBAAqB;QACrB,cAAc;QACd,qEAAqE;QACrE,mBAAmB;QACnB,oBAAoB;QACpB,kBAAkB;QAClB,UAAU;aAIV;YAEI,IAAI,GAAG,GAAU,6BAAa,CAAC,EAAE;gBACb,OAAO,GAAC,qBAAS,CAAC,OAAO,CAAC,MAAM,EAAE;gBAClC,WAAW,GAAG,qBAAS,CAAC,OAAO,CAAC,UAAU,EAAE;gBAC5C,WAAW,GAAC,qBAAS,CAAC,OAAO,CAAC,SAAS,EAAE;gBACzC,aAAa,GAAC,qBAAS,CAAC,OAAO,CAAC,QAAQ;gBACxC,WAAW,GAAC,qBAAS,CAAC,OAAO,CAAC,MAAM;gBACpC,WAAW,GAAC,qBAAS,CAAC,OAAO,CAAC,QAAQ;gBACtC,QAAQ,GAAC,qBAAS,CAAC,OAAO,CAAC,IAAI;gBAC/B,UAAU,GAAC,qBAAS,CAAC,OAAO,CAAC,KAAK;gBAClC,eAAe,GAAC,qBAAS,CAAC,OAAO,CAAC,UAAU;gBAC5C,WAAW,GAAC,qBAAS,CAAC,OAAO,CAAC,MAAM,CAAC;YAEzD,OAAO,GAAG,CAAC;QACf,CAAC;;;OAAA;IACL,eAAC;AAAD,CAjCA,AAiCC,CAjC6B,qBAAS,GAiCtC;AAjCY,4BAAQ", "file": "", "sourceRoot": "/", "sourcesContent": ["/*\r\n * @Author: <PERSON>\r\n * @Date: 2023-03-15 11:47:21\r\n */\r\n\r\nimport { MeshTools } from \"../../meshTools/MeshTools\";\r\nimport { Singleton } from \"../../meshTools/Singleton\";\r\nimport { GameServerUrl } from \"../net/GameServerUrl\";\r\n\r\nexport class GameData extends Singleton\r\n{\r\n    public GameIsInFront: boolean = true;//true 游戏中没有压后台  false 压后台了  \r\n\r\n    // ?env=0\r\n    // &game_id=1051\r\n    // &game_mode=3\r\n    // &user_id=667055436\r\n    // &country=EN\r\n    // &code=RW0WXfzGQmTw8gqDkIg5LfUm0UBGN6UTFAynqhENZjVgehdufnSlQhmseHHW\r\n    // &app_id=66666666\r\n    // &app_channel=mesh\r\n    // &room_id=room01\r\n    // &role=1\r\n\r\n\r\n\r\n    public get ServerUrl (): string\r\n    {\r\n        let url :string= GameServerUrl.Ws+\r\n                            '?env='+MeshTools.Publish.getEnv()+\r\n                            '&version=' + MeshTools.Publish.getVersion() +\r\n                            '&game_id='+MeshTools.Publish.getGameId()+\r\n                            '&game_mode='+MeshTools.Publish.gameMode+\r\n                            '&user_id='+MeshTools.Publish.userId+\r\n                            '&country='+MeshTools.Publish.language+\r\n                            '&code='+MeshTools.Publish.code+\r\n                            '&app_id='+MeshTools.Publish.appId+\r\n                            '&app_channel='+MeshTools.Publish.appChannel+\r\n                            '&room_id='+MeshTools.Publish.roomId;\r\n                            \r\n        return url;\r\n    }\r\n}"]}