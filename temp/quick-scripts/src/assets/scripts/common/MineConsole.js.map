{"version": 3, "sources": ["assets/scripts/common/MineConsole.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,uDAAsD;AAItD;IAAyC,+BAAS;IAAlD;QAAA,qEAsGC;QApGU,mBAAa,GAAY,IAAI,CAAC;;IAoGzC,CAAC;IAlGW,8BAAQ,GAAhB,UAAkB,QAA2B;QAA3B,yBAAA,EAAA,eAA2B;QAEzC,IAAI,IAAI,CAAC,aAAa,EACtB;YACI,QAAQ,IAAI,QAAQ,EAAE,CAAC;SAC1B;IACL,CAAC;IAEM,yBAAG,GAAV;QAAY,cAAc;aAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;YAAd,yBAAc;;QAEtB,IAAI,CAAC,QAAQ,CAAC;YAEV,OAAO,CAAC,GAAG,OAAX,OAAO,EAAQ,IAAI,EAAE;QACzB,CAAC,CAAC,CAAC;IACP,CAAC;IAEM,0BAAI,GAAX;QAAa,cAAc;aAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;YAAd,yBAAc;;QAEvB,IAAI,CAAC,QAAQ,CAAC;YAEV,OAAO,CAAC,IAAI,OAAZ,OAAO,EAAS,IAAI,EAAE;QAC1B,CAAC,CAAC,CAAC;IACP,CAAC;IAEM,0BAAI,GAAX;QAEI,sBAAsB;QACtB,IAAI;QACJ,6BAA6B;QAC7B,MAAM;QALG,cAAc;aAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;YAAd,yBAAc;;QAOvB,OAAO,CAAC,IAAI,OAAZ,OAAO,EAAS,IAAI,EAAE;IAC1B,CAAC;IAEM,2BAAK,GAAZ;QAEI,sBAAsB;QACtB,IAAI;QACJ,8BAA8B;QAC9B,MAAM;QALI,cAAc;aAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;YAAd,yBAAc;;QAOxB,OAAO,CAAC,KAAK,OAAb,OAAO,EAAU,IAAI,EAAE;IAC3B,CAAC;IAEM,2BAAK,GAAZ,UAAc,QAAgB;QAAE,cAAc;aAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;YAAd,6BAAc;;QAE1C,IAAI,CAAC,QAAQ,CAAC;YAEV,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YACxB,OAAO,CAAC,GAAG,OAAX,OAAO,EAAQ,IAAI,EAAE;YACrB,OAAO,CAAC,QAAQ,EAAE,CAAC;QACvB,CAAC,CAAC,CAAC;IACP,CAAC;IAEM,+BAAS,GAAhB,UAAkB,GAAW,EAAE,KAAgC;QAAhC,sBAAA,EAAA,QAAkB,EAAE,CAAC,KAAK,CAAC,KAAK;QAE3D,IAAI,CAAC,QAAQ,CAAC;YAEV,IAAI,KAAK,GAAW,oBAAoB,GAAG,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;YAClE,EAAE,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,GAAG,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACzE,CAAC,CAAC,CAAC;IACP,CAAC;IAEM,2BAAK,GAAZ,UAAc,IAAQ;QAElB,IAAI,CAAC,QAAQ,CAAC;YAEV,EAAE,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;IACP,CAAC;IAEM,4BAAM,GAAb,UAAe,iBAA0B;QAAE,cAAc;aAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;YAAd,6BAAc;;QAErD,IAAI,CAAC,QAAQ,CAAC;YAEV,OAAO,CAAC,MAAM,OAAd,OAAO,kBAAQ,iBAAiB,GAAK,IAAI,GAAE;QAC/C,CAAC,CAAC,CAAC;IACP,CAAC;IAEM,0BAAI,GAAX,UAAa,OAAe,EAAE,IAAmC;QAAnC,qBAAA,EAAA,WAAmC;QAAE,gBAAc;aAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;YAAd,+BAAc;;QAE7E,IAAI,CAAC,QAAQ,CAAC;YAEV,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACtB,IAAI,IAAI,IAAI,eAAI,MAAM,CAAC,CAAC;YACxB,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;IACP,CAAC;IAEM,kCAAY,GAAnB,UAAqB,GAAW;QAAhC,iBAQC;QANG,IAAI,IAAI,GAAW,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC;QACxC,OAAO;YAEH,IAAI,OAAO,GAAW,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC;YAC3C,KAAI,CAAC,GAAG,CAAC,GAAG,GAAG,SAAS,GAAG,CAAC,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;QAChE,CAAC,CAAA;IACL,CAAC;IACL,kBAAC;AAAD,CAtGA,AAsGC,CAtGwC,qBAAS,GAsGjD", "file": "", "sourceRoot": "/", "sourcesContent": ["import { Singleton } from \"../../meshTools/Singleton\";\r\n\r\n\r\n\r\nexport default class MineConsole extends Singleton\r\n{\r\n    public IsShowConsole: boolean = true;\r\n\r\n    private showInfo (callback: () => void = null): void\r\n    {\r\n        if (this.IsShowConsole)\r\n        {\r\n            callback && callback();\r\n        }\r\n    }\r\n\r\n    public Log (...data: any[]): void\r\n    {\r\n        this.showInfo(() =>\r\n        {\r\n            console.log(...data);\r\n        });\r\n    }\r\n\r\n    public Info (...data: any[]): void\r\n    {\r\n        this.showInfo(() =>\r\n        {\r\n            console.info(...data);\r\n        });\r\n    }\r\n\r\n    public Warn (...data: any[]): void\r\n    {\r\n        // this.showInfo(() =>\r\n        // {\r\n        //     console.warn(...data);\r\n        // });\r\n        \r\n        console.warn(...data);\r\n    }\r\n\r\n    public Error (...data: any[]): void\r\n    {\r\n        // this.showInfo(() =>\r\n        // {\r\n        //     console.error(...data);\r\n        // });\r\n\r\n        console.error(...data);\r\n    }\r\n\r\n    public Group (groupKey: string, ...data: any[]): void\r\n    {\r\n        this.showInfo(() =>\r\n        {\r\n            console.group(groupKey);\r\n            console.log(...data);\r\n            console.groupEnd();\r\n        });\r\n    }\r\n\r\n    public LogString (str: string, color: cc.Color = cc.Color.WHITE): void\r\n    {\r\n        this.showInfo(() =>\r\n        {\r\n            let style: string = \"background-color:#\" + color.toHEX(\"#rrggbb\");\r\n            cc.sys.isBrowser ? console.log(\"%c\" + str, style) : console.log(str);\r\n        });\r\n    }\r\n\r\n    public Table (data: {}): void\r\n    {\r\n        this.showInfo(() =>\r\n        {\r\n            cc.sys.isBrowser ? console.table(data) : console.log(data);\r\n        });\r\n    }\r\n\r\n    public Assert (correctConditions: boolean, ...data: any[]): void\r\n    {\r\n        this.showInfo(() =>\r\n        {\r\n            console.assert(correctConditions, ...data);\r\n        });\r\n    }\r\n\r\n    public Time (funcKey: string, func: (...data: any) => void = null, ...params: any): void\r\n    {\r\n        this.showInfo(() =>\r\n        {\r\n            console.time(funcKey);\r\n            func && func(...params);\r\n            console.timeEnd(funcKey);\r\n        });\r\n    }\r\n\r\n    public TimeInterval (key: string): () => void\r\n    {\r\n        let time: number = new Date().getTime();\r\n        return () =>\r\n        {\r\n            let nowTime: number = new Date().getTime();\r\n            this.Log(key + \" time: \" + ((nowTime - time) / 1000) + \"s\");\r\n        }\r\n    }\r\n}"]}