"use strict";
cc._RF.push(module, 'fe2d52E2MxMKarwZe5xDbAh', 'GameTools');
// scripts/common/GameTools.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.GameTools = void 0;
var Singleton_1 = require("../../meshTools/Singleton");
var GameTools = /** @class */ (function (_super) {
    __extends(GameTools, _super);
    function GameTools() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    GameTools.prototype.GetUrlParams = function (url) {
        var urlArr = url.split("?");
        var data = {};
        if (urlArr.length === 1)
            return data;
        for (var i = 1; i <= urlArr.length - 1; i++) {
            var paramsStr = decodeURIComponent(urlArr[i]);
            if (paramsStr && paramsStr !== 'undefined') {
                var paramsArr = paramsStr.split("&");
                paramsArr.forEach(function (str) {
                    var _a = str.split("="), key = _a[0], rest = _a.slice(1);
                    var value = rest.join("=");
                    if (key)
                        data[key] = value;
                });
            }
        }
        return data;
    };
    return GameTools;
}(Singleton_1.Singleton));
exports.GameTools = GameTools;

cc._RF.pop();