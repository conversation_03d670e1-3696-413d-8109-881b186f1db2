{"version": 3, "sources": ["assets/scripts/common/GameMgr.ts"], "names": [], "mappings": ";;;;;;;AAGA,+DAA0D;AAC1D,6CAA4C;AAC5C,uCAAsC;AACtC,yCAAwC;AACxC,6CAAwC;AAKxC;IAAA;IAuBA,CAAC;IApBG,sBAAkB,gBAAK;aAAvB;YACI,IAAI,OAAO,CAAC,MAAM,IAAI,IAAI,EAAE;gBACxB,OAAO,CAAC,MAAM,GAAG,IAAI,oBAAU,EAAE,CAAC;aACrC;YAED,OAAO,OAAO,CAAC,MAAM,CAAC;QAC1B,CAAC;;;OAAA;IAED,sBAAkB,mBAAQ;aAA1B;YACI,OAAO,mBAAQ,CAAC,WAAW,EAAE,CAAC;QAClC,CAAC;;;OAAA;IACD,sBAAkB,gBAAK;aAAvB;YACI,OAAO,yBAAW,CAAC,WAAW,EAAE,CAAC;QACrC,CAAC;;;OAAA;IACD,sBAAkB,gBAAK;aAAvB;YACI,OAAO,qBAAS,CAAC,WAAW,EAAE,CAAC;QACnC,CAAC;;;OAAA;IACD,sBAAkB,kBAAO;aAAzB;YACI,OAAO,qBAAW,CAAC,WAAW,EAAE,CAAC;QACrC,CAAC;;;OAAA;IArBc,cAAM,GAAY,IAAI,CAAC;IAsB1C,cAAC;CAvBD,AAuBC,IAAA;AAvBY,0BAAO", "file": "", "sourceRoot": "/", "sourcesContent": ["\r\n\r\nimport { BaseSDK } from \"../../meshTools/BaseSDK\";\r\nimport MeshSdkApi from \"../../meshTools/tools/MeshSdkApi\";\r\nimport { EventCenter } from \"./EventCenter\";\r\nimport { GameData } from \"./GameData\";\r\nimport { GameTools } from \"./GameTools\";\r\nimport MineConsole from \"./MineConsole\";\r\n\r\n\r\n\r\n\r\nexport class GameMgr {\r\n    private static _h5SDK: BaseSDK = null;\r\n\r\n    public static get H5SDK(): BaseSDK {\r\n        if (GameMgr._h5SDK == null) {\r\n            GameMgr._h5SDK = new MeshSdkApi();\r\n        }\r\n\r\n        return GameMgr._h5SDK;\r\n    }\r\n\r\n    public static get GameData(): GameData {\r\n        return GameData.GetInstance();\r\n    }\r\n    public static get Event(): EventCenter {\r\n        return EventCenter.GetInstance();\r\n    }\r\n    public static get Utils(): GameTools {\r\n        return GameTools.GetInstance();\r\n    }\r\n    public static get Console(): MineConsole {\r\n        return MineConsole.GetInstance();\r\n    }\r\n}"]}