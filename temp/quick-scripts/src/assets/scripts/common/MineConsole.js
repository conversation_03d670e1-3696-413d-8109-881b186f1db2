"use strict";
cc._RF.push(module, 'b7ed6JVjjlO9KYRYoBoofK2', 'MineConsole');
// scripts/common/MineConsole.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __spreadArrays = (this && this.__spreadArrays) || function () {
    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;
    for (var r = Array(s), k = 0, i = 0; i < il; i++)
        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)
            r[k] = a[j];
    return r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Singleton_1 = require("../../meshTools/Singleton");
var MineConsole = /** @class */ (function (_super) {
    __extends(MineConsole, _super);
    function MineConsole() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.IsShowConsole = true;
        return _this;
    }
    MineConsole.prototype.showInfo = function (callback) {
        if (callback === void 0) { callback = null; }
        if (this.IsShowConsole) {
            callback && callback();
        }
    };
    MineConsole.prototype.Log = function () {
        var data = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            data[_i] = arguments[_i];
        }
        this.showInfo(function () {
            console.log.apply(console, data);
        });
    };
    MineConsole.prototype.Info = function () {
        var data = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            data[_i] = arguments[_i];
        }
        this.showInfo(function () {
            console.info.apply(console, data);
        });
    };
    MineConsole.prototype.Warn = function () {
        // this.showInfo(() =>
        // {
        //     console.warn(...data);
        // });
        var data = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            data[_i] = arguments[_i];
        }
        console.warn.apply(console, data);
    };
    MineConsole.prototype.Error = function () {
        // this.showInfo(() =>
        // {
        //     console.error(...data);
        // });
        var data = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            data[_i] = arguments[_i];
        }
        console.error.apply(console, data);
    };
    MineConsole.prototype.Group = function (groupKey) {
        var data = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            data[_i - 1] = arguments[_i];
        }
        this.showInfo(function () {
            console.group(groupKey);
            console.log.apply(console, data);
            console.groupEnd();
        });
    };
    MineConsole.prototype.LogString = function (str, color) {
        if (color === void 0) { color = cc.Color.WHITE; }
        this.showInfo(function () {
            var style = "background-color:#" + color.toHEX("#rrggbb");
            cc.sys.isBrowser ? console.log("%c" + str, style) : console.log(str);
        });
    };
    MineConsole.prototype.Table = function (data) {
        this.showInfo(function () {
            cc.sys.isBrowser ? console.table(data) : console.log(data);
        });
    };
    MineConsole.prototype.Assert = function (correctConditions) {
        var data = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            data[_i - 1] = arguments[_i];
        }
        this.showInfo(function () {
            console.assert.apply(console, __spreadArrays([correctConditions], data));
        });
    };
    MineConsole.prototype.Time = function (funcKey, func) {
        if (func === void 0) { func = null; }
        var params = [];
        for (var _i = 2; _i < arguments.length; _i++) {
            params[_i - 2] = arguments[_i];
        }
        this.showInfo(function () {
            console.time(funcKey);
            func && func.apply(void 0, params);
            console.timeEnd(funcKey);
        });
    };
    MineConsole.prototype.TimeInterval = function (key) {
        var _this = this;
        var time = new Date().getTime();
        return function () {
            var nowTime = new Date().getTime();
            _this.Log(key + " time: " + ((nowTime - time) / 1000) + "s");
        };
    };
    return MineConsole;
}(Singleton_1.Singleton));
exports.default = MineConsole;

cc._RF.pop();