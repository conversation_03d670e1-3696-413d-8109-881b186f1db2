"use strict";
cc._RF.push(module, '50d67lS8N5Ehr/tRCitXjWZ', 'GameData');
// scripts/common/GameData.ts

"use strict";
/*
 * @Author: <PERSON>
 * @Date: 2023-03-15 11:47:21
 */
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.GameData = void 0;
var MeshTools_1 = require("../../meshTools/MeshTools");
var Singleton_1 = require("../../meshTools/Singleton");
var GameServerUrl_1 = require("../net/GameServerUrl");
var GameData = /** @class */ (function (_super) {
    __extends(GameData, _super);
    function GameData() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.GameIsInFront = true; //true 游戏中没有压后台  false 压后台了  
        return _this;
    }
    Object.defineProperty(GameData.prototype, "ServerUrl", {
        // ?env=0
        // &game_id=1051
        // &game_mode=3
        // &user_id=667055436
        // &country=EN
        // &code=RW0WXfzGQmTw8gqDkIg5LfUm0UBGN6UTFAynqhENZjVgehdufnSlQhmseHHW
        // &app_id=66666666
        // &app_channel=mesh
        // &room_id=room01
        // &role=1
        get: function () {
            var url = GameServerUrl_1.GameServerUrl.Ws +
                '?env=' + MeshTools_1.MeshTools.Publish.getEnv() +
                '&version=' + MeshTools_1.MeshTools.Publish.getVersion() +
                '&game_id=' + MeshTools_1.MeshTools.Publish.getGameId() +
                '&game_mode=' + MeshTools_1.MeshTools.Publish.gameMode +
                '&user_id=' + MeshTools_1.MeshTools.Publish.userId +
                '&country=' + MeshTools_1.MeshTools.Publish.language +
                '&code=' + MeshTools_1.MeshTools.Publish.code +
                '&app_id=' + MeshTools_1.MeshTools.Publish.appId +
                '&app_channel=' + MeshTools_1.MeshTools.Publish.appChannel +
                '&room_id=' + MeshTools_1.MeshTools.Publish.roomId;
            return url;
        },
        enumerable: false,
        configurable: true
    });
    return GameData;
}(Singleton_1.Singleton));
exports.GameData = GameData;

cc._RF.pop();