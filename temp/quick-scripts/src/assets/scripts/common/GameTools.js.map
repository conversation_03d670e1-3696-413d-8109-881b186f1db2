{"version": 3, "sources": ["assets/scripts/common/GameTools.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA,uDAAsD;AAItD;IAA+B,6BAAS;IAAxC;;IAqBA,CAAC;IAlBU,gCAAY,GAAnB,UAAoB,GAAW;QAC3B,IAAI,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC5B,IAAI,IAAI,GAAG,EAAE,CAAC;QACd,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,IAAI,CAAC;QACrC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YACzC,IAAI,SAAS,GAAG,kBAAkB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9C,IAAI,SAAS,IAAI,SAAS,KAAK,WAAW,EAAE;gBACxC,IAAI,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBACrC,SAAS,CAAC,OAAO,CAAC,UAAC,GAAG;oBACd,IAAA,KAAiB,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,EAA9B,GAAG,QAAA,EAAK,IAAI,cAAkB,CAAC;oBACpC,IAAI,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;oBAC3B,IAAI,GAAG;wBAAE,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;gBAC/B,CAAC,CAAC,CAAC;aACN;SACJ;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEL,gBAAC;AAAD,CArBA,AAqBC,CArB8B,qBAAS,GAqBvC;AArBY,8BAAS", "file": "", "sourceRoot": "/", "sourcesContent": ["import { Singleton } from \"../../meshTools/Singleton\";\r\n\r\n\r\n\r\nexport class GameTools extends Singleton\r\n{\r\n\r\n    public GetUrlParams(url: string): {} {\r\n        let urlArr = url.split(\"?\");\r\n        let data = {};\r\n        if (urlArr.length === 1) return data;\r\n        for (let i = 1; i <= urlArr.length - 1; i++) {\r\n            let paramsStr = decodeURIComponent(urlArr[i]);\r\n            if (paramsStr && paramsStr !== 'undefined') {\r\n                let paramsArr = paramsStr.split(\"&\");\r\n                paramsArr.forEach((str) => {\r\n                    let [key, ...rest] = str.split(\"=\");\r\n                    let value = rest.join(\"=\");\r\n                    if (key) data[key] = value;\r\n                });\r\n            }\r\n        }\r\n        return data;\r\n    }\r\n\r\n}\r\n"]}