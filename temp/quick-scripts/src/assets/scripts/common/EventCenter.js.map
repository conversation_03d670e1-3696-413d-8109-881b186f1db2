{"version": 3, "sources": ["assets/scripts/common/EventCenter.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA,uDAAsD;AACtD,qCAAoC;AAcpC;IAAiC,+BAAS;IAA1C;QAAA,qEAsDC;QApDW,aAAO,GAAW,EAAY,CAAC;;IAoD3C,CAAC;IAlDU,sCAAgB,GAAvB,UAAyB,SAA0B,EAAE,EAAY,EAAE,MAAY;QAE3E,iBAAO,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,GAAC,SAAS,CAAC,CAAC;QACvC,SAAS,GAAG,SAAS,CAAC,QAAQ,EAAE,CAAC;QACjC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,EAC5B;YACI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;SAChC;QAED,IAAI,SAAS,GAAe,EAAgB,CAAC;QAC7C,SAAS,CAAC,EAAE,GAAG,EAAE,CAAC;QAClB,SAAS,CAAC,MAAM,GAAG,MAAM,CAAC;QAC1B,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAC5C,CAAC;IAEM,yCAAmB,GAA1B,UAA4B,SAA0B,EAAE,EAAY,EAAE,MAAY;;QAE9E,iBAAO,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,GAAC,SAAS,CAAC,CAAC;QACvC,SAAS,GAAG,SAAS,CAAC,QAAQ,EAAE,CAAC;QACjC,IAAI,IAAI,SAAiB,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,mCAAI,EAAE,CAAC;QACvD,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,EACpB;YACI,OAAO;SACV;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,EACpC;YACI,IAAI,KAAK,GAAe,IAAI,CAAC,CAAC,CAAC,CAAC;YAChC,IAAI,KAAK,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC,MAAM,CAAC,EAC3D;gBACI,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;aACrB;SACJ;IACL,CAAC;IAEM,0BAAI,GAAX,UAAa,SAA0B;;QAAE,gBAAc;aAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;YAAd,+BAAc;;QAEnD,SAAS,GAAG,SAAS,CAAC,QAAQ,EAAE,CAAC;QACjC,IAAI,IAAI,SAAiB,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,mCAAI,EAAE,CAAC;QACvD,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,EACpB;YACI,OAAO;SACV;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,EACpC;YACI,IAAI,KAAK,GAAe,IAAI,CAAC,CAAC,CAAC,CAAC;YAChC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;SACxC;IACL,CAAC;IACL,kBAAC;AAAD,CAtDA,AAsDC,CAtDgC,qBAAS,GAsDzC;AAtDY,kCAAW;AAwDxB,SAAS;AACT,IAAY,SAIX;AAJD,WAAY,SAAS;IACjB,8CAAiC,CAAA;IACjC,wDAA2C,CAAA;IAC3C,wCAA2B,CAAA;AAC/B,CAAC,EAJW,SAAS,GAAT,iBAAS,KAAT,iBAAS,QAIpB", "file": "", "sourceRoot": "/", "sourcesContent": ["import { Singleton } from \"../../meshTools/Singleton\";\r\nimport { GameMgr } from \"./GameMgr\";\r\n\r\n\r\ninterface IEventData\r\n{\r\n    cb: Function;\r\n    target: any\r\n}\r\n\r\ninterface IEvent\r\n{\r\n    [eventName: string]: IEventData[]\r\n}\r\n\r\nexport class EventCenter extends Singleton\r\n{\r\n    private _handle: IEvent = {} as IEvent;\r\n    \r\n    public AddEventListener (eventName: string | number, cb: Function, target?: any): void\r\n    {\r\n        GameMgr.Console.Log('添加监听：'+eventName);\r\n        eventName = eventName.toString();\r\n        if (!this._handle[eventName])\r\n        {\r\n            this._handle[eventName] = [];\r\n        }\r\n\r\n        let eventData: IEventData = {} as IEventData;\r\n        eventData.cb = cb;\r\n        eventData.target = target;\r\n        this._handle[eventName].push(eventData);\r\n    }\r\n\r\n    public RemoveEventListener (eventName: string | number, cb: Function, target?: any): void\r\n    {\r\n        GameMgr.Console.Log('移除监听：'+eventName);\r\n        eventName = eventName.toString();\r\n        let list: IEventData[] = this._handle[eventName] ?? [];\r\n        if (list.length <= 0)\r\n        {\r\n            return;\r\n        }\r\n\r\n        for (let i = 0; i < list.length; ++i)\r\n        {\r\n            let event: IEventData = list[i];\r\n            if (event.cb === cb && (!target || target === event.target))\r\n            {\r\n                list.splice(i, 1);\r\n            }\r\n        }\r\n    }\r\n\r\n    public Send (eventName: string | number, ...params: any): void\r\n    {\r\n        eventName = eventName.toString();\r\n        let list: IEventData[] = this._handle[eventName] ?? [];\r\n        if (list.length <= 0)\r\n        {\r\n            return;\r\n        }\r\n\r\n        for (let i = 0; i < list.length; ++i)\r\n        {\r\n            let event: IEventData = list[i];\r\n            event.cb.apply(event.target, params);\r\n        }\r\n    }\r\n}\r\n\r\n//通知的消息类型\r\nexport enum EventType{\r\n    ReceiveMessage = 'receiveMessage',//收到长链接消息的通知\r\n    ReceiveErrorMessage = 'receiveErrorMessage',//收到长链接消息的通知,错误 code 的消息\r\n    AutoMessage = 'autoMessage',// 程序内的通知\r\n}\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n"]}