"use strict";
cc._RF.push(module, '17fd4BJ6+1INYp143EG7XAk', 'GameMgr');
// scripts/common/GameMgr.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GameMgr = void 0;
var MeshSdkApi_1 = require("../../meshTools/tools/MeshSdkApi");
var EventCenter_1 = require("./EventCenter");
var GameData_1 = require("./GameData");
var GameTools_1 = require("./GameTools");
var MineConsole_1 = require("./MineConsole");
var GameMgr = /** @class */ (function () {
    function GameMgr() {
    }
    Object.defineProperty(GameMgr, "H5SDK", {
        get: function () {
            if (GameMgr._h5SDK == null) {
                GameMgr._h5SDK = new MeshSdkApi_1.default();
            }
            return GameMgr._h5SDK;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(GameMgr, "GameData", {
        get: function () {
            return GameData_1.GameData.GetInstance();
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(GameMgr, "Event", {
        get: function () {
            return EventCenter_1.EventCenter.GetInstance();
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(GameMgr, "Utils", {
        get: function () {
            return GameTools_1.GameTools.GetInstance();
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(GameMgr, "Console", {
        get: function () {
            return MineConsole_1.default.GetInstance();
        },
        enumerable: false,
        configurable: true
    });
    GameMgr._h5SDK = null;
    return GameMgr;
}());
exports.GameMgr = GameMgr;

cc._RF.pop();