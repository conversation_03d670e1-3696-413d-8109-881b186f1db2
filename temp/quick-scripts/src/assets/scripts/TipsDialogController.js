"use strict";
cc._RF.push(module, 'e3a65XdwbpBvLD8jdftAyyv', 'TipsDialogController');
// scripts/TipsDialogController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Config_1 = require("./util/Config");
var Tools_1 = require("./util/Tools");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
//这个是只有一个退出按钮的 弹窗
var TipsDialogController = /** @class */ (function (_super) {
    __extends(TipsDialogController, _super);
    function TipsDialogController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.boardBg = null;
        _this.content = null; //文案
        _this.leaveButton = null; //退出按钮
        return _this;
        // update (dt) {}
    }
    // onLoad () {}
    TipsDialogController.prototype.start = function () {
        var _this = this;
        Tools_1.Tools.redButton(this.leaveButton, function () {
            if (_this.callback) {
                _this.callback();
            }
        });
    };
    TipsDialogController.prototype.showDialog = function (content, callback) {
        this.callback = callback;
        this.content.string = content;
        if (this.node.active == false) {
            this.node.active = true;
            this.boardBg.scale = 0;
            // 执行缩放动画
            cc.tween(this.boardBg)
                .to(Config_1.Config.dialogScaleTime, { scale: 1 })
                .start();
        }
    };
    __decorate([
        property(cc.Node)
    ], TipsDialogController.prototype, "boardBg", void 0);
    __decorate([
        property(cc.Label)
    ], TipsDialogController.prototype, "content", void 0);
    __decorate([
        property(cc.Node)
    ], TipsDialogController.prototype, "leaveButton", void 0);
    TipsDialogController = __decorate([
        ccclass
    ], TipsDialogController);
    return TipsDialogController;
}(cc.Component));
exports.default = TipsDialogController;

cc._RF.pop();