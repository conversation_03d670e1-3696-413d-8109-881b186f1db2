{"version": 3, "sources": ["assets/scripts/util/Config.ts"], "names": [], "mappings": ";;;;;;;AACA;IAAA;IAqCA,CAAC;IAnCG,cAAc;IACP,gBAAS,GAAG,aAAa,CAAC,CAAC,SAAS;IACpC,cAAO,GAAG,WAAW,CAAC,CAAC,UAAU;IACjC,cAAO,GAAG,WAAW,CAAC,CAAC,WAAW;IAClC,YAAK,GAAG,QAAQ,CAAC,CAAC,UAAU;IAGnC,MAAM;IACC,mBAAY,GAAG,MAAM,CAAC,SAAS,GAAG,gBAAgB,CAAA;IAClD,oBAAa,GAAG,MAAM,CAAC,SAAS,GAAG,iBAAiB,CAAA;IACpD,wBAAiB,GAAG,SAAS,CAAA;IAC7B,yBAAkB,GAAG,SAAS,CAAA;IAE9B,qBAAc,GAAG,MAAM,CAAC,SAAS,GAAG,kBAAkB,CAAA;IACtD,sBAAe,GAAG,MAAM,CAAC,SAAS,GAAG,mBAAmB,CAAA;IACxD,0BAAmB,GAAG,SAAS,CAAA;IAC/B,2BAAoB,GAAG,SAAS,CAAA;IAEhC,sBAAe,GAAG,MAAM,CAAC,SAAS,GAAG,mBAAmB,CAAA;IACxD,uBAAgB,GAAG,MAAM,CAAC,SAAS,GAAG,oBAAoB,CAAA;IAC1D,2BAAoB,GAAG,SAAS,CAAA;IAChC,4BAAqB,GAAG,SAAS,CAAA;IAEjC,oBAAa,GAAG,MAAM,CAAC,SAAS,GAAG,iBAAiB,CAAA;IACpD,yBAAkB,GAAG,SAAS,CAAA;IAErC,SAAS;IACF,sBAAe,GAAG,IAAI,CAAA,CAAC,SAAS;IAChC,oBAAa,GAAG,EAAE,CAAA,CAAC,MAAM;IACzB,qBAAc,GAAG,CAAC,CAAA,CAAC,SAAS;IAC5B,qBAAc,GAAG,CAAC,CAAA,CAAC,SAAS;IAE5B,cAAO,GAAG,GAAG,CAAA,CAAC,UAAU;IACxB,gCAAyB,GAAG,IAAI,CAAA,CAAC,eAAe;IAE3D,aAAC;CArCD,AAqCC,IAAA;AArCY,wBAAM", "file": "", "sourceRoot": "/", "sourcesContent": ["\nexport class Config{\n\n    //resources 相关\n    static buttonRes = 'button_res/'; //按钮相关的资源\n    static hallRes = 'hall_res/'; //大厅的资源文件夹\n    static gameRes = 'game_res/'; //游戏中的资源文件夹\n    static audio = 'audio/'; //游戏中的音乐资源\n\n\n    //按钮名称\n    static btnRedNormal = Config.buttonRes + 'btn_red_normal'\n    static btnRedPressed = Config.buttonRes + 'btn_red_pressed'\n    static btnRedNormalColor = `#BA3207`\n    static btnRedPressedColor = `#832305`\n\n    static btnGreenNormal = Config.buttonRes + 'btn_green_normal'\n    static btnGreenPressed = Config.buttonRes + 'btn_green_pressed'\n    static btnGreenNormalColor = `#119C0F`\n    static btnGreenPressedColor = `#0C6D0B`\n\n    static btnYellowNormal = Config.buttonRes + 'btn_yellow_normal'\n    static btnYellowPressed = Config.buttonRes + 'btn_yellow_pressed'\n    static btnYellowNormalColor = `#CC6519`\n    static btnYellowPressedColor = `#8F4611`\n\n    static btnGrayNormal = Config.buttonRes + 'btn_gray_normal'\n    static btnGrayNormalColor = `#7B7B7B`\n\n    //game 相关\n    static dialogScaleTime = 0.15 //弹窗的缩放时间\n    static latticeNumber = 63 //格子数量\n    static latticeNumberH = 7 //每一竖行格子数\n    static latticeNumberW = 9 //每一横行格子数\n\n    static flyTime = 0.3 //飞行动画时间 s\n    static huojianAndZhadanSkillTime = 0.65 //火箭和炸弹技能动画时间 s\n\n}"]}