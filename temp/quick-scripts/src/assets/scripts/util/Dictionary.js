"use strict";
cc._RF.push(module, '80e8491m9tLO5SusdDsOgKH', 'Dictionary');
// scripts/util/Dictionary.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Dictionary = void 0;
/**
 * 自定义字典类
 */
var Dictionary = /** @class */ (function () {
    function Dictionary() {
        this.keys = [];
        this.values = [];
    }
    /**
     * 获取所有值
     */
    Dictionary.prototype.getList = function () {
        var self = this;
        return self.values;
    };
    /**
     * 根据键得到值
     */
    Dictionary.prototype.getValue = function (key) {
        var self = this;
        var index = self.keys.indexOf(key);
        if (index != -1)
            return self.values[index];
        return null;
    };
    /**根据值得到键 */
    Dictionary.prototype.getKey = function (value) {
        var self = this;
        var index = self.values.indexOf(value);
        if (index != -1)
            return self.keys[index];
        return null;
    };
    /**改变值 */
    Dictionary.prototype.changeValue = function (key, changeValue) {
        var self = this;
        var index = self.keys.indexOf(key);
        if (index != -1)
            self.values[index] = changeValue;
    };
    /**改变键 */
    Dictionary.prototype.changeKey = function (key, changeKey) {
        var self = this;
        var index = self.keys.indexOf(key);
        if (index != -1)
            self.keys[index] = changeKey;
    };
    /** 根据键刷新值 */
    Dictionary.prototype.updateValue = function (key, value) {
        var self = this;
        var index = self.keys.indexOf(key);
        if (index != -1)
            self.values[index] = value;
        else
            self.add(key, value);
    };
    /**
     * 添加键值
     */
    Dictionary.prototype.add = function (key, value, name) {
        var self = this;
        if (self.keys.indexOf(key) != -1) {
            // console.log("same key in dic", name);
            return;
        }
        self.keys.push(key);
        self.values.push(value);
    };
    /**
     * 根据键添加值
     * type: 0:values是数组往里面添加值
     *       1:values是number,用来统计数量的
     */
    Dictionary.prototype.addValue = function (key, value, type) {
        if (type === void 0) { type = 0; }
        var self = this;
        var index = self.keys.indexOf(key);
        if (index < 0) {
            self.keys.push(key);
            self.addValue(key, value, type);
        }
        else {
            var values = self.getValue(key);
            if (type == 0) {
                if (values) {
                    values.push(value);
                }
                else {
                    values = [value];
                }
            }
            else {
                if (values) {
                    values += value;
                }
                else {
                    values = 1;
                }
            }
            self.changeValue(key, values);
        }
    };
    /**
     * 清空
     */
    Dictionary.prototype.clear = function () {
        var self = this;
        self.keys.length = 0;
        self.values.length = 0;
    };
    /**
     * 根据键移除对象
     */
    Dictionary.prototype.removeKey = function (key) {
        var self = this;
        var index = self.keys.indexOf(key);
        if (index < 0)
            return;
        self.keys.splice(index, 1);
        self.values.splice(index, 1);
    };
    /**
     * 根据值移除对象
     */
    Dictionary.prototype.removeValue = function (value) {
        var self = this;
        var index = self.values.indexOf(value);
        self.keys.splice(index, 1);
        self.values.splice(index, 1);
    };
    /**
     * 根据键检测是否存在对象
     */
    Dictionary.prototype.containsKey = function (key) {
        if (this.keys.indexOf(key, 0) == -1) {
            return false;
        }
        return true;
    };
    /**
     * 根据值检测是否存在对象
     */
    Dictionary.prototype.containsValue = function (value) {
        if (this.values.indexOf(value, 0) == -1) {
            return false;
        }
        return true;
    };
    /**突出最后一个对象 */
    Dictionary.prototype.pop = function () {
        var self = this;
        self.keys.pop();
        self.values.pop();
    };
    /**根据索引交换位置 */
    Dictionary.prototype.swap = function (num1, num2) {
        var self = this;
        if (self.keys.length <= num1 ||
            self.keys.length <= num2)
            return;
        //交换
        var tmpK = self.keys[num1];
        self.keys[num1] = self.keys[num2];
        self.keys[num2] = tmpK;
        var tmpV = self.values[num1];
        self.values[num1] = self.values[num2];
        self.values[num2] = tmpV;
    };
    /** 交换两个索引对应的值*/
    Dictionary.prototype.cutValue = function (num1, num2) {
        var self = this;
        if (self.keys.indexOf(num1) < -1 ||
            self.keys.indexOf(num1) < -2)
            return;
        var tmpV = self.getValue(num1);
        self.changeValue(num1, self.getValue(num2));
        self.changeValue(num2, tmpV);
    };
    Object.defineProperty(Dictionary.prototype, "size", {
        // public cutValue(num1:number, num2:number):void {
        //     let self = this;
        //     if( self.keys.length <= num1 ||
        //         self.keys.length <= num2  )
        //         return;
        //     let tmpV = self.values[num1];
        //     self.values[num1] = self.values[num2];
        //     self.values[num2] = tmpV;
        // }
        /**长度 */
        get: function () {
            return this.keys.length;
        },
        enumerable: false,
        configurable: true
    });
    return Dictionary;
}());
exports.Dictionary = Dictionary;

cc._RF.pop();