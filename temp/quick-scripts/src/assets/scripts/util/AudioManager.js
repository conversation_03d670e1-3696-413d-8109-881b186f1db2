"use strict";
cc._RF.push(module, '41fa63qXFBNjI/3M246s/5D', 'AudioManager');
// scripts/util/AudioManager.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AudioManager = void 0;
var AudioMgr_1 = require("./AudioMgr");
var LocalStorageManager_1 = require("./LocalStorageManager");
var AudioManager = /** @class */ (function () {
    function AudioManager() {
    }
    //播放 Bgm
    AudioManager.playBgm = function () {
        // 播放音乐，音乐文件名为'bgm.mp3'
        var music = LocalStorageManager_1.LocalStorageManager.GetInstance().getMusicSwitch(); //判断有没有开启背景音乐
        if (!music) {
            return;
        }
        AudioMgr_1.AudioMgr.ins.playMusic('bgm');
    };
    //停止 bgm
    AudioManager.stopBgm = function () {
        AudioMgr_1.AudioMgr.ins.stopMusic();
    };
    //按键音效
    AudioManager.keyingToneAudio = function () {
        AudioManager.playSound('keying_tone');
    };
    //胜利音效
    AudioManager.winAudio = function () {
        AudioManager.playSound('you_win');
    };
    //失败音效
    AudioManager.loseAudio = function () {
        AudioManager.playSound('you_lose');
    };
    //播放音效
    AudioManager.playSound = function (audioName) {
        var sound = LocalStorageManager_1.LocalStorageManager.GetInstance().getSoundSwitch(); //判断有没有开启音效
        if (!sound) {
            return;
        }
        AudioMgr_1.AudioMgr.ins.playSound(audioName);
    };
    return AudioManager;
}());
exports.AudioManager = AudioManager;

cc._RF.pop();