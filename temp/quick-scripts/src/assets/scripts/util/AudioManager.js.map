{"version": 3, "sources": ["assets/scripts/util/AudioManager.ts"], "names": [], "mappings": ";;;;;;;AAAA,uCAAsC;AACtC,6DAA4D;AAE5D;IAAA;IAyCA,CAAC;IAvCG,QAAQ;IACD,oBAAO,GAAd;QACI,uBAAuB;QACvB,IAAI,KAAK,GAAG,yCAAmB,CAAC,WAAW,EAAE,CAAC,cAAc,EAAE,CAAC,CAAA,aAAa;QAC5E,IAAI,CAAC,KAAK,EAAE;YACR,OAAO;SACV;QACD,mBAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;IAClC,CAAC;IACD,QAAQ;IACD,oBAAO,GAAd;QACI,mBAAQ,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC;IAC7B,CAAC;IAGD,MAAM;IACC,4BAAe,GAAtB;QACI,YAAY,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;IAC1C,CAAC;IAGD,MAAM;IACC,qBAAQ,GAAf;QACI,YAAY,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;IACtC,CAAC;IACD,MAAM;IACC,sBAAS,GAAhB;QACI,YAAY,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;IACvC,CAAC;IAGD,MAAM;IACC,sBAAS,GAAhB,UAAiB,SAAiB;QAC9B,IAAI,KAAK,GAAG,yCAAmB,CAAC,WAAW,EAAE,CAAC,cAAc,EAAE,CAAC,CAAA,WAAW;QAC1E,IAAI,CAAC,KAAK,EAAE;YACR,OAAO;SACV;QACD,mBAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,SAAS,CAAC,CAAA;IACrC,CAAC;IACL,mBAAC;AAAD,CAzCA,AAyCC,IAAA;AAzCY,oCAAY", "file": "", "sourceRoot": "/", "sourcesContent": ["import { AudioMgr } from \"./AudioMgr\";\nimport { LocalStorageManager } from \"./LocalStorageManager\";\n\nexport class AudioManager {\n\n    //播放 Bgm\n    static playBgm() {\n        // 播放音乐，音乐文件名为'bgm.mp3'\n        let music = LocalStorageManager.GetInstance().getMusicSwitch();//判断有没有开启背景音乐\n        if (!music) {\n            return;\n        }\n        AudioMgr.ins.playMusic('bgm');\n    }\n    //停止 bgm\n    static stopBgm() {\n        AudioMgr.ins.stopMusic();\n    }\n\n\n    //按键音效\n    static keyingToneAudio() {\n        AudioManager.playSound('keying_tone');\n    }\n\n    \n    //胜利音效\n    static winAudio() {\n        AudioManager.playSound('you_win');\n    }\n    //失败音效\n    static loseAudio() {\n        AudioManager.playSound('you_lose');\n    }\n   \n\n    //播放音效\n    static playSound(audioName: string) {\n        let sound = LocalStorageManager.GetInstance().getSoundSwitch();//判断有没有开启音效\n        if (!sound) {\n            return;\n        }\n        AudioMgr.ins.playSound(audioName)\n    }\n}"]}