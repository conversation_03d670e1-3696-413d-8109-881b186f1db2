{"version": 3, "sources": ["assets/scripts/util/Dictionary.ts"], "names": [], "mappings": ";;;;;;;AAAA;;GAEG;AACH;IAAA;QAEI,SAAI,GAAQ,EAAE,CAAC;QACf,WAAM,GAAQ,EAAE,CAAC;IA+LrB,CAAC;IA7LG;;OAEG;IACI,4BAAO,GAAd;QACI,IAAI,IAAI,GAAG,IAAI,CAAC;QAChB,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IACD;;OAEG;IACI,6BAAQ,GAAf,UAAgB,GAAM;QAClB,IAAI,IAAI,GAAG,IAAI,CAAC;QAChB,IAAI,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QACnC,IAAI,KAAK,IAAI,CAAC,CAAC;YACX,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC9B,OAAO,IAAK,CAAC;IACjB,CAAC;IAED,YAAY;IACL,2BAAM,GAAb,UAAc,KAAQ;QAClB,IAAI,IAAI,GAAG,IAAI,CAAC;QAChB,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QACvC,IAAI,KAAK,IAAI,CAAC,CAAC;YACX,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5B,OAAO,IAAK,CAAC;IACjB,CAAC;IAED,SAAS;IACF,gCAAW,GAAlB,UAAmB,GAAM,EAAE,WAAc;QACrC,IAAI,IAAI,GAAG,IAAI,CAAC;QAChB,IAAI,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QACnC,IAAI,KAAK,IAAI,CAAC,CAAC;YACX,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,WAAW,CAAC;IACzC,CAAC;IACD,SAAS;IACF,8BAAS,GAAhB,UAAiB,GAAM,EAAE,SAAY;QACjC,IAAI,IAAI,GAAG,IAAI,CAAC;QAChB,IAAI,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QACnC,IAAI,KAAK,IAAI,CAAC,CAAC;YACX,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,SAAS,CAAC;IACrC,CAAC;IACD,aAAa;IACN,gCAAW,GAAlB,UAAmB,GAAM,EAAE,KAAQ;QAC/B,IAAI,IAAI,GAAG,IAAI,CAAC;QAChB,IAAI,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QACnC,IAAI,KAAK,IAAI,CAAC,CAAC;YACX,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;;YAE3B,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IAC7B,CAAC;IAED;;OAEG;IACI,wBAAG,GAAV,UAAW,GAAM,EAAE,KAAQ,EAAE,IAAa;QACtC,IAAI,IAAI,GAAG,IAAI,CAAC;QAChB,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE;YAC9B,wCAAwC;YACxC,OAAO;SACV;QACD,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACpB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC5B,CAAC;IAED;;;;OAIG;IACI,6BAAQ,GAAf,UAAgB,GAAM,EAAE,KAAU,EAAE,IAAgB;QAAhB,qBAAA,EAAA,QAAgB;QAChD,IAAI,IAAI,GAAG,IAAI,CAAC;QAChB,IAAI,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QACnC,IAAI,KAAK,GAAG,CAAC,EAAE;YACX,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACpB,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;SACnC;aAAM;YACH,IAAI,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAQ,CAAC;YACvC,IAAG,IAAI,IAAI,CAAC,EAAE;gBACV,IAAG,MAAM,EAAE;oBACP,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;iBACtB;qBAAM;oBACH,MAAM,GAAG,CAAC,KAAK,CAAC,CAAC;iBACpB;aACJ;iBAAM;gBACH,IAAG,MAAM,EAAE;oBACP,MAAM,IAAI,KAAK,CAAC;iBACnB;qBAAM;oBACH,MAAM,GAAG,CAAC,CAAC;iBACd;aACJ;YACD,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;SACjC;IACL,CAAC;IAED;;OAEG;IACI,0BAAK,GAAZ;QACI,IAAI,IAAI,GAAG,IAAI,CAAC;QAChB,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;QACrB,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACI,8BAAS,GAAhB,UAAiB,GAAM;QACnB,IAAI,IAAI,GAAG,IAAI,CAAC;QAChB,IAAI,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QACnC,IAAI,KAAK,GAAG,CAAC;YAAE,OAAO;QACtB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QAC3B,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;IACjC,CAAC;IAED;;OAEG;IACI,gCAAW,GAAlB,UAAmB,KAAQ;QACvB,IAAI,IAAI,GAAG,IAAI,CAAC;QAChB,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QACvC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QAC3B,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;IACjC,CAAC;IAED;;OAEG;IACI,gCAAW,GAAlB,UAAmB,GAAM;QACrB,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE;YACjC,OAAO,KAAK,CAAC;SAChB;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,kCAAa,GAApB,UAAqB,KAAQ;QACzB,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE;YACrC,OAAO,KAAK,CAAC;SAChB;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,cAAc;IACP,wBAAG,GAAV;QACI,IAAI,IAAI,GAAG,IAAI,CAAC;QAChB,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;QAChB,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;IACtB,CAAC;IAED,cAAc;IACP,yBAAI,GAAX,UAAY,IAAY,EAAE,IAAY;QAClC,IAAI,IAAI,GAAG,IAAI,CAAC;QAChB,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI;YACxB,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI;YACxB,OAAO;QACX,IAAI;QACJ,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAClC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;QACvB,IAAI,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAC7B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACtC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;IAC7B,CAAC;IACD,gBAAgB;IACT,6BAAQ,GAAf,UAAgB,IAAO,EAAE,IAAO;QAC5B,IAAI,IAAI,GAAG,IAAI,CAAC;QAChB,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC5B,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC5B,OAAO;QACX,IAAI,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC/B,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;QAC5C,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IACjC,CAAC;IAYD,sBAAW,4BAAI;QAXf,mDAAmD;QACnD,uBAAuB;QACvB,sCAAsC;QACtC,sCAAsC;QACtC,kBAAkB;QAClB,oCAAoC;QACpC,6CAA6C;QAC7C,gCAAgC;QAChC,IAAI;QAEJ,QAAQ;aACR;YACI,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;QAC5B,CAAC;;;OAAA;IACL,iBAAC;AAAD,CAlMA,AAkMC,IAAA;AAlMY,gCAAU", "file": "", "sourceRoot": "/", "sourcesContent": ["/**\n * 自定义字典类\n */\nexport class Dictionary<K, V>\n{\n    keys: K[] = [];\n    values: V[] = [];\n \n    /**\n     * 获取所有值\n     */\n    public getList() {\n        let self = this;\n        return self.values;\n    }\n    /**\n     * 根据键得到值\n     */\n    public getValue(key: K): V {\n        let self = this;\n        let index = self.keys.indexOf(key);\n        if (index != -1)\n            return self.values[index];\n        return null!;\n    }\n \n    /**根据值得到键 */\n    public getKey(value: V): K {\n        let self = this;\n        let index = self.values.indexOf(value);\n        if (index != -1)\n            return self.keys[index];\n        return null!;\n    }\n \n    /**改变值 */\n    public changeValue(key: K, changeValue: V): void {\n        let self = this;\n        let index = self.keys.indexOf(key);\n        if (index != -1)\n            self.values[index] = changeValue;\n    }\n    /**改变键 */\n    public changeKey(key: K, changeKey: K): void {\n        let self = this;\n        let index = self.keys.indexOf(key);\n        if (index != -1)\n            self.keys[index] = changeKey;\n    }\n    /** 根据键刷新值 */\n    public updateValue(key: K, value: V) {\n        let self = this;\n        let index = self.keys.indexOf(key);\n        if (index != -1)\n            self.values[index] = value;\n        else \n            self.add(key, value);\n    }\n \n    /**\n     * 添加键值\n     */\n    public add(key: K, value: V, name?: string): void {\n        let self = this;\n        if (self.keys.indexOf(key) != -1) {\n            // console.log(\"same key in dic\", name);\n            return;\n        }\n        self.keys.push(key);\n        self.values.push(value);\n    }\n \n    /** \n     * 根据键添加值\n     * type: 0:values是数组往里面添加值\n     *       1:values是number,用来统计数量的\n     */\n    public addValue(key: K, value: any, type: number = 0) {\n        let self = this;\n        let index = self.keys.indexOf(key);\n        if (index < 0) {\n            self.keys.push(key);\n            self.addValue(key, value, type);\n        } else {\n            let values = self.getValue(key) as any;\n            if(type == 0) {\n                if(values) {\n                    values.push(value);\n                } else {\n                    values = [value];\n                }\n            } else {\n                if(values) {\n                    values += value;\n                } else {\n                    values = 1;\n                }\n            }\n            self.changeValue(key, values);\n        }\n    }\n \n    /**\n     * 清空\n     */\n    public clear(): void {\n        let self = this;\n        self.keys.length = 0;\n        self.values.length = 0;\n    }\n \n    /**\n     * 根据键移除对象\n     */\n    public removeKey(key: K): void {\n        let self = this;\n        let index = self.keys.indexOf(key);\n        if (index < 0) return;\n        self.keys.splice(index, 1);\n        self.values.splice(index, 1);\n    }\n \n    /**\n     * 根据值移除对象\n     */\n    public removeValue(value: V): void {\n        let self = this;\n        let index = self.values.indexOf(value);\n        self.keys.splice(index, 1);\n        self.values.splice(index, 1);\n    }\n \n    /**\n     * 根据键检测是否存在对象\n     */\n    public containsKey(key: K) {\n        if (this.keys.indexOf(key, 0) == -1) {\n            return false;\n        }\n        return true;\n    }\n \n    /**\n     * 根据值检测是否存在对象\n     */\n    public containsValue(value: V) {\n        if (this.values.indexOf(value, 0) == -1) {\n            return false;\n        }\n        return true;\n    }\n \n    /**突出最后一个对象 */\n    public pop(): void {\n        let self = this;\n        self.keys.pop();\n        self.values.pop();\n    }\n \n    /**根据索引交换位置 */\n    public swap(num1: number, num2: number): void {\n        let self = this;\n        if (self.keys.length <= num1 ||\n            self.keys.length <= num2)\n            return;\n        //交换\n        let tmpK = self.keys[num1];\n        self.keys[num1] = self.keys[num2];\n        self.keys[num2] = tmpK;\n        let tmpV = self.values[num1];\n        self.values[num1] = self.values[num2];\n        self.values[num2] = tmpV;\n    }\n    /** 交换两个索引对应的值*/\n    public cutValue(num1: K, num2: K): void {\n        let self = this;\n        if (self.keys.indexOf(num1) < -1 ||\n            self.keys.indexOf(num1) < -2)\n            return;\n        let tmpV = self.getValue(num1);\n        self.changeValue(num1, self.getValue(num2));\n        self.changeValue(num2, tmpV);\n    }\n    // public cutValue(num1:number, num2:number):void {\n    //     let self = this;\n    //     if( self.keys.length <= num1 ||\n    //         self.keys.length <= num2  )\n    //         return;\n    //     let tmpV = self.values[num1];\n    //     self.values[num1] = self.values[num2];\n    //     self.values[num2] = tmpV;\n    // }\n \n    /**长度 */\n    public get size(): number {\n        return this.keys.length;\n    }\n}"]}