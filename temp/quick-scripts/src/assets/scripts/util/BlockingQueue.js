"use strict";
cc._RF.push(module, '3ff90WyQGFLYZ+OEda8zbs/', 'BlockingQueue');
// scripts/util/BlockingQueue.ts

var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var BlockingQueue = /** @class */ (function () {
    // 私有构造函数，防止外部直接创建实例
    function BlockingQueue(capacity) {
        this.queue = [];
        this.waitingConsumers = [];
        this.waitingProducers = [];
        this.capacity = capacity;
    }
    // 获取单例实例
    BlockingQueue.getInstance = function (capacity) {
        if (!BlockingQueue.instance) {
            BlockingQueue.instance = new BlockingQueue(capacity);
        }
        return BlockingQueue.instance;
    };
    // 向队列添加元素
    BlockingQueue.prototype.enqueue = function (item) {
        return __awaiter(this, void 0, Promise, function () {
            var consumer;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!(this.queue.length >= this.capacity)) return [3 /*break*/, 2];
                        // 队列已满，等待直到有空间
                        return [4 /*yield*/, new Promise(function (resolve) { return _this.waitingProducers.push(resolve); })];
                    case 1:
                        // 队列已满，等待直到有空间
                        _a.sent();
                        _a.label = 2;
                    case 2:
                        this.queue.push(item);
                        // 如果有消费者等待，唤醒一个消费者
                        if (this.waitingConsumers.length > 0) {
                            consumer = this.waitingConsumers.shift();
                            consumer === null || consumer === void 0 ? void 0 : consumer();
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    // 从队列中取出元素
    BlockingQueue.prototype.dequeue = function () {
        return __awaiter(this, void 0, Promise, function () {
            var item, producer;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!(this.queue.length === 0)) return [3 /*break*/, 2];
                        // 队列为空，等待直到有元素
                        return [4 /*yield*/, new Promise(function (resolve) { return _this.waitingConsumers.push(resolve); })];
                    case 1:
                        // 队列为空，等待直到有元素
                        _a.sent();
                        _a.label = 2;
                    case 2:
                        item = this.queue.shift();
                        // 如果有生产者等待，唤醒一个生产者
                        if (this.waitingProducers.length > 0) {
                            producer = this.waitingProducers.shift();
                            producer === null || producer === void 0 ? void 0 : producer();
                        }
                        return [2 /*return*/, item];
                }
            });
        });
    };
    // 队列的当前大小
    BlockingQueue.prototype.size = function () {
        return this.queue.length;
    };
    // 队列是否已满
    BlockingQueue.prototype.isFull = function () {
        return this.queue.length >= this.capacity;
    };
    // 队列是否为空
    BlockingQueue.prototype.isEmpty = function () {
        return this.queue.length === 0;
    };
    BlockingQueue.instance = null;
    return BlockingQueue;
}());

cc._RF.pop();