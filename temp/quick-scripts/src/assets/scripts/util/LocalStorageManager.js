"use strict";
cc._RF.push(module, '4829baz02BEOKmgqAB/a7a3', 'LocalStorageManager');
// scripts/util/LocalStorageManager.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.LocalStorageManager = void 0;
var Singleton_1 = require("../../meshTools/Singleton");
//这个就是本地存储的管理类
var LocalStorageManager = /** @class */ (function (_super) {
    __extends(LocalStorageManager, _super);
    function LocalStorageManager() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.musicSwitch = 'MusicSwitch'; //背景音乐的控制
        _this.soundSwitch = 'SoundSwitch'; //动效音乐的控制
        return _this;
    }
    LocalStorageManager.prototype._setValue = function (key, value) {
        localStorage.setItem(key, value);
    };
    LocalStorageManager.prototype._getValue = function (key) {
        return localStorage.getItem(key);
    };
    //设置背景音乐的开关
    LocalStorageManager.prototype.setMusicSwitch = function (bool) {
        this._setValue(this.musicSwitch, bool ? '1' : '0');
    };
    //获取背景音乐的开关,默认是开的
    LocalStorageManager.prototype.getMusicSwitch = function () {
        var value = this._getValue(this.musicSwitch);
        if (value) {
            return value == '1' ? true : false;
        }
        else {
            return true;
        }
    };
    //设置动效音乐的开关
    LocalStorageManager.prototype.setSoundSwitch = function (bool) {
        this._setValue(this.soundSwitch, bool ? '1' : '0');
    };
    //获取动效音乐的开关,默认是开的
    LocalStorageManager.prototype.getSoundSwitch = function () {
        var value = this._getValue(this.soundSwitch);
        if (value) {
            return value == '1' ? true : false;
        }
        else {
            return true;
        }
    };
    return LocalStorageManager;
}(Singleton_1.Singleton));
exports.LocalStorageManager = LocalStorageManager;

cc._RF.pop();