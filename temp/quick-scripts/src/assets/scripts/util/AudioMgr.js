"use strict";
cc._RF.push(module, 'a2ba8VmZJlPZ4hHcfCOQPX0', 'AudioMgr');
// scripts/util/AudioMgr.ts

"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AudioMgr = void 0;
var Config_1 = require("./Config");
var Dictionary_1 = require("./Dictionary");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
/**最多有几个音效播放器*/
var MAX_SOUNDS = 8;
/**
 * 音效管理器
 */
var AudioMgr = /** @class */ (function () {
    function AudioMgr() {
        /**音效的常驻节点*/
        this._persistRootNode = null;
        //bgm音效全局唯一一个
        this._music = null;
        //sound音效可以有多个
        this._sounds = null;
        /**bgm静音 0没静音 1静音*/
        this._music_muted = 0;
        /**sound静音 0没静音 1静音*/
        this._sound_muted = 0;
        /**bgm音量*/
        this._music_volume = 1;
        /**sound音量*/
        this._sound_volume = 1;
        /**当前播放的音效索引用以控制使用不同的AudioSource*/
        this._now_soundid = 0;
        /**当前播放的bgm音效名字*/
        this._cur_music_name = "";
    }
    AudioMgr_1 = AudioMgr;
    Object.defineProperty(AudioMgr, "ins", {
        get: function () {
            if (this._instance) {
                return this._instance;
            }
            this._instance = new AudioMgr_1();
            return this._instance;
        },
        enumerable: false,
        configurable: true
    });
    AudioMgr.prototype.init = function () {
        if (this._persistRootNode)
            return; //避免切换场景初始化报错
        this._persistRootNode = cc.find("DataNode"); //这个节点是常驻节点，如果不是的话 就用下面那行代码把它变成常驻节点
        cc.game.addPersistRootNode(this._persistRootNode);
        this._sounds = [];
        this.music_clips = new Dictionary_1.Dictionary();
        /**
         * 读取本地存储的数据
         *
         */
        this._music = this._persistRootNode.addComponent(cc.AudioSource);
        //获取bgm的音量
        this._music.volume = this._music_volume;
        //获取bgm是否存储静音
        this._music.volume = this._music_muted == 1 ? 0 : this._music_volume;
        //获取sounds列表
        for (var i = 0; i < MAX_SOUNDS; i++) {
            this._sounds[i] = this._persistRootNode.addComponent(cc.AudioSource);
            this._sounds[i].volume = this._sound_volume;
            this._sounds[i].volume = this._sound_muted == 1 ? 0 : this._sound_volume;
        }
    };
    /**
     * @param audioName 音效名字
     * @param isLoop 是否循环播放
     * @protected 播放音效
     */
    AudioMgr.prototype.playMusic = function (audioName, isLoop) {
        var _this = this;
        if (isLoop === void 0) { isLoop = true; }
        if (this._cur_music_name == audioName) {
            return;
        }
        var call = function (clip) {
            // this._music.clip = null
            _this._music.clip = clip;
            _this._music.loop = isLoop;
            _this._music.play();
            if (!_this.music_clips.containsKey(audioName)) {
                _this.music_clips.add(audioName, clip);
            }
        };
        if (this.music_clips.containsKey(audioName)) {
            call(this.music_clips.getValue(audioName));
        }
        else {
            var bundleName = "resources";
            cc.assetManager.loadBundle(bundleName, function (err, bundle) {
                bundle.load(Config_1.Config.audio + audioName, cc.AudioClip, function (err, clip) {
                    if (err) {
                        console.error("loadAudioClip" + err);
                    }
                    else {
                        call(clip);
                    }
                });
            });
        }
    };
    /**
     * @param audioName 音效名字
     * @param isLoop 是否循环播放
     * @protected 播放音效
     */
    AudioMgr.prototype.playSound = function (audioName, isLoop) {
        var _this = this;
        if (isLoop === void 0) { isLoop = false; }
        var call = function (clip) {
            // this._sounds[this._now_soundid].clip = null
            _this._sounds[_this._now_soundid].clip = clip;
            _this._sounds[_this._now_soundid].loop = isLoop;
            _this._sounds[_this._now_soundid].play();
            if (!_this.music_clips.containsKey(audioName)) {
                _this.music_clips.add(audioName, clip);
            }
            _this._now_soundid = _this._now_soundid + 1 >= _this._sounds.length ? 0 : _this._now_soundid + 1;
        };
        if (this.music_clips.containsKey(audioName)) {
            call(this.music_clips.getValue(audioName));
        }
        else {
            var bundleName = "resources";
            cc.assetManager.loadBundle(bundleName, function (err, bundle) {
                bundle.load(Config_1.Config.audio + audioName, cc.AudioClip, function (err, clip) {
                    if (err) {
                        console.error("loadAudioClip" + err);
                    }
                    else {
                        call(clip);
                    }
                });
            });
        }
    };
    /**
     * 停止播放bgm
     */
    AudioMgr.prototype.stopMusic = function () {
        this._music.stop();
        // this._music.clip = null
    };
    /**
     * 停止播放所有的sound
     */
    AudioMgr.prototype.stopAllSound = function () {
        for (var i = 0; i < this._sounds.length; i++) {
            this._sounds[i].stop();
            // this._sounds[i].clip = null
        }
        this._now_soundid = 0;
    };
    /**
     *
     * @param mute 是否静音music
     */
    AudioMgr.prototype.setMusicMute = function (mute) {
        if (mute == (this._music_muted == 1)) {
            return;
        }
        this._music_muted = mute ? 1 : 0;
        this._music.volume = mute ? this._music_volume : 0;
        //存储music静音 this._music_muted
    };
    /**
     *
     * @param mute 是否静音sound
     */
    AudioMgr.prototype.setSoundMute = function (mute) {
        if (mute == (this._sound_muted == 1)) {
            return;
        }
        this._sound_muted = mute ? 1 : 0;
        for (var i = 0; i < this._sounds.length; i++) {
            this._sounds[i].volume = mute ? this._sound_volume : 0;
        }
        //存储sound静音 this._sound_muted
    };
    /**
     *
     * @param value 设置音乐声音大小
     */
    AudioMgr.prototype.setMusicVolume = function (value) {
        this._music.volume = value;
        this._music_volume = value;
        //存储music音量大小 this._music_volume
    };
    /**
     *
     * @param value 设置sound声音大小
     */
    AudioMgr.prototype.setSoundVolume = function (value) {
        this._sound_volume = value;
        for (var i = 0; i < this._sounds.length; i++) {
            this._sounds[i].volume = value;
        }
        //存储sound音量大小 this._sound_volume
    };
    /**
     *
     * @returns 返回bgm静音状态
     */
    AudioMgr.prototype.getMusicMute = function () {
        return this._music_muted;
    };
    /**
     *
     * @returns 返回sound音效静音状态
     */
    AudioMgr.prototype.getSoundMute = function () {
        return this._sound_muted;
    };
    /**
     *
     * @returns 返回bgm声音大小
     */
    AudioMgr.prototype.getMusicVolume = function () {
        return this._music_volume;
    };
    /**
     *
     * @returns 返回sound音效声音大小
     */
    AudioMgr.prototype.getSoundVolume = function () {
        return this._sound_volume;
    };
    var AudioMgr_1;
    AudioMgr = AudioMgr_1 = __decorate([
        ccclass("AudioMgr")
    ], AudioMgr);
    return AudioMgr;
}());
exports.AudioMgr = AudioMgr;

cc._RF.pop();