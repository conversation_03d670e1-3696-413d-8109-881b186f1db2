{"version": 3, "sources": ["assets/scripts/util/LocalStorageManager.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA,uDAAsD;AAItD,cAAc;AACd;IAAyC,uCAAS;IAAlD;QAAA,qEAyCC;QAvCG,iBAAW,GAAW,aAAa,CAAC,CAAA,SAAS;QAC7C,iBAAW,GAAW,aAAa,CAAC,CAAA,SAAS;;IAsCjD,CAAC;IApCU,uCAAS,GAAjB,UAAkB,GAAW,EAAE,KAAa;QACvC,YAAY,CAAC,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IACrC,CAAC;IAEO,uCAAS,GAAjB,UAAkB,GAAW;QACzB,OAAO,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IACrC,CAAC;IAED,WAAW;IACX,4CAAc,GAAd,UAAe,IAAa;QACxB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACvD,CAAC;IACD,iBAAiB;IACjB,4CAAc,GAAd;QACI,IAAI,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC7C,IAAG,KAAK,EAAC;YACL,OAAO,KAAK,IAAI,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;SACtC;aAAI;YACD,OAAO,IAAI,CAAC;SACf;IAEL,CAAC;IAED,WAAW;IACX,4CAAc,GAAd,UAAe,IAAa;QACxB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACvD,CAAC;IACD,iBAAiB;IACjB,4CAAc,GAAd;QACI,IAAI,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC7C,IAAG,KAAK,EAAC;YACL,OAAO,KAAK,IAAI,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;SACtC;aAAI;YACD,OAAO,IAAI,CAAC;SACf;IACL,CAAC;IACL,0BAAC;AAAD,CAzCA,AAyCC,CAzCwC,qBAAS,GAyCjD;AAzCY,kDAAmB", "file": "", "sourceRoot": "/", "sourcesContent": ["import { Singleton } from \"../../meshTools/Singleton\";\n\n\n\n//这个就是本地存储的管理类\nexport class LocalStorageManager extends Singleton {\n\n    musicSwitch: string = 'MusicSwitch';//背景音乐的控制\n    soundSwitch: string = 'SoundSwitch';//动效音乐的控制\n\n   private _setValue(key: string, value: string) {\n        localStorage.setItem(key, value);\n    }\n\n    private _getValue(key: string,) {\n        return localStorage.getItem(key);\n    }\n\n    //设置背景音乐的开关\n    setMusicSwitch(bool: boolean) {\n        this._setValue(this.musicSwitch, bool ? '1' : '0');\n    }\n    //获取背景音乐的开关,默认是开的\n    getMusicSwitch() {\n        let value = this._getValue(this.musicSwitch);\n        if(value){\n            return value == '1' ? true : false;\n        }else{\n            return true;\n        }\n        \n    }\n\n    //设置动效音乐的开关\n    setSoundSwitch(bool: boolean) {\n        this._setValue(this.soundSwitch, bool ? '1' : '0');\n    }\n    //获取动效音乐的开关,默认是开的\n    getSoundSwitch() {\n        let value = this._getValue(this.soundSwitch);\n        if(value){\n            return value == '1' ? true : false;\n        }else{\n            return true;\n        }\n    }\n}"]}