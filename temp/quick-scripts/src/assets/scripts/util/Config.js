"use strict";
cc._RF.push(module, '21e14lh3EZFpZ8wKzVoFPD8', 'Config');
// scripts/util/Config.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Config = void 0;
var Config = /** @class */ (function () {
    function Config() {
    }
    //resources 相关
    Config.buttonRes = 'button_res/'; //按钮相关的资源
    Config.hallRes = 'hall_res/'; //大厅的资源文件夹
    Config.gameRes = 'game_res/'; //游戏中的资源文件夹
    Config.audio = 'audio/'; //游戏中的音乐资源
    //按钮名称
    Config.btnRedNormal = Config.buttonRes + 'btn_red_normal';
    Config.btnRedPressed = Config.buttonRes + 'btn_red_pressed';
    Config.btnRedNormalColor = "#BA3207";
    Config.btnRedPressedColor = "#832305";
    Config.btnGreenNormal = Config.buttonRes + 'btn_green_normal';
    Config.btnGreenPressed = Config.buttonRes + 'btn_green_pressed';
    Config.btnGreenNormalColor = "#119C0F";
    Config.btnGreenPressedColor = "#0C6D0B";
    Config.btnYellowNormal = Config.buttonRes + 'btn_yellow_normal';
    Config.btnYellowPressed = Config.buttonRes + 'btn_yellow_pressed';
    Config.btnYellowNormalColor = "#CC6519";
    Config.btnYellowPressedColor = "#8F4611";
    Config.btnGrayNormal = Config.buttonRes + 'btn_gray_normal';
    Config.btnGrayNormalColor = "#7B7B7B";
    //game 相关
    Config.dialogScaleTime = 0.15; //弹窗的缩放时间
    Config.latticeNumber = 63; //格子数量
    Config.latticeNumberH = 7; //每一竖行格子数
    Config.latticeNumberW = 9; //每一横行格子数
    Config.flyTime = 0.3; //飞行动画时间 s
    Config.huojianAndZhadanSkillTime = 0.65; //火箭和炸弹技能动画时间 s
    return Config;
}());
exports.Config = Config;

cc._RF.pop();