{"version": 3, "sources": ["assets/scripts/util/BlockingQueue.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;IAOI,oBAAoB;IACpB,uBAAoB,QAAgB;QAN5B,UAAK,GAAQ,EAAE,CAAC;QAEhB,qBAAgB,GAAmB,EAAE,CAAC;QACtC,qBAAgB,GAAmB,EAAE,CAAC;QAI1C,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAC7B,CAAC;IAED,SAAS;IACK,yBAAW,GAAzB,UAA6B,QAAgB;QACzC,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;YACzB,aAAa,CAAC,QAAQ,GAAG,IAAI,aAAa,CAAI,QAAQ,CAAC,CAAC;SAC3D;QACD,OAAO,aAAa,CAAC,QAAQ,CAAC;IAClC,CAAC;IAED,UAAU;IACJ,+BAAO,GAAb,UAAc,IAAO;uCAAG,OAAO;;;;;;6BACvB,CAAA,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAA,EAAlC,wBAAkC;wBAClC,eAAe;wBACf,qBAAM,IAAI,OAAO,CAAO,UAAC,OAAO,IAAK,OAAA,KAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAnC,CAAmC,CAAC,EAAA;;wBADzE,eAAe;wBACf,SAAyE,CAAC;;;wBAE9E,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBAEtB,mBAAmB;wBACnB,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE;4BAC5B,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;4BAC/C,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,GAAK;yBAChB;;;;;KACJ;IAED,WAAW;IACL,+BAAO,GAAb;uCAAiB,OAAO;;;;;;6BAChB,CAAA,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,CAAA,EAAvB,wBAAuB;wBACvB,eAAe;wBACf,qBAAM,IAAI,OAAO,CAAO,UAAC,OAAO,IAAK,OAAA,KAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAnC,CAAmC,CAAC,EAAA;;wBADzE,eAAe;wBACf,SAAyE,CAAC;;;wBAGxE,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,EAAG,CAAC;wBAEjC,mBAAmB;wBACnB,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE;4BAC5B,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;4BAC/C,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,GAAK;yBAChB;wBAED,sBAAO,IAAI,EAAC;;;;KACf;IAED,UAAU;IACV,4BAAI,GAAJ;QACI,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;IAC7B,CAAC;IAED,SAAS;IACT,8BAAM,GAAN;QACI,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAC;IAC9C,CAAC;IAED,SAAS;IACT,+BAAO,GAAP;QACI,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC;IACnC,CAAC;IAjEc,sBAAQ,GAA8B,IAAI,CAAC;IAkE9D,oBAAC;CAnED,AAmEC,IAAA", "file": "", "sourceRoot": "/", "sourcesContent": ["class BlockingQueue<T> {\n    private static instance: BlockingQueue<any> | null = null;\n    private queue: T[] = [];\n    private capacity: number;\n    private waitingConsumers: (() => void)[] = [];\n    private waitingProducers: (() => void)[] = [];\n\n    // 私有构造函数，防止外部直接创建实例\n    private constructor(capacity: number) {\n        this.capacity = capacity;\n    }\n\n    // 获取单例实例\n    public static getInstance<T>(capacity: number): BlockingQueue<T> {\n        if (!BlockingQueue.instance) {\n            BlockingQueue.instance = new BlockingQueue<T>(capacity);\n        }\n        return BlockingQueue.instance;\n    }\n\n    // 向队列添加元素\n    async enqueue(item: T): Promise<void> {\n        if (this.queue.length >= this.capacity) {\n            // 队列已满，等待直到有空间\n            await new Promise<void>((resolve) => this.waitingProducers.push(resolve));\n        }\n        this.queue.push(item);\n\n        // 如果有消费者等待，唤醒一个消费者\n        if (this.waitingConsumers.length > 0) {\n            const consumer = this.waitingConsumers.shift();\n            consumer?.();\n        }\n    }\n\n    // 从队列中取出元素\n    async dequeue(): Promise<T> {\n        if (this.queue.length === 0) {\n            // 队列为空，等待直到有元素\n            await new Promise<void>((resolve) => this.waitingConsumers.push(resolve));\n        }\n\n        const item = this.queue.shift()!;\n\n        // 如果有生产者等待，唤醒一个生产者\n        if (this.waitingProducers.length > 0) {\n            const producer = this.waitingProducers.shift();\n            producer?.();\n        }\n\n        return item;\n    }\n\n    // 队列的当前大小\n    size(): number {\n        return this.queue.length;\n    }\n\n    // 队列是否已满\n    isFull(): boolean {\n        return this.queue.length >= this.capacity;\n    }\n\n    // 队列是否为空\n    isEmpty(): boolean {\n        return this.queue.length === 0;\n    }\n}"]}