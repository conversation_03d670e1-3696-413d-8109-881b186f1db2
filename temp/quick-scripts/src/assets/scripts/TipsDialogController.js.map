{"version": 3, "sources": ["assets/scripts/TipsDialogController.ts"], "names": [], "mappings": ";;;;;AAAA,oBAAoB;AACpB,4EAA4E;AAC5E,mBAAmB;AACnB,sFAAsF;AACtF,8BAA8B;AAC9B,sFAAsF;;;;;;;;;;;;;;;;;;;;;AAEtF,wCAAuC;AACvC,sCAAqC;AAE/B,IAAA,KAAwB,EAAE,CAAC,UAAU,EAAnC,OAAO,aAAA,EAAE,QAAQ,cAAkB,CAAC;AAE5C,iBAAiB;AAEjB;IAAkD,wCAAY;IAA9D;QAAA,qEAqCC;QAlCG,aAAO,GAAY,IAAI,CAAA;QAEvB,aAAO,GAAa,IAAI,CAAC,CAAA,IAAI;QAE7B,iBAAW,GAAY,IAAI,CAAC,CAAA,MAAM;;QA6BlC,iBAAiB;IACrB,CAAC;IA3BG,eAAe;IAEf,oCAAK,GAAL;QAAA,iBAQC;QANG,aAAK,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,EAAE;YAC9B,IAAI,KAAI,CAAC,QAAQ,EAAE;gBACf,KAAI,CAAC,QAAQ,EAAE,CAAC;aACnB;QACL,CAAC,CAAC,CAAA;IAEN,CAAC;IAED,yCAAU,GAAV,UAAW,OAAe,EAAE,QAAkB;QAC1C,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAA;QACxB,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,OAAO,CAAA;QAC7B,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,KAAK,EAAE;YAC3B,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAA;YACvB,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,CAAC,CAAA;YACtB,SAAS;YACT,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC;iBACjB,EAAE,CAAC,eAAM,CAAC,eAAe,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;iBACxC,KAAK,EAAE,CAAC;SAChB;IAEL,CAAC;IA/BD;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;yDACK;IAEvB;QADC,QAAQ,CAAC,EAAE,CAAC,KAAK,CAAC;yDACM;IAEzB;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;6DACU;IAPX,oBAAoB;QADxC,OAAO;OACa,oBAAoB,CAqCxC;IAAD,2BAAC;CArCD,AAqCC,CArCiD,EAAE,CAAC,SAAS,GAqC7D;kBArCoB,oBAAoB", "file": "", "sourceRoot": "/", "sourcesContent": ["// Learn TypeScript:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html\n// Learn Attribute:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html\n// Learn life-cycle callbacks:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html\n\nimport { Config } from \"./util/Config\";\nimport { Tools } from \"./util/Tools\";\n\nconst { ccclass, property } = cc._decorator;\n\n//这个是只有一个退出按钮的 弹窗\n@ccclass\nexport default class TipsDialogController extends cc.Component {\n\n    @property(cc.Node)\n    boardBg: cc.Node = null\n    @property(cc.Label)\n    content: cc.Label = null;//文案\n    @property(cc.Node)\n    leaveButton: cc.Node = null;//退出按钮\n\n    callback: Function\n    // onLoad () {}\n\n    start() {\n\n        Tools.redButton(this.leaveButton, () => {\n            if (this.callback) {\n                this.callback();\n            }\n        })\n\n    }\n\n    showDialog(content: string, callback: Function) {\n        this.callback = callback\n        this.content.string = content\n        if (this.node.active == false) {\n            this.node.active = true\n            this.boardBg.scale = 0\n            // 执行缩放动画\n            cc.tween(this.boardBg)\n                .to(Config.dialogScaleTime, { scale: 1 })\n                .start();\n        }\n\n    }\n\n    // update (dt) {}\n}\n"]}