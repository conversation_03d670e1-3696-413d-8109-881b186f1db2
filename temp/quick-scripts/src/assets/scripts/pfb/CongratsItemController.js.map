{"version": 3, "sources": ["assets/scripts/pfb/CongratsItemController.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAGA,yDAAwD;AAExD,yCAAwC;AACxC,uDAAkD;AAClD,uCAAsC;AAE9B,IAAA,OAAO,GAAK,EAAE,CAAC,UAAU,QAAlB,CAAmB;AAGlC;IAAoD,0CAAY;IAAhE;;IA0EA,CAAC;IA/Da,sCAAK,GAAf;QACI,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC,CAAC,CAAA,OAAO;QAC9E,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC,CAAA,OAAO;QAC7D,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,CAAA,OAAO;QAC5D,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAA,OAAO;QACtF,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAA,QAAQ;QACvF,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,qBAAqB,CAAC,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,CAAA,OAAO;QAChI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,qBAAqB,CAAC,CAAC,cAAc,CAAC,kBAAkB,CAAC,CAAA,CAAA,MAAM;IAE5G,CAAC;IAED,MAAM;IACN,iBAAiB;IACjB,qBAAqB;IACrB,2CAAU,GAAV,UAAW,UAA8C,EAAE,SAAqB;QAE5E,IAAI,UAAU,CAAC,IAAI,IAAI,CAAC,EAAE;YACtB,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAG,IAAI,CAAA;YACrC,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,KAAK,CAAA;YAC5B,QAAQ;YACR,QAAQ,UAAU,CAAC,IAAI,EAAE;gBACrB,KAAK,CAAC;oBACF,aAAK,CAAC,kBAAkB,CAAC,IAAI,CAAC,kBAAkB,EAAE,eAAM,CAAC,OAAO,GAAG,qBAAqB,CAAC,CAAC;oBAC1F,MAAM;gBACV,KAAK,CAAC;oBACF,aAAK,CAAC,kBAAkB,CAAC,IAAI,CAAC,kBAAkB,EAAE,eAAM,CAAC,OAAO,GAAG,qBAAqB,CAAC,CAAC;oBAC1F,MAAM;gBACV,KAAK,CAAC;oBACF,aAAK,CAAC,kBAAkB,CAAC,IAAI,CAAC,kBAAkB,EAAE,eAAM,CAAC,OAAO,GAAG,qBAAqB,CAAC,CAAC;oBAC1F,MAAM;aACb;SACJ;aAAM;YACH,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAG,KAAK,CAAA;YACtC,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAA;YAC3B,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,UAAU,CAAC,IAAI,GAAG,EAAE,CAAC,CAAA,MAAM;SAC5E;QAED,kBAAkB;QAClB,IAAI,UAAkB,CAAC;QACvB,IAAI,OAAO,IAAI,UAAU,EAAE;YACvB,oBAAoB;YACpB,UAAU,GAAG,UAAU,CAAC,KAAK,CAAC;SACjC;aAAM;YACH,uBAAuB;YACvB,UAAU,GAAG,UAAU,CAAC,UAAU,CAAC;SACtC;QACD,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,UAAU,GAAG,EAAE,CAAC,CAAA,MAAM;QAEpE,IAAM,KAAK,GAAG,SAAS,CAAC,SAAS,CAAC,UAAC,IAAI,IAAK,OAAA,IAAI,CAAC,MAAM,KAAK,UAAU,CAAC,MAAM,EAAjC,CAAiC,CAAC,CAAC,CAAA,IAAI;QACnF,IAAI,KAAK,IAAI,CAAC,CAAC,EAAE;YACb,IAAI,IAAI,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC;YAC5B,aAAK,CAAC,qBAAqB,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;YAC1D,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,uBAAa,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAA;SAEnE;QACD,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,aAAK,CAAC,SAAS,CAAC,UAAU,CAAC,OAAO,CAAC,CAAA;QAGnF,IAAI,iBAAO,CAAC,WAAW,EAAE,CAAC,YAAY,IAAI,IAAI,IAAI,iBAAO,CAAC,WAAW,EAAE,CAAC,YAAY,KAAK,EAAE,EAAE;YACzF,aAAK,CAAC,qBAAqB,CAAC,IAAI,CAAC,QAAQ,EAAE,iBAAO,CAAC,WAAW,EAAE,CAAC,YAAY,CAAC,CAAA;SACjF;IAEL,CAAC;IAzEgB,sBAAsB;QAD1C,OAAO;OACa,sBAAsB,CA0E1C;IAAD,6BAAC;CA1ED,AA0EC,CA1EmD,EAAE,CAAC,SAAS,GA0E/D;kBA1EoB,sBAAsB", "file": "", "sourceRoot": "/", "sourcesContent": ["\n\n\nimport { Publish } from \"../../meshTools/tools/Publish\";\nimport { RoomUser, UserSettlement, PlayerFinalResult } from \"../bean/GameBean\";\nimport { Config } from \"../util/Config\";\nimport NickNameLabel from \"../util/NickNameLabel\";\nimport { Tools } from \"../util/Tools\";\n\nconst { ccclass } = cc._decorator;\n\n@ccclass\nexport default class CongratsItemController extends cc.Component {\n\n    boardIconCrownNode: cc.Node;\n    boardNum: cc.Node;\n    avatarNode: cc.Node;\n    nameNode: cc.Node;\n    stepNode: cc.Node;\n    numberNode: cc.Node;\n    beanIcon: cc.Node;\n\n\n    protected start(): void {\n        this.boardIconCrownNode = this.node.getChildByName('board_icon_crown');//皇冠的节点\n        this.boardNum = this.node.getChildByName('board_num');//皇冠的节点\n        this.avatarNode = this.node.getChildByName('avatar');//头像的节点\n        this.nameNode = this.node.getChildByName('name_layout').getChildByName('name');//名称的节点\n        this.stepNode = this.node.getChildByName(`step_layout`).getChildByName('step');// 步数的节点\n        this.numberNode = this.node.getChildByName('congrats_list_frame').getChildByName('number_view').getChildByName('number');//金豆的节点\n        this.beanIcon = this.node.getChildByName('congrats_list_frame').getChildByName('board_icon_beans')//金豆图标\n\n    }\n\n    //设置数据\n    //settleType:结算类型\n    //intUserID:中断游戏的玩家Id\n    createData(settlement: UserSettlement | PlayerFinalResult, gameUsers: RoomUser[]) {\n\n        if (settlement.rank <= 3) {\n            this.boardIconCrownNode.active = true\n            this.boardNum.active = false\n            //设置皇冠图片\n            switch (settlement.rank) {\n                case 1:\n                    Tools.setNodeSpriteFrame(this.boardIconCrownNode, Config.gameRes + 'board_icon_crown_01');\n                    break;\n                case 2:\n                    Tools.setNodeSpriteFrame(this.boardIconCrownNode, Config.gameRes + 'board_icon_crown_02');\n                    break;\n                case 3:\n                    Tools.setNodeSpriteFrame(this.boardIconCrownNode, Config.gameRes + 'board_icon_crown_03');\n                    break;\n            }\n        } else {\n            this.boardIconCrownNode.active = false\n            this.boardNum.active = true\n            this.boardNum.getComponent(cc.Label).string = settlement.rank + '';//显示名次\n        }\n\n        // 根据数据类型显示不同的分数字段\n        let scoreValue: number;\n        if ('score' in settlement) {\n            // UserSettlement 类型\n            scoreValue = settlement.score;\n        } else {\n            // PlayerFinalResult 类型\n            scoreValue = settlement.totalScore;\n        }\n        this.stepNode.getComponent(cc.Label).string = scoreValue + '';//显示分数\n\n        const index = gameUsers.findIndex((item) => item.userId === settlement.userId);//搜索\n        if (index != -1) {\n            let user = gameUsers[index];\n            Tools.setNodeSpriteFrameUrl(this.avatarNode, user.avatar);\n            this.nameNode.getComponent(NickNameLabel).string = user.nickName\n\n        }\n        this.numberNode.getComponent(cc.Label).string = Tools.NumToTBMK(settlement.coinChg)\n\n\n        if (Publish.GetInstance().currencyIcon != null && Publish.GetInstance().currencyIcon !== '') {\n            Tools.setNodeSpriteFrameUrl(this.beanIcon, Publish.GetInstance().currencyIcon)\n        }\n\n    }\n}\n"]}