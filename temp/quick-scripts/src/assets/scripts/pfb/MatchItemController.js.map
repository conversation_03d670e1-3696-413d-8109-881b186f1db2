{"version": 3, "sources": ["assets/scripts/pfb/MatchItemController.ts"], "names": [], "mappings": ";;;;;AAAA,oBAAoB;AACpB,4EAA4E;AAC5E,mBAAmB;AACnB,sFAAsF;AACtF,8BAA8B;AAC9B,sFAAsF;;;;;;;;;;;;;;;;;;;;;AAGtF,uDAAkD;AAClD,uCAAsC;AAEhC,IAAA,KAAsB,EAAE,CAAC,UAAU,EAAlC,OAAO,aAAA,EAAE,QAAQ,cAAiB,CAAC;AAG1C;IAAiD,uCAAY;IAA7D;QAAA,qEAoCC;QAjCG,YAAM,GAAY,IAAI,CAAC,CAAG,IAAI;QAE9B,qBAAe,GAAY,IAAI,CAAC,CAAG,MAAM;QAEzC,eAAS,GAAa,IAAI,CAAC,CAAE,MAAM;;QA4BnC,iBAAiB;IACrB,CAAC;IAzBG,eAAe;IAEf,mCAAK,GAAL;IAEA,CAAC;IAED,qCAAO,GAAP,UAAQ,IAAc;QAAtB,iBAgBC;QAfG,IAAI,CAAC,YAAY,CAAC;YACd,IAAI,IAAI,IAAI,IAAI,EAAE;gBACd,KAAI,CAAC,eAAe,CAAC,MAAM,GAAG,IAAI,CAAA;gBAClC,KAAI,CAAC,MAAM,CAAC,MAAM,GAAG,KAAK,CAAA;aAE7B;iBAAM;gBAGH,aAAK,CAAC,qBAAqB,CAAC,KAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAA,MAAM;gBAC5D,IAAI,aAAa,GAAG,KAAI,CAAC,SAAS,CAAC,YAAY,CAAC,uBAAa,CAAC,CAAA;gBAC9D,aAAa,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAA,MAAM;gBAC3C,KAAI,CAAC,eAAe,CAAC,MAAM,GAAG,KAAK,CAAA;gBACnC,KAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAA;aAC5B;QACL,CAAC,EAAE,GAAG,CAAC,CAAC;IACZ,CAAC;IA9BD;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;uDACK;IAEvB;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;gEACc;IAEhC;QADC,QAAQ,CAAC,EAAE,CAAC,KAAK,CAAC;0DACQ;IAPV,mBAAmB;QADvC,OAAO;OACa,mBAAmB,CAoCvC;IAAD,0BAAC;CApCD,AAoCC,CApCgD,EAAE,CAAC,SAAS,GAoC5D;kBApCoB,mBAAmB", "file": "", "sourceRoot": "/", "sourcesContent": ["// Learn TypeScript:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html\n// Learn Attribute:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html\n// Learn life-cycle callbacks:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html\n\nimport { RoomUser } from \"../bean/GameBean\";\nimport NickNameLabel from \"../util/NickNameLabel\";\nimport { Tools } from \"../util/Tools\";\n\nconst {ccclass, property} = cc._decorator;\n\n@ccclass\nexport default class MatchItemController extends cc.Component {\n\n    @property(cc.Node)\n    avatar: cc.Node = null;   //头像\n    @property(cc.Node)\n    gundongtouxiang: cc.Node = null;   //滚动头像\n    @property(cc.Label)\n    nameLabel: cc.Label = null;  //用户昵称\n\n\n\n    // onLoad () {}\n\n    start () {\n\n    }\n\n    setData(user: RoomUser){\n        this.scheduleOnce(() => {\n            if (user == null) {\n                this.gundongtouxiang.active = true\n                this.avatar.active = false\n\n            } else {\n               \n\n                Tools.setNodeSpriteFrameUrl(this.avatar, user.avatar);//添加头像\n                let nicknameLabel = this.nameLabel.getComponent(NickNameLabel)\n                nicknameLabel.string = user.nickName;//添加昵称\n                this.gundongtouxiang.active = false\n                this.avatar.active = true\n            }\n        }, 0.1);\n    }\n\n    // update (dt) {}\n}\n"]}