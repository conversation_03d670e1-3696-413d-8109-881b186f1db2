{"version": 3, "sources": ["assets/scripts/pfb/InfoItemOneController.ts"], "names": [], "mappings": ";;;;;AAAA,oBAAoB;AACpB,4EAA4E;AAC5E,mBAAmB;AACnB,sFAAsF;AACtF,8BAA8B;AAC9B,sFAAsF;;;;;;;;;;;;;;;;;;;;;AAEhF,IAAA,KAAsB,EAAE,CAAC,UAAU,EAAlC,OAAO,aAAA,EAAE,QAAQ,cAAiB,CAAC;AAG1C;IAAmD,yCAAY;IAA/D;QAAA,qEAsBC;QAlBG,aAAO,GAAa,IAAI,CAAC;;QAiBzB,iBAAiB;IACrB,CAAC;IAhBG,eAAe;IAEf,qCAAK,GAAL;IAEA,CAAC;IAED,uCAAO,GAAP,UAAQ,OAAc;QAClB,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,OAAO,CAAA;IACjC,CAAC;IAED,0CAAU,GAAV,UAAW,OAAe;QACtB,cAAc;QACd,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;IACtC,CAAC;IAfD;QADC,QAAQ,CAAC,EAAE,CAAC,KAAK,CAAC;0DACM;IAJR,qBAAqB;QADzC,OAAO;OACa,qBAAqB,CAsBzC;IAAD,4BAAC;CAtBD,AAsBC,CAtBkD,EAAE,CAAC,SAAS,GAsB9D;kBAtBoB,qBAAqB", "file": "", "sourceRoot": "/", "sourcesContent": ["// Learn TypeScript:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html\n// Learn Attribute:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html\n// Learn life-cycle callbacks:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html\n\nconst {ccclass, property} = cc._decorator;\n\n@ccclass\nexport default class InfoItemOneController extends cc.Component {\n\n     \n    @property(cc.Label)\n    content: cc.Label = null;\n\n    // onLoad () {}\n\n    start () {\n\n    }\n\n    setData(content:string){\n        this.content.string = content\n    }\n\n    setimgNode(imgNode:cc.Node){\n        // 在第 0 位插入新节点\n        this.node.insertChild(imgNode, 0);\n    }\n\n    // update (dt) {}\n}\n"]}