"use strict";
cc._RF.push(module, '193ceD23EZHLaY3WXjarlZa', 'InfoItemOneController');
// scripts/pfb/InfoItemOneController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var InfoItemOneController = /** @class */ (function (_super) {
    __extends(InfoItemOneController, _super);
    function InfoItemOneController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.content = null;
        return _this;
        // update (dt) {}
    }
    // onLoad () {}
    InfoItemOneController.prototype.start = function () {
    };
    InfoItemOneController.prototype.setData = function (content) {
        this.content.string = content;
    };
    InfoItemOneController.prototype.setimgNode = function (imgNode) {
        // 在第 0 位插入新节点
        this.node.insertChild(imgNode, 0);
    };
    __decorate([
        property(cc.Label)
    ], InfoItemOneController.prototype, "content", void 0);
    InfoItemOneController = __decorate([
        ccclass
    ], InfoItemOneController);
    return InfoItemOneController;
}(cc.Component));
exports.default = InfoItemOneController;

cc._RF.pop();