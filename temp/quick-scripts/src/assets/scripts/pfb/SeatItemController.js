"use strict";
cc._RF.push(module, '282a9v0jv9KApt/IYgxbleE', 'SeatItemController');
// scripts/pfb/SeatItemController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var NickNameLabel_1 = require("../util/NickNameLabel");
var Tools_1 = require("../util/Tools");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var SeatItemController = /** @class */ (function (_super) {
    __extends(SeatItemController, _super);
    function SeatItemController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.avatarFrame = null;
        _this.avatar = null;
        _this.seatEmpty = null;
        _this.nameLabel = null;
        _this.ready = null;
        return _this;
        // update (dt) {}
    }
    // onLoad () {}
    SeatItemController.prototype.start = function () {
    };
    SeatItemController.prototype.setData = function (users) {
        this.users = users;
        if (users == null) {
            this.nameLabel.string = window.getLocalizedStr('Empty');
            this.nameLabel.node.color = cc.Color.fromHEX(new cc.Color(), '#F4C684');
            this.seatEmpty.active = true;
            this.avatarFrame.active = false;
            this.avatar.active = false;
            this.ready.active = false;
        }
        else {
            this.nameLabel.string = users.nickname;
            this.seatEmpty.active = false;
            this.avatarFrame.active = true;
            this.avatar.active = true;
            this.ready.active = users.ready;
            this.nameLabel.node.color = cc.Color.fromHEX(new cc.Color(), '#D07649');
            Tools_1.Tools.setNodeSpriteFrameUrl(this.avatar, users.avatar);
        }
    };
    SeatItemController.prototype.getUsers = function () {
        return this.users;
    };
    __decorate([
        property(cc.Node)
    ], SeatItemController.prototype, "avatarFrame", void 0);
    __decorate([
        property(cc.Node)
    ], SeatItemController.prototype, "avatar", void 0);
    __decorate([
        property(cc.Node)
    ], SeatItemController.prototype, "seatEmpty", void 0);
    __decorate([
        property(NickNameLabel_1.default)
    ], SeatItemController.prototype, "nameLabel", void 0);
    __decorate([
        property(cc.Node)
    ], SeatItemController.prototype, "ready", void 0);
    SeatItemController = __decorate([
        ccclass
    ], SeatItemController);
    return SeatItemController;
}(cc.Component));
exports.default = SeatItemController;

cc._RF.pop();