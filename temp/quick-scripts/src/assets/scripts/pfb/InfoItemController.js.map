{"version": 3, "sources": ["assets/scripts/pfb/InfoItemController.ts"], "names": [], "mappings": ";;;;;AAAA,oBAAoB;AACpB,4EAA4E;AAC5E,mBAAmB;AACnB,sFAAsF;AACtF,8BAA8B;AAC9B,sFAAsF;;;;;;;;;;;;;;;;;;;;;AAEhF,IAAA,KAAwB,EAAE,CAAC,UAAU,EAAnC,OAAO,aAAA,EAAE,QAAQ,cAAkB,CAAC;AAG5C;IAAgD,sCAAY;IAA5D;QAAA,qEAkBC;QAfG,WAAK,GAAa,IAAI,CAAC;;QAcvB,iBAAiB;IACrB,CAAC;IAZG,wBAAwB;IAExB,eAAe;IAEf,kCAAK,GAAL;IAEA,CAAC;IACD,uCAAU,GAAV,UAAW,OAAe;QACtB,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,OAAO,CAAA;IAC/B,CAAC;IAZD;QADC,QAAQ,CAAC,EAAE,CAAC,KAAK,CAAC;qDACI;IAHN,kBAAkB;QADtC,OAAO;OACa,kBAAkB,CAkBtC;IAAD,yBAAC;CAlBD,AAkBC,CAlB+C,EAAE,CAAC,SAAS,GAkB3D;kBAlBoB,kBAAkB", "file": "", "sourceRoot": "/", "sourcesContent": ["// Learn TypeScript:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html\n// Learn Attribute:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html\n// Learn life-cycle callbacks:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html\n\nconst { ccclass, property } = cc._decorator;\n\n@ccclass\nexport default class InfoItemController extends cc.Component {\n\n    @property(cc.Label)\n    label: cc.Label = null;\n\n\n    // LIFE-CYCLE CALLBACKS:\n\n    // onLoad () {}\n\n    start() {\n\n    }\n    setContent(content: string) {\n        this.label.string = content\n    }\n\n    // update (dt) {}\n}\n"]}