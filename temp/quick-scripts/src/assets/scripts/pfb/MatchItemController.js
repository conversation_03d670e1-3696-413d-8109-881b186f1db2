"use strict";
cc._RF.push(module, '17d65tE2S5HU4rq7OdoOrej', 'MatchItemController');
// scripts/pfb/MatchItemController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var NickNameLabel_1 = require("../util/NickNameLabel");
var Tools_1 = require("../util/Tools");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var MatchItemController = /** @class */ (function (_super) {
    __extends(MatchItemController, _super);
    function MatchItemController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.avatar = null; //头像
        _this.gundongtouxiang = null; //滚动头像
        _this.nameLabel = null; //用户昵称
        return _this;
        // update (dt) {}
    }
    // onLoad () {}
    MatchItemController.prototype.start = function () {
    };
    MatchItemController.prototype.setData = function (user) {
        var _this = this;
        this.scheduleOnce(function () {
            if (user == null) {
                _this.gundongtouxiang.active = true;
                _this.avatar.active = false;
            }
            else {
                Tools_1.Tools.setNodeSpriteFrameUrl(_this.avatar, user.avatar); //添加头像
                var nicknameLabel = _this.nameLabel.getComponent(NickNameLabel_1.default);
                nicknameLabel.string = user.nickName; //添加昵称
                _this.gundongtouxiang.active = false;
                _this.avatar.active = true;
            }
        }, 0.1);
    };
    __decorate([
        property(cc.Node)
    ], MatchItemController.prototype, "avatar", void 0);
    __decorate([
        property(cc.Node)
    ], MatchItemController.prototype, "gundongtouxiang", void 0);
    __decorate([
        property(cc.Label)
    ], MatchItemController.prototype, "nameLabel", void 0);
    MatchItemController = __decorate([
        ccclass
    ], MatchItemController);
    return MatchItemController;
}(cc.Component));
exports.default = MatchItemController;

cc._RF.pop();