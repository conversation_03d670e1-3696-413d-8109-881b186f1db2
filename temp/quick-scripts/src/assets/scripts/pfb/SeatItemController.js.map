{"version": 3, "sources": ["assets/scripts/pfb/SeatItemController.ts"], "names": [], "mappings": ";;;;;AAAA,oBAAoB;AACpB,4EAA4E;AAC5E,mBAAmB;AACnB,sFAAsF;AACtF,8BAA8B;AAC9B,sFAAsF;;;;;;;;;;;;;;;;;;;;;AAGtF,uDAAkD;AAClD,uCAAsC;AAEhC,IAAA,KAAsB,EAAE,CAAC,UAAU,EAAlC,OAAO,aAAA,EAAE,QAAQ,cAAiB,CAAC;AAG1C;IAAgD,sCAAY;IAA5D;QAAA,qEA6CC;QA1CG,iBAAW,GAAY,IAAI,CAAC;QAE5B,YAAM,GAAY,IAAI,CAAC;QAEvB,eAAS,GAAY,IAAI,CAAC;QAE1B,eAAS,GAAkB,IAAI,CAAC;QAEhC,WAAK,GAAY,IAAI,CAAC;;QAiCtB,iBAAiB;IACrB,CAAC;IA9BG,eAAe;IAEf,kCAAK,GAAL;IAEA,CAAC;IAED,oCAAO,GAAP,UAAQ,KAAiB;QACrB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAA;QAClB,IAAI,KAAK,IAAI,IAAI,EAAE;YACf,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,MAAM,CAAC,eAAe,CAAC,OAAO,CAAC,CAAA;YACvD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,EAAC,SAAS,CAAC,CAAC;YACvE,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC;YAC7B,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,KAAK,CAAC;YAChC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,KAAK,CAAC;YAC3B,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC;SAC7B;aAAM;YACH,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,KAAK,CAAC,QAAQ,CAAA;YACtC,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,KAAK,CAAC;YAC9B,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,IAAI,CAAC;YAC/B,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC;YAC1B,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,KAAK,CAAA;YAC/B,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,EAAC,SAAS,CAAC,CAAC;YACvE,aAAK,CAAC,qBAAqB,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,CAAA;SACzD;IACL,CAAC;IACD,qCAAQ,GAAR;QACI,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IAvCD;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;2DACU;IAE5B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;sDACK;IAEvB;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;yDACQ;IAE1B;QADC,QAAQ,CAAC,uBAAa,CAAC;yDACQ;IAEhC;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;qDACI;IAXL,kBAAkB;QADtC,OAAO;OACa,kBAAkB,CA6CtC;IAAD,yBAAC;CA7CD,AA6CC,CA7C+C,EAAE,CAAC,SAAS,GA6C3D;kBA7CoB,kBAAkB", "file": "", "sourceRoot": "/", "sourcesContent": ["// Learn TypeScript:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html\n// Learn Attribute:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html\n// Learn life-cycle callbacks:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html\n\nimport { InviteUser, RoomUser } from \"../bean/GameBean\";\nimport NickNameLabel from \"../util/NickNameLabel\";\nimport { Tools } from \"../util/Tools\";\n\nconst {ccclass, property} = cc._decorator;\n\n@ccclass\nexport default class SeatItemController extends cc.Component {\n\n    @property(cc.Node)\n    avatarFrame: cc.Node = null;\n    @property(cc.Node)\n    avatar: cc.Node = null;\n    @property(cc.Node)\n    seatEmpty: cc.Node = null;\n    @property(NickNameLabel)\n    nameLabel: NickNameLabel = null;\n    @property(cc.Node)\n    ready: cc.Node = null;\n\n    users: InviteUser\n\n    // onLoad () {}\n\n    start () {\n\n    }\n\n    setData(users: InviteUser) {\n        this.users = users\n        if (users == null) {\n            this.nameLabel.string = window.getLocalizedStr('Empty')\n            this.nameLabel.node.color = cc.Color.fromHEX(new cc.Color(),'#F4C684');\n            this.seatEmpty.active = true;\n            this.avatarFrame.active = false;\n            this.avatar.active = false;\n            this.ready.active = false;\n        } else {\n            this.nameLabel.string = users.nickname\n            this.seatEmpty.active = false;\n            this.avatarFrame.active = true;\n            this.avatar.active = true;\n            this.ready.active = users.ready\n            this.nameLabel.node.color = cc.Color.fromHEX(new cc.Color(),'#D07649');\n            Tools.setNodeSpriteFrameUrl(this.avatar, users.avatar)\n        }\n    }\n    getUsers(){\n        return this.users;\n    }\n\n    // update (dt) {}\n}\n"]}