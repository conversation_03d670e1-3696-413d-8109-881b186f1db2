{"version": 3, "sources": ["assets/scripts/ToastController.ts"], "names": [], "mappings": ";;;;;AAAA,oBAAoB;AACpB,4EAA4E;AAC5E,mBAAmB;AACnB,sFAAsF;AACtF,8BAA8B;AAC9B,sFAAsF;;;;;;;;;;;;;;;;;;;;;AAEhF,IAAA,KAAsB,EAAE,CAAC,UAAU,EAAlC,OAAO,aAAA,EAAE,QAAQ,cAAiB,CAAC;AAG1C;IAA6C,mCAAY;IAAzD;QAAA,qEAiCC;QA9BG,aAAO,GAAY,IAAI,CAAC;QAGxB,gBAAU,GAAa,IAAI,CAAC;QAE5B,YAAM,GAAW,KAAK,CAAA;;IAyB1B,CAAC;IAvBG,eAAe;IAEf,+BAAK,GAAL;IAEA,CAAC;IAED,QAAQ;IACR,qCAAW,GAAX,UAAY,OAAe;QACvB,IAAG,IAAI,CAAC,MAAM,EAAC,EAAC,oCAAoC;YACjD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;SACnC;QACD,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,OAAO,CAAA;QAChC,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAA;QAC1B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAA;QAElB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC,CAAA;IAC5C,CAAC;IAED,qCAAW,GAAX;QACI,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,KAAK,CAAA;QAC3B,IAAI,CAAC,MAAM,GAAG,KAAK,CAAA;QACnB,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,EAAE,CAAA;IAC/B,CAAC;IA7BD;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;oDACM;IAGxB;QADC,QAAQ,CAAC,EAAE,CAAC,KAAK,CAAC;uDACS;IANX,eAAe;QADnC,OAAO;OACa,eAAe,CAiCnC;IAAD,sBAAC;CAjCD,AAiCC,CAjC4C,EAAE,CAAC,SAAS,GAiCxD;kBAjCoB,eAAe", "file": "", "sourceRoot": "/", "sourcesContent": ["// Learn TypeScript:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html\n// Learn Attribute:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html\n// Learn life-cycle callbacks:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html\n\nconst {ccclass, property} = cc._decorator;\n\n@ccclass\nexport default class ToastController extends cc.Component {\n\n    @property(cc.Node)\n    alertBg: cc.Node = null;\n\n    @property(cc.Label)\n    toastLabel: cc.Label = null;\n\n    isShow:boolean = false\n\n    // onLoad () {}\n\n    start () {\n\n    }\n\n    //设置提示文案\n    showContent(content: string) {\n        if(this.isShow){//判断当前是否正在显示提示文案  如果显示的话 就把上一个的倒计时关掉\n           this.unschedule(this.hideContent) \n        }\n        this.toastLabel.string = content\n        this.alertBg.active = true\n        this.isShow = true\n        \n        this.scheduleOnce(this.hideContent, 1.5)\n    }\n\n    hideContent() {\n        this.alertBg.active = false\n        this.isShow = false\n        this.toastLabel.string = ''\n    }\n}\n"]}