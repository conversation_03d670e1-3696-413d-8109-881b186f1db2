{"version": 3, "sources": ["assets/scripts/game/Chess/GridController.ts"], "names": [], "mappings": ";;;;;AAAA,oBAAoB;AACpB,4EAA4E;AAC5E,mBAAmB;AACnB,sFAAsF;AACtF,8BAA8B;AAC9B,sFAAsF;;;;;;;;;;;;;;;;;;;;;AAEhF,IAAA,KAAsB,EAAE,CAAC,UAAU,EAAlC,OAAO,aAAA,EAAE,QAAQ,cAAiB,CAAC;AAG1C;IAA4C,kCAAY;IAAxD;QAAA,qEA8HC;QA3HG,sBAAgB,GAAc,IAAI,CAAC,CAAE,OAAO;QAG5C,iBAAW,GAAmB,IAAI,CAAC,CAAE,WAAW;QAGhD,gBAAU,GAAmB,IAAI,CAAC,CAAE,WAAW;QAG/C,mBAAa,GAAmB,IAAI,CAAC,CAAE,YAAY;QAEnD,OAAO;QACA,WAAK,GAAW,CAAC,CAAC;QAClB,WAAK,GAAW,CAAC,CAAC;QAEzB,OAAO;QACA,gBAAU,GAAY,KAAK,CAAC;QAC5B,gBAAU,GAAY,IAAI,CAAC;QAElC,OAAO;QACA,yBAAmB,GAAmC,IAAI,CAAC;;QAsGlE,iBAAiB;IACrB,CAAC;IArGG,+BAAM,GAAN;QACI,SAAS;QACT,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;QACrE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QACjE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;QACvE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;QACrE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;IACzE,CAAC;IAED,8BAAK,GAAL;QACI,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC7B,CAAC;IAED,SAAS;IACF,0CAAiB,GAAxB,UAAyB,CAAS,EAAE,CAAS;QACzC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;QACf,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;QACf,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,UAAQ,CAAC,SAAI,CAAG,CAAC;IACtC,CAAC;IAED,SAAS;IACF,yCAAgB,GAAvB,UAAwB,QAAwC;QAC5D,IAAI,CAAC,mBAAmB,GAAG,QAAQ,CAAC;IACxC,CAAC;IAED,OAAO;IACC,qCAAY,GAApB,UAAqB,KAA0B;QAC3C,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,gBAAgB,EAAE;YAC9D,IAAI,CAAC,gBAAgB,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC;SACvD;IACL,CAAC;IAED,OAAO;IACC,mCAAU,GAAlB,UAAmB,KAA0B;QACzC,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAEzB,SAAS;QACT,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1B,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;SACpD;IACL,CAAC;IAED,OAAO;IACC,sCAAa,GAArB,UAAsB,KAA0B;QAC5C,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC7B,CAAC;IAED,OAAO;IACC,qCAAY,GAApB,UAAqB,KAA0B;QAC3C,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,gBAAgB,EAAE;YAC9D,IAAI,CAAC,gBAAgB,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC;SACvD;IACL,CAAC;IAED,OAAO;IACC,qCAAY,GAApB,UAAqB,KAA0B;QAC3C,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC7B,CAAC;IAED,SAAS;IACD,0CAAiB,GAAzB;QACI,IAAI,CAAC,IAAI,CAAC,gBAAgB;YAAE,OAAO;QAEnC,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,aAAa,EAAE;YACvC,IAAI,CAAC,gBAAgB,CAAC,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC;SAC1D;aAAM,IAAI,IAAI,CAAC,WAAW,EAAE;YACzB,IAAI,CAAC,gBAAgB,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;SACxD;IACL,CAAC;IAED,SAAS;IACF,oCAAW,GAAlB,UAAmB,QAAiB,EAAE,UAAoB;QACtD,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC;QAC3B,IAAI,CAAC,UAAU,GAAG,UAAU,IAAI,IAAI,CAAC;QACrC,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC7B,CAAC;IAED,WAAW;IACJ,yCAAgB,GAAvB;QACI,OAAO,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACzD,CAAC;IAED,OAAO;IACA,oCAAW,GAAlB;QACI,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC;YACnC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;SAC1B;QACD,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;IAC5B,CAAC;IAED,kCAAS,GAAT;QACI,SAAS;QACT,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;QACtE,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QAClE,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;QACxE,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;QACtE,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;IAC1E,CAAC;IAxHD;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;4DACe;IAGnC;QADC,QAAQ,CAAC,EAAE,CAAC,WAAW,CAAC;uDACU;IAGnC;QADC,QAAQ,CAAC,EAAE,CAAC,WAAW,CAAC;sDACS;IAGlC;QADC,QAAQ,CAAC,EAAE,CAAC,WAAW,CAAC;yDACY;IAZpB,cAAc;QADlC,OAAO;OACa,cAAc,CA8HlC;IAAD,qBAAC;CA9HD,AA8HC,CA9H2C,EAAE,CAAC,SAAS,GA8HvD;kBA9HoB,cAAc", "file": "", "sourceRoot": "/", "sourcesContent": ["// Learn TypeScript:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html\n// Learn Attribute:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html\n// Learn life-cycle callbacks:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html\n\nconst {ccclass, property} = cc._decorator;\n\n@ccclass\nexport default class GridController extends cc.Component {\n\n    @property(cc.Sprite)\n    backgroundSprite: cc.Sprite = null;  // 格子背景\n\n    @property(cc.SpriteFrame)\n    normalFrame: cc.SpriteFrame = null;  // 正常状态的背景图\n\n    @property(cc.SpriteFrame)\n    hoverFrame: cc.SpriteFrame = null;  // 悬停状态的背景图\n\n    @property(cc.SpriteFrame)\n    occupiedFrame: cc.SpriteFrame = null;  // 已占用状态的背景图\n\n    // 格子坐标\n    public gridX: number = 0;\n    public gridY: number = 0;\n\n    // 格子状态\n    public isOccupied: boolean = false;\n    public playerNode: cc.Node = null;\n\n    // 回调函数\n    public onGridClickCallback: (x: number, y: number) => void = null;\n\n    onLoad() {\n        // 添加触摸事件\n        this.node.on(cc.Node.EventType.TOUCH_START, this.onTouchStart, this);\n        this.node.on(cc.Node.EventType.TOUCH_END, this.onTouchEnd, this);\n        this.node.on(cc.Node.EventType.TOUCH_CANCEL, this.onTouchCancel, this);\n        this.node.on(cc.Node.EventType.MOUSE_ENTER, this.onMouseEnter, this);\n        this.node.on(cc.Node.EventType.MOUSE_LEAVE, this.onMouseLeave, this);\n    }\n\n    start() {\n        this.updateVisualState();\n    }\n\n    // 设置格子坐标\n    public setGridCoordinate(x: number, y: number) {\n        this.gridX = x;\n        this.gridY = y;\n        this.node.name = `Grid_${x}_${y}`;\n    }\n\n    // 设置点击回调\n    public setClickCallback(callback: (x: number, y: number) => void) {\n        this.onGridClickCallback = callback;\n    }\n\n    // 触摸开始\n    private onTouchStart(event: cc.Event.EventTouch) {\n        if (!this.isOccupied && this.hoverFrame && this.backgroundSprite) {\n            this.backgroundSprite.spriteFrame = this.hoverFrame;\n        }\n    }\n\n    // 触摸结束\n    private onTouchEnd(event: cc.Event.EventTouch) {\n        this.updateVisualState();\n        \n        // 触发点击回调\n        if (this.onGridClickCallback) {\n            this.onGridClickCallback(this.gridX, this.gridY);\n        }\n    }\n\n    // 触摸取消\n    private onTouchCancel(event: cc.Event.EventTouch) {\n        this.updateVisualState();\n    }\n\n    // 鼠标进入\n    private onMouseEnter(event: cc.Event.EventMouse) {\n        if (!this.isOccupied && this.hoverFrame && this.backgroundSprite) {\n            this.backgroundSprite.spriteFrame = this.hoverFrame;\n        }\n    }\n\n    // 鼠标离开\n    private onMouseLeave(event: cc.Event.EventMouse) {\n        this.updateVisualState();\n    }\n\n    // 更新视觉状态\n    private updateVisualState() {\n        if (!this.backgroundSprite) return;\n\n        if (this.isOccupied && this.occupiedFrame) {\n            this.backgroundSprite.spriteFrame = this.occupiedFrame;\n        } else if (this.normalFrame) {\n            this.backgroundSprite.spriteFrame = this.normalFrame;\n        }\n    }\n\n    // 设置占用状态\n    public setOccupied(occupied: boolean, playerNode?: cc.Node) {\n        this.isOccupied = occupied;\n        this.playerNode = playerNode || null;\n        this.updateVisualState();\n    }\n\n    // 获取格子世界坐标\n    public getWorldPosition(): cc.Vec2 {\n        return this.node.convertToWorldSpaceAR(cc.Vec2.ZERO);\n    }\n\n    // 清除玩家\n    public clearPlayer() {\n        if (this.playerNode) {\n            this.playerNode.removeFromParent();\n            this.playerNode = null;\n        }\n        this.setOccupied(false);\n    }\n\n    onDestroy() {\n        // 清理事件监听\n        this.node.off(cc.Node.EventType.TOUCH_START, this.onTouchStart, this);\n        this.node.off(cc.Node.EventType.TOUCH_END, this.onTouchEnd, this);\n        this.node.off(cc.Node.EventType.TOUCH_CANCEL, this.onTouchCancel, this);\n        this.node.off(cc.Node.EventType.MOUSE_ENTER, this.onMouseEnter, this);\n        this.node.off(cc.Node.EventType.MOUSE_LEAVE, this.onMouseLeave, this);\n    }\n\n    // update (dt) {}\n}\n"]}