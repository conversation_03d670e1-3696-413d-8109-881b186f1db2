"use strict";
cc._RF.push(module, '477f5VfSppO37TQXXq0KN4Z', 'GridController');
// scripts/game/Chess/GridController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var GridController = /** @class */ (function (_super) {
    __extends(GridController, _super);
    function GridController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.backgroundSprite = null; // 格子背景
        _this.normalFrame = null; // 正常状态的背景图
        _this.hoverFrame = null; // 悬停状态的背景图
        _this.occupiedFrame = null; // 已占用状态的背景图
        // 格子坐标
        _this.gridX = 0;
        _this.gridY = 0;
        // 格子状态
        _this.isOccupied = false;
        _this.playerNode = null;
        // 回调函数
        _this.onGridClickCallback = null;
        return _this;
        // update (dt) {}
    }
    GridController.prototype.onLoad = function () {
        // 添加触摸事件
        this.node.on(cc.Node.EventType.TOUCH_START, this.onTouchStart, this);
        this.node.on(cc.Node.EventType.TOUCH_END, this.onTouchEnd, this);
        this.node.on(cc.Node.EventType.TOUCH_CANCEL, this.onTouchCancel, this);
        this.node.on(cc.Node.EventType.MOUSE_ENTER, this.onMouseEnter, this);
        this.node.on(cc.Node.EventType.MOUSE_LEAVE, this.onMouseLeave, this);
    };
    GridController.prototype.start = function () {
        this.updateVisualState();
    };
    // 设置格子坐标
    GridController.prototype.setGridCoordinate = function (x, y) {
        this.gridX = x;
        this.gridY = y;
        this.node.name = "Grid_" + x + "_" + y;
    };
    // 设置点击回调
    GridController.prototype.setClickCallback = function (callback) {
        this.onGridClickCallback = callback;
    };
    // 触摸开始
    GridController.prototype.onTouchStart = function (event) {
        if (!this.isOccupied && this.hoverFrame && this.backgroundSprite) {
            this.backgroundSprite.spriteFrame = this.hoverFrame;
        }
    };
    // 触摸结束
    GridController.prototype.onTouchEnd = function (event) {
        this.updateVisualState();
        // 触发点击回调
        if (this.onGridClickCallback) {
            this.onGridClickCallback(this.gridX, this.gridY);
        }
    };
    // 触摸取消
    GridController.prototype.onTouchCancel = function (event) {
        this.updateVisualState();
    };
    // 鼠标进入
    GridController.prototype.onMouseEnter = function (event) {
        if (!this.isOccupied && this.hoverFrame && this.backgroundSprite) {
            this.backgroundSprite.spriteFrame = this.hoverFrame;
        }
    };
    // 鼠标离开
    GridController.prototype.onMouseLeave = function (event) {
        this.updateVisualState();
    };
    // 更新视觉状态
    GridController.prototype.updateVisualState = function () {
        if (!this.backgroundSprite)
            return;
        if (this.isOccupied && this.occupiedFrame) {
            this.backgroundSprite.spriteFrame = this.occupiedFrame;
        }
        else if (this.normalFrame) {
            this.backgroundSprite.spriteFrame = this.normalFrame;
        }
    };
    // 设置占用状态
    GridController.prototype.setOccupied = function (occupied, playerNode) {
        this.isOccupied = occupied;
        this.playerNode = playerNode || null;
        this.updateVisualState();
    };
    // 获取格子世界坐标
    GridController.prototype.getWorldPosition = function () {
        return this.node.convertToWorldSpaceAR(cc.Vec2.ZERO);
    };
    // 清除玩家
    GridController.prototype.clearPlayer = function () {
        if (this.playerNode) {
            this.playerNode.removeFromParent();
            this.playerNode = null;
        }
        this.setOccupied(false);
    };
    GridController.prototype.onDestroy = function () {
        // 清理事件监听
        this.node.off(cc.Node.EventType.TOUCH_START, this.onTouchStart, this);
        this.node.off(cc.Node.EventType.TOUCH_END, this.onTouchEnd, this);
        this.node.off(cc.Node.EventType.TOUCH_CANCEL, this.onTouchCancel, this);
        this.node.off(cc.Node.EventType.MOUSE_ENTER, this.onMouseEnter, this);
        this.node.off(cc.Node.EventType.MOUSE_LEAVE, this.onMouseLeave, this);
    };
    __decorate([
        property(cc.Sprite)
    ], GridController.prototype, "backgroundSprite", void 0);
    __decorate([
        property(cc.SpriteFrame)
    ], GridController.prototype, "normalFrame", void 0);
    __decorate([
        property(cc.SpriteFrame)
    ], GridController.prototype, "hoverFrame", void 0);
    __decorate([
        property(cc.SpriteFrame)
    ], GridController.prototype, "occupiedFrame", void 0);
    GridController = __decorate([
        ccclass
    ], GridController);
    return GridController;
}(cc.Component));
exports.default = GridController;

cc._RF.pop();