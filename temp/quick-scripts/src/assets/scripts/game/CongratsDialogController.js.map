{"version": 3, "sources": ["assets/scripts/game/CongratsDialogController.ts"], "names": [], "mappings": ";;;;;AAAA,oBAAoB;AACpB,4EAA4E;AAC5E,mBAAmB;AACnB,sFAAsF;AACtF,8BAA8B;AAC9B,sFAAsF;;;;;;;;;;;;;;;;;;;;;AAItF,iDAAgD;AAChD,qDAAkD;AAClD,6CAA4C;AAC5C,0DAAwE;AACxE,wEAAmE;AACnE,yCAAwC;AACxC,uCAAsC;AAEhC,IAAA,KAAwB,EAAE,CAAC,UAAU,EAAnC,OAAO,aAAA,EAAE,QAAQ,cAAkB,CAAC;AAE5C,MAAM;AAEN;IAAsD,4CAAY;IAAlE;QAAA,qEAwIC;QArIG,aAAO,GAAY,IAAI,CAAA;QAEvB,gBAAU,GAAY,IAAI,CAAA;QAE1B,aAAO,GAAY,IAAI,CAAA;QAEvB,kBAAY,GAAc,IAAI,CAAC,CAAA,UAAU;QAElC,gBAAU,GAAY,IAAI,CAAC,CAAA,SAAS;QAG3C,wBAAkB,GAAa,IAAI,CAAA;QACnC,uBAAiB,GAAW,IAAI,CAAC,CAAA,SAAS;QAE1C,kBAAY,GAAa,IAAI,CAAA,CAAC,SAAS;QACvC,aAAO,GAAW,EAAE,CAAC,CAAA,UAAU;;IAsHnC,CAAC;IAnHG,yCAAM,GAAN;QACI,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,kBAAkB,CAAC,CAAC,YAAY,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;IACrG,CAAC;IACS,2CAAQ,GAAlB;QACI,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACxC,aAAK,CAAC,qBAAqB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;IAC7C,CAAC;IAED,wCAAK,GAAL;QAAA,iBAKC;QAJG,gBAAgB;QAChB,aAAK,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,EAAE;YAC5B,KAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACnB,CAAC,CAAC,CAAA;IACN,CAAC;IAGD,uCAAI,GAAJ,UAAK,gBAAkC,EAAE,YAAsB;QAC3D,IAAI,CAAC,YAAY,GAAG,YAAY,CAAA;QAChC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAA;QACvB,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,CAAC,CAAA;QACtB,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAA;QAC/B,SAAS;QACT,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC;aACjB,EAAE,CAAC,eAAM,CAAC,eAAe,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;aACxC,KAAK,EAAE,CAAC;IACjB,CAAC;IAED,2CAAQ,GAAR,UAAS,gBAAkC;QACvC,sCAAsC;QACtC,IAAI,QAAQ,GAAG,gBAAgB,CAAC,YAAY,IAAI,gBAAgB,CAAC,KAAK,CAAC;QAEvE,aAAa;QACb,IAAI,CAAC,gBAAgB,IAAI,CAAC,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAC5D,OAAO,CAAC,IAAI,CAAC,0BAA0B,EAAE,gBAAgB,CAAC,CAAC;YAC3D,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU;YACnC,OAAO;SACV;QAED,IAAM,aAAa,GAAG,uBAAU,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC;QACzE,IAAM,KAAK,GAAG,QAAQ,CAAC,SAAS,CAAC,UAAC,IAAI,IAAK,OAAA,IAAI,CAAC,MAAM,KAAK,aAAa,EAA7B,CAA6B,CAAC,CAAC,CAAA,IAAI;QAC9E,IAAI,KAAK,IAAI,CAAC,EAAE;YACZ,2CAA2C;YAC3C,IAAI,MAAM,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;gBAC3B,iCAAiC;gBACjC,uBAAU,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,GAAI,QAAQ,CAAC,KAAK,CAAS,CAAC,IAAI,CAAC;aACpF;iBAAM,IAAI,SAAS,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;gBACrC,yCAAyC;gBACzC,uBAAU,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,IAAK,QAAQ,CAAC,KAAK,CAAS,CAAC,OAAO,CAAC;aACxF;SACJ;QAED,IAAI,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAC;gCAC3B,CAAC;YACN,IAAM,IAAI,GAAG,EAAE,CAAC,WAAW,CAAC,OAAK,YAAY,CAAC,CAAC;YAC/C,IAAM,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;YACzB,OAAK,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAC/B,UAAU,CAAC;gBACP,IAAI,CAAC,YAAY,CAAC,gCAAsB,CAAC,CAAC,UAAU,CAAC,IAAI,EAAE,uBAAU,CAAC,WAAW,EAAE,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;YAC/G,CAAC,EAAE,GAAG,CAAC,CAAC;;;QANZ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,EAAE,CAAC;oBAA/B,CAAC;SAOT;QACD,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC,CAAA,CAAA,UAAU;IACrC,CAAC;IAED,oBAAoB;IACpB,uCAAI,GAAJ,UAAK,IAAqB;QAA1B,iBAoBC;QApBI,qBAAA,EAAA,YAAqB;QACtB,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,YAAY,EAAE,CAAA;SACtB;QACD,iBAAO,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;QAC7B,SAAS;QACT,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC;aACjB,EAAE,CAAC,eAAM,CAAC,eAAe,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;aACxC,IAAI,CAAC;YACF,KAAI,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAA;YACxB,IAAI,IAAI,EAAE;gBACN,uBAAU,CAAC,WAAW,EAAE,CAAC,SAAS,EAAE,CAAA;gBACpC,IAAI,eAAe,GAAoB;oBACnC,OAAO,EAAE,+BAAa,CAAC,YAAY;oBACnC,MAAM,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,CAAA,WAAW;iBACnC,CAAA;gBACD,iBAAO,CAAC,KAAK,CAAC,IAAI,CAAC,uBAAS,CAAC,WAAW,EAAE,eAAe,CAAC,CAAC;aAC9D;QACL,CAAC,CAAC;aACD,KAAK,EAAE,CAAC;IACjB,CAAC;IACS,4CAAS,GAAnB;QACI,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,aAAa,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;SACzC;IAEL,CAAC;IAED,iDAAc,GAAd,UAAe,OAAe;QAA9B,iBAgBC;QAfG,IAAI,gBAAgB,GAAG,OAAO,CAAC;QAC/B,IAAI,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,CAAC;QAE5C,IAAI,CAAC,iBAAiB,GAAG,WAAW,CAAC;YACjC,gBAAgB,EAAE,CAAC;YAEnB,IAAI,gBAAgB,IAAI,CAAC,EAAE;gBACvB,aAAa,CAAC,KAAI,CAAC,iBAAiB,CAAC,CAAC;gBACtC,KAAI,CAAC,iBAAiB,GAAG,IAAI,CAAA;gBAC7B,cAAc;gBACd,KAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;gBACf,OAAM;aACT;YACD,KAAI,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,CAAC;QAChD,CAAC,EAAE,IAAI,CAAC,CAAC;IACb,CAAC;IAED,uDAAoB,GAApB,UAAqB,OAAe;QAChC,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAG,WAAI,OAAO,YAAI,CAAC;SACpD;IACL,CAAC;IApID;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;6DACK;IAEvB;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;gEACQ;IAE1B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;6DACK;IAEvB;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;kEACW;IAE/B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;gEACgB;IAXjB,wBAAwB;QAD5C,OAAO;OACa,wBAAwB,CAwI5C;IAAD,+BAAC;CAxID,AAwIC,CAxIqD,EAAE,CAAC,SAAS,GAwIjE;kBAxIoB,wBAAwB", "file": "", "sourceRoot": "/", "sourcesContent": ["// Learn TypeScript:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html\n// Learn Attribute:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html\n// Learn life-cycle callbacks:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html\n\n\nimport { NoticeSettlement } from \"../bean/GameBean\";\nimport { GlobalBean } from \"../bean/GlobalBean\";\nimport { EventType } from \"../common/EventCenter\";\nimport { GameMgr } from \"../common/GameMgr\";\nimport { AutoMessageBean, AutoMessageId } from \"../net/MessageBaseBean\";\nimport CongratsItemController from \"../pfb/CongratsItemController\";\nimport { Config } from \"../util/Config\";\nimport { Tools } from \"../util/Tools\";\n\nconst { ccclass, property } = cc._decorator;\n\n//结算页面\n@ccclass\nexport default class CongratsDialogController extends cc.Component {\n\n    @property(cc.Node)\n    boardBg: cc.Node = null\n    @property(cc.Node)\n    contentLay: cc.Node = null\n    @property(cc.Node)\n    backBtn: cc.Node = null\n    @property(cc.Prefab)\n    congratsItem: cc.Prefab = null;//列表的 item\n    @property(cc.Node)\n    public layoutNode: cc.Node = null;//存放列表的布局\n\n\n    countdownTimeLabel: cc.Label = null\n    countdownInterval: number = null;//倒计时的 id\n\n    backCallback: Function = null //隐藏弹窗的回调\n    seconds: number = 10;//倒计时 10 秒\n\n\n    onLoad() {\n        this.countdownTimeLabel = this.backBtn.getChildByName('buttonLabel_time').getComponent(cc.Label);\n    }\n    protected onEnable(): void {\n        this.updateCountdownLabel(this.seconds);\n        Tools.setCountDownTimeLabel(this.backBtn)\n    }\n\n    start() {\n        //backBtn 按钮点击事件\n        Tools.greenButton(this.backBtn, () => {\n            this.hide(true)\n        })\n    }\n\n\n    show(noticeSettlement: NoticeSettlement, backCallback: Function) {\n        this.backCallback = backCallback\n        this.node.active = true\n        this.boardBg.scale = 0\n        this._setData(noticeSettlement)\n        // 执行缩放动画\n        cc.tween(this.boardBg)\n            .to(Config.dialogScaleTime, { scale: 1 })\n            .start();\n    }\n\n    _setData(noticeSettlement: NoticeSettlement) {\n        // 获取用户列表，优先使用 finalRanking，其次使用 users\n        let userList = noticeSettlement.finalRanking || noticeSettlement.users;\n\n        // 检查用户列表是否存在\n        if (!noticeSettlement || !userList || !Array.isArray(userList)) {\n            console.warn('NoticeSettlement 用户数据无效:', noticeSettlement);\n            this.startCountdown(10); // 仍然启动倒计时\n            return;\n        }\n\n        const currentUserId = GlobalBean.GetInstance().loginData.userInfo.userId;\n        const index = userList.findIndex((item) => item.userId === currentUserId);//搜索\n        if (index >= 0) {\n            // 对于扫雷游戏，finalRanking 中有 coinChg 字段，需要更新金币\n            if ('coin' in userList[index]) {\n                // UserSettlement 类型，直接使用 coin 字段\n                GlobalBean.GetInstance().loginData.userInfo.coin = (userList[index] as any).coin;\n            } else if ('coinChg' in userList[index]) {\n                // PlayerFinalResult 类型，使用 coinChg 字段更新金币\n                GlobalBean.GetInstance().loginData.userInfo.coin += (userList[index] as any).coinChg;\n            }\n        }\n\n        this.layoutNode.removeAllChildren();\n        for (let i = 0; i < userList.length; ++i) {\n            const item = cc.instantiate(this.congratsItem);\n            const data = userList[i];\n            this.layoutNode.addChild(item);\n            setTimeout(() => {\n                item.getComponent(CongratsItemController).createData(data, GlobalBean.GetInstance().noticeStartGame.users);\n            }, 100);\n        }\n        this.startCountdown(10)//倒计时 10 秒\n    }\n\n    // bool 在隐藏的时候是否返回大厅\n    hide(bool: boolean = false) {\n        if (this.backCallback) {\n            this.backCallback()\n        }\n        GameMgr.Console.Log('隐藏结算页面')\n        // 执行缩放动画\n        cc.tween(this.boardBg)\n            .to(Config.dialogScaleTime, { scale: 0 })\n            .call(() => {\n                this.node.active = false\n                if (bool) {\n                    GlobalBean.GetInstance().cleanData()\n                    let autoMessageBean: AutoMessageBean = {\n                        'msgId': AutoMessageId.JumpHallPage,//跳转进大厅页面\n                        'data': { 'type': 2 }//2是结算弹窗跳转的\n                    }\n                    GameMgr.Event.Send(EventType.AutoMessage, autoMessageBean);\n                }\n            })\n            .start();\n    }\n    protected onDisable(): void {\n        if (this.countdownInterval) {\n            clearInterval(this.countdownInterval);\n        }\n\n    }\n\n    startCountdown(seconds: number) {\n        let remainingSeconds = seconds;\n        this.updateCountdownLabel(remainingSeconds);\n\n        this.countdownInterval = setInterval(() => {\n            remainingSeconds--;\n\n            if (remainingSeconds <= 0) {\n                clearInterval(this.countdownInterval);\n                this.countdownInterval = null\n                // 倒计时结束时的处理逻辑\n                this.hide(true)\n                return\n            }\n            this.updateCountdownLabel(remainingSeconds);\n        }, 1000);\n    }\n\n    updateCountdownLabel(seconds: number) {\n        if (this.countdownTimeLabel) {\n            this.countdownTimeLabel.string = `（${seconds}s）`;\n        }\n    }\n}\n"]}