"use strict";
cc._RF.push(module, 'c27f8F0/5lGDbDLReB1H1g7', 'GamePageInitializer');
// scripts/game/GamePageInitializer.ts

"use strict";
// 游戏页面初始化器 - 确保GamePageController被正确初始化
// 这个脚本可以附加到任何节点上，它会自动创建和初始化GamePageController
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var GamePageController_1 = require("./GamePageController");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var GamePageInitializer = /** @class */ (function (_super) {
    __extends(GamePageInitializer, _super);
    function GamePageInitializer() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    GamePageInitializer.prototype.onLoad = function () {
        console.log("=== GamePageInitializer onLoad 开始 ===");
        // 检查是否已经有GamePageController实例
        if (window.gamePageController) {
            console.log("GamePageController 实例已存在");
            return;
        }
        // 查找或创建GamePageController
        this.initializeGamePageController();
        console.log("=== GamePageInitializer onLoad 完成 ===");
    };
    GamePageInitializer.prototype.initializeGamePageController = function () {
        // 首先尝试在场景中查找现有的GamePageController
        var existingController = cc.find("Canvas").getComponent(GamePageController_1.default);
        if (existingController) {
            console.log("找到现有的GamePageController");
            window.gamePageController = existingController;
            return;
        }
        // 如果没有找到，创建一个新的节点并添加GamePageController
        console.log("创建新的GamePageController");
        var gamePageNode = new cc.Node("GamePageController");
        var canvas = cc.find("Canvas");
        if (canvas) {
            canvas.addChild(gamePageNode);
            var controller_1 = gamePageNode.addComponent(GamePageController_1.default);
            window.gamePageController = controller_1;
            console.log("GamePageController 创建并初始化完成");
            // 暴露测试方法
            window.testRoundAnimation = function () {
                console.log("全局测试方法被调用");
                controller_1.testRoundStartAnimation();
            };
        }
        else {
            console.error("找不到Canvas节点");
        }
    };
    GamePageInitializer = __decorate([
        ccclass
    ], GamePageInitializer);
    return GamePageInitializer;
}(cc.Component));
exports.default = GamePageInitializer;

cc._RF.pop();