"use strict";
cc._RF.push(module, '7ea6eVJMwFIT6o9GqVNzOjt', 'Singleton');
// meshTools/Singleton.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Singleton = void 0;
var Singleton = /** @class */ (function () {
    function Singleton() {
    }
    Singleton.GetInstance = function () {
        if (this._inst == null) {
            this._inst = new this();
        }
        return this._inst;
    };
    return Singleton;
}());
exports.Singleton = Singleton;

cc._RF.pop();