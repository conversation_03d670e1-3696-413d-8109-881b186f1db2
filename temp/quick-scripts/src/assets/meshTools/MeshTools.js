"use strict";
cc._RF.push(module, '1ac88QF4p1CyKQm2YcGvV0R', 'MeshTools');
// meshTools/MeshTools.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MeshTools = void 0;
var Publish_1 = require("./tools/Publish");
var MeshTools = /** @class */ (function () {
    function MeshTools() {
    }
    Object.defineProperty(MeshTools, "Publish", {
        /**
         * 游戏发布配置
         * @type {Publish}
         */
        get: function () {
            return Publish_1.Publish.GetInstance();
        },
        enumerable: false,
        configurable: true
    });
    return MeshTools;
}());
exports.MeshTools = MeshTools;

cc._RF.pop();