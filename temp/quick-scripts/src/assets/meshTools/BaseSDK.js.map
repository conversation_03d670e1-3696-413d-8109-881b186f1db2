{"version": 3, "sources": ["assets/meshTools/BaseSDK.ts"], "names": [], "mappings": ";;;;;;;AAEA;IAAA;IAsBA,CAAC;IApBU,6BAAW,GAAlB,cAA4B,CAAC;IAEtB,6BAAW,GAAlB,cAA4B,CAAC;IAEtB,8BAAY,GAAnB,cAA6B,CAAC;IAEvB,6BAAW,GAAlB,UAAmB,IAAa,IAAS,CAAC;IAEnC,+BAAa,GAApB,UAAqB,QAAyB,EAAE,WAAqB;QAEjE,QAAQ,IAAI,QAAQ,CAAC,OAAO,CAAC,CAAC;IAClC,CAAC;IAEM,2BAAS,GAAhB,UAAiB,QAAsB;QAEnC,QAAQ,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAIL,cAAC;AAAD,CAtBA,AAsBC,IAAA;AAtBY,0BAAO", "file": "", "sourceRoot": "/", "sourcesContent": ["\r\n\r\nexport class BaseSDK\r\n{\r\n    public AddAPPEvent(): void {}\r\n\r\n    public HideLoading(): void {}\r\n\r\n    public CloseWebView(): void {}\r\n\r\n    public ShowAppShop(type?: number): void {}\r\n\r\n    public GetAppVersion(callback: Action1<string>, errCallback?: Action0): void \r\n    {\r\n        callback && callback(\"0.0.0\");\r\n    }\r\n\r\n    public GetConfig(callback: Action1<any>): void \r\n    {\r\n        callback && callback(null);\r\n    }\r\n\r\n   \r\n\r\n}\r\n\r\nexport interface Action0\r\n{\r\n    (): void;\r\n}\r\n\r\nexport interface Action1<T>\r\n{\r\n    (p: T): void;\r\n}"]}