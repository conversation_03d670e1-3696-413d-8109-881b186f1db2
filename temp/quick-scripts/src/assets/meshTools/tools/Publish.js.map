{"version": 3, "sources": ["assets/meshTools/tools/Publish.ts"], "names": [], "mappings": ";;;;;AAAA;;GAEG;;;;;;;;;;;;;;;;AAEH,0CAAyC;AAGzC;IAA6B,2BAAS;IAAtC;QAAA,qEA2CC;QAzCG,iBAAiB;QACT,iBAAW,GAAW,CAAC,CAAC;QAChC,KAAK;QACG,qBAAe,GAAW,OAAO,CAAC;QAC1C,MAAM;QACE,YAAM,GAAW,IAAI,CAAC;QAWvB,SAAG,GAAQ,IAAI,CAAA,CAAC,iFAAiF;QAMvG,iDAAiD;QAC1C,iBAAW,GAAG,KAAK,CAAC;;IAkBhC,CAAC;IAdU,4BAAU,GAAjB;QAEI,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAEM,wBAAM,GAAb;QAEI,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAEM,2BAAS,GAAhB;QAEI,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IACL,cAAC;AAAD,CA3CA,AA2CC,CA3C4B,qBAAS,GA2CrC;AA3CY,0BAAO", "file": "", "sourceRoot": "/", "sourcesContent": ["/**\r\n * mesh游戏发布配置管理类\r\n */\r\n\r\nimport { Singleton } from \"../Singleton\";\r\n\r\n\r\nexport class Publish extends Singleton\r\n{\r\n    // 0 开发 1 测试 2 正式\r\n    private miniGameEnv: number = 1;\r\n    //版本号\r\n    private miniGameVersion: string = \"1.0.0\";\r\n    //游戏id\r\n    private gameId: number = 2059;  \r\n\r\n\r\n    // ※※※※※ 下面这些个参数是由 app 端传进来的 接收到之后进行赋值※※※※※※\r\n    public appChannel:string //商户渠道\r\n    public appId:number //商户 id\r\n    public userId:string //用户 userId\r\n    public code:string //商户生成的认证令牌\r\n    public roomId:string //房间 ID（不存在，可以传空）\r\n    public gameMode:string //游戏场景   2:半屏（秀场）    3: 全屏（游戏场）\r\n    public language:string //语言类型，默认为英文（详情见 表 3。多语言对照表）\r\n    public gsp:number=8001 //游戏正式服务器节点 101:新加坡（阿里云）  201:迪拜（AWS）301:硅谷 (阿里云)  401:法兰克福(阿里云） 8001:soofun（阿里云）\r\n    // 下面这俩参数归属于 gameConfig对象下面 \r\n    public sceneMode:number  //场馆级别，默认为 0  0:场馆列表 1:初级场 2:中级场 3:高级场  这个游戏此参数用不到\r\n    public currencyIcon:string //货币图标\r\n\r\n\r\n     //区分上面的参数是不是 url 传进来的，正常是用 js 传进来，个别渠道是通过 url 传参的\r\n     public isDataByURL = false;\r\n\r\n\r\n \r\n    public getVersion (): string\r\n    {\r\n        return this.miniGameVersion;\r\n    }\r\n\r\n    public getEnv (): number\r\n    {\r\n        return this.miniGameEnv;\r\n    }\r\n\r\n    public getGameId(): number\r\n    {\r\n        return this.gameId;\r\n    }\r\n}"]}