"use strict";
cc._RF.push(module, '1c80fkcVgdAbKBUUkWof48s', 'Publish');
// meshTools/tools/Publish.ts

"use strict";
/**
 * mesh游戏发布配置管理类
 */
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.Publish = void 0;
var Singleton_1 = require("../Singleton");
var Publish = /** @class */ (function (_super) {
    __extends(Publish, _super);
    function Publish() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        // 0 开发 1 测试 2 正式
        _this.miniGameEnv = 1;
        //版本号
        _this.miniGameVersion = "1.0.0";
        //游戏id
        _this.gameId = 2059;
        _this.gsp = 8001; //游戏正式服务器节点 101:新加坡（阿里云）  201:迪拜（AWS）301:硅谷 (阿里云)  401:法兰克福(阿里云） 8001:soofun（阿里云）
        //区分上面的参数是不是 url 传进来的，正常是用 js 传进来，个别渠道是通过 url 传参的
        _this.isDataByURL = false;
        return _this;
    }
    Publish.prototype.getVersion = function () {
        return this.miniGameVersion;
    };
    Publish.prototype.getEnv = function () {
        return this.miniGameEnv;
    };
    Publish.prototype.getGameId = function () {
        return this.gameId;
    };
    return Publish;
}(Singleton_1.Singleton));
exports.Publish = Publish;

cc._RF.pop();