{"version": 3, "sources": ["assets/meshTools/tools/MeshSdk.js"], "names": ["root", "factory", "exports", "module", "define", "amd", "global", "globalThis", "window", "self", "MeshSDK", "Protocal", "methodName", "id", "params", "onComplete", "onReceive", "timeout", "_this", "key", "_receiveName", "_completeName", "type", "console", "log", "result", "done", "flag", "dispose", "Utils", "isIframe", "addEventListener", "e", "data", "jsMethods", "jsData", "prototype", "parseResult", "undefined", "_completeTimeoutId", "setTimeout", "clearTimeout", "onDone", "callback", "_onDone", "TIME_OUT_IN_MS", "Native", "invoke", "protocal", "proto", "createProtocal", "isAndroid", "parent", "postMessage", "JSON", "stringify", "isUnity", "call", "Unity", "NativeBridge", "isIOS", "error", "total", "PROTOCAL_CACHE", "_allIFrames", "_iframeId", "u", "navigator", "userAgent", "indexOf", "match", "top", "nativeCall", "bind", "Object", "freeze", "_wrappedConfigParams", "handler", "cb", "createProtocol", "fnName", "webkit", "messageHandlers", "err", "parsed", "parse", "usePromiseResult", "resolve", "reject", "send", "protocol", "request", "onReceived", "reason", "regReceiveMessage", "wrapped", "keys", "for<PERSON>ach", "name", "str", "getVersion", "Promise", "getConfig", "destroy", "gameRecharge", "gameLoaded", "foldGame", "sendGameAction", "isSupportRechargeV2", "isSupport", "apis", "includes", "meshSDK", "defineProperty", "value"], "mappings": ";;;;;;AAAC,WAAUA,IAAV,EAAgBC,OAAhB,EAAyB;EACtB,OAAOC,OAAP,KAAmB,QAAnB,IAA+B,OAAOC,MAAP,KAAkB,WAAjD,GAA+DF,OAAO,CAACC,OAAD,CAAtE,GACI,OAAOE,MAAP,KAAkB,UAAlB,IAAgCA,MAAM,CAACC,GAAvC,GAA6CD,MAAM,CAAC,CAAC,SAAD,CAAD,EAAcH,OAAd,CAAnD,IACKK,MAAM,GAAG,OAAOC,UAAP,KAAsB,WAAtB,GAAoCA,UAApC,GAAiDC,MAAM,CAACF,MAAP,GAAgBE,MAAM,IAAIC,IAApF,EAA0FR,OAAO,CAACK,MAAM,CAACI,OAAP,GAAiB,EAAlB,CADtG,CADJ;AAGH,CAJA,UAKG,UAAUR,OAAV,EAAmB;EACf;;EACA,IAAIS,QAAQ,GAAI,YAAY;IACxB,SAASA,QAAT,CAAkBC,UAAlB,EAA8BC,EAA9B,EAAkCC,MAAlC,EAA0CC,UAA1C,EAAsDC,SAAtD,EAAiEC,OAAjE,EAA0E;MAAA;;MACtE,IAAIC,KAAK,GAAG,IAAZ;;MACA,IAAIC,GAAG,GAAGP,UAAU,GAAG,GAAb,GAAmBC,EAA7B;MACA,KAAKO,YAAL,GAAoBD,GAAG,GAAG,UAA1B;MACA,KAAKE,aAAL,GAAqBF,GAAG,GAAG,WAA3B;MACAL,MAAM,GAAGA,MAAM,IAAI,EAAnB;MACAA,MAAM,CAAC,SAAD,CAAN,GAAoB,KAAKM,YAAzB;MACAN,MAAM,CAAC,YAAD,CAAN,GAAuB,KAAKO,aAA5B;;MACA,IAAIT,UAAU,KAAK,gBAAf,IAAmCE,MAAM,CAACQ,IAAP,KAAgB,CAAvD,EAA0D;QACtDC,OAAO,CAACC,GAAR,CAAY,UAAZ,EAAwB,QAAxB,EAAkCV,MAAlC;MACH;;MACDN,MAAM,CAAC,KAAKY,YAAN,CAAN,GAA4B,UAAUK,MAAV,EAAkB;QAC1CF,OAAO,CAACC,GAAR,CAAY,UAAZ,EAAwB,SAAxB,EAAmC,gBAAgBL,GAAhB,GAAsB,WAAzD;QACAX,MAAM,CAACU,KAAK,CAACE,YAAP,CAAN,GAA6B,IAA7B;;QACAF,KAAK,CAACQ,IAAN;;QACA,IAAIV,SAAJ,EAAe;UACXA,SAAS,CAAC,IAAD,EAAOS,MAAP,CAAT;QACH;MACJ,CAPD;;MAQA,IAAIE,IAAI,GAAG,KAAX;;MACAnB,MAAM,CAAC,KAAKa,aAAN,CAAN,GAA6B,UAAUI,MAAV,EAAkB;QAC3CF,OAAO,CAACC,GAAR,CAAY,UAAZ,EAAwB,SAAxB,EAAmC,gBAAgBL,GAAhB,GAAsB,YAAzD,EAAuEM,MAAvE;QACAE,IAAI,GAAG,IAAP;;QACAT,KAAK,CAACU,OAAN;;QACA,IAAIb,UAAJ,EAAgB;UACZA,UAAU,CAAC,IAAD,EAAOU,MAAP,CAAV;QACH;MACJ,CAPD;;MAQA,IAAII,KAAK,CAACC,QAAN,EAAJ,EAAsB;QAClB,IAAIlB,UAAU,IAAI,gBAAlB,EAAoC;UAChC,IAAIE,MAAM,CAACQ,IAAP,IAAe,CAAnB,EAAsB;YAClBK,IAAI,GAAG,IAAP;;YACAT,KAAK,CAACU,OAAN;;YACA;UACH;QACJ;;QACDpB,MAAM,CAACuB,gBAAP,CAAwB,SAAxB,EAAmC,UAAAC,CAAC,EAAI;UACpC,IAAIA,CAAC,CAACC,IAAF,IAAUzB,MAAM,CAAC,MAAI,CAACa,aAAN,CAApB,EAA0C;YACtC,IAAIY,IAAI,GAAGD,CAAC,CAACC,IAAb;YACA,IAAIC,SAAS,GAAGD,IAAI,CAACC,SAArB;YACA,IAAI,CAACA,SAAL,EAAgB;YAChB,IAAIA,SAAS,KAAK,MAAI,CAACb,aAAvB,EAAsC;YACtC,IAAIc,MAAM,GAAGF,IAAI,CAACE,MAAlB;YACA,IAAI,CAACA,MAAL,EAAa;YACb,IAAIV,MAAM,GAAGf,OAAO,CAAC0B,SAAR,CAAkBC,WAAlB,CAA8BC,SAA9B,EAAyCH,MAAzC,CAAb;YACA,IAAI,CAACV,MAAL,EACI;YACJF,OAAO,CAACC,GAAR,CAAY,UAAZ,EAAwB,SAAxB,EAAmC,gBAAgBU,SAAhB,GAA4B,YAA/D,EAA6ET,MAA7E;YACAE,IAAI,GAAG,IAAP;;YACAT,KAAK,CAACU,OAAN;;YACA,IAAIb,UAAJ,EAAgB;cACZA,UAAU,CAAC,IAAD,EAAOU,MAAP,CAAV;YACH;UACJ;QACJ,CAlBD,EAkBG,KAlBH;MAmBH;;MACD,IAAIR,OAAO,GAAG,CAAd,EAAiB;QACb,KAAKsB,kBAAL,GAA0BC,UAAU,CAAC,YAAY;UAC7CC,YAAY,CAACvB,KAAK,CAACqB,kBAAP,CAAZ;;UACA,IAAIxB,UAAU,IAAI,CAACY,IAAnB,EAAyB;YACrBT,KAAK,CAACU,OAAN;;YACAb,UAAU,CAAC,gBAAgBI,GAAhB,GAAsB,iBAAvB,EAA0C,IAA1C,CAAV;UACH;QACJ,CANmC,EAMjCF,OANiC,CAApC;MAOH;IACJ;;IAEDN,QAAQ,CAACyB,SAAT,CAAmBM,MAAnB,GAA4B,UAAUC,QAAV,EAAoB;MAC5C,IAAI,KAAKvB,YAAL,IAAqB,KAAKC,aAA9B,EACI,KAAKuB,OAAL,GAAeD,QAAf;IACP,CAHD;;IAIAhC,QAAQ,CAACyB,SAAT,CAAmBV,IAAnB,GAA0B,YAAY;MAClC,IAAI,KAAKkB,OAAT,EAAkB;QACd,KAAKA,OAAL;;QACA,KAAKA,OAAL,GAAeN,SAAf;MACH;IACJ,CALD;;IAMA3B,QAAQ,CAACyB,SAAT,CAAmBR,OAAnB,GAA6B,YAAY;MACrC,KAAKF,IAAL;MACAlB,MAAM,CAAC,KAAKY,YAAN,CAAN,GAA4B,IAA5B;MACA,OAAOZ,MAAM,CAAC,KAAKY,YAAN,CAAb;MACA,KAAKA,YAAL,GAAoB,IAApB;MACAZ,MAAM,CAAC,KAAKa,aAAN,CAAN,GAA6B,IAA7B;MACA,OAAOb,MAAM,CAAC,KAAKa,aAAN,CAAb;MACA,KAAKA,aAAL,GAAqB,IAArB;MACAoB,YAAY,CAAC,KAAKF,kBAAN,CAAZ;IACH,CATD;;IAUA5B,QAAQ,CAACkC,cAAT,GAA0B,IAA1B;IACA,OAAOlC,QAAP;EACH,CA1Fe,EAAhB;;EA2FA,IAAImC,MAAM,GAAI,YAAY;IACtB,SAASA,MAAT,GAAkB,CACjB;;IAEDA,MAAM,CAACC,MAAP,GAAgB,UAAUnC,UAAV,EAAsBoC,QAAtB,EAAgClC,MAAhC,EAAwCC,UAAxC,EAAoDC,SAApD,EAA+DC,OAA/D,EACd;MACE,IAAIC,KAAK,GAAG,IAAZ;;MACA,IAAID,OAAO,KAAK,KAAK,CAArB,EAAwB;QACpBA,OAAO,GAAG,IAAV;MACH;;MACD,IAAIgC,KAAK,GAAG,KAAKC,cAAL,CAAoBtC,UAApB,EAAgCE,MAAhC,EAAwCC,UAAxC,EAAoDC,SAApD,EAA+DC,OAA/D,CAAZ;;MACA,IAAIgC,KAAJ,EAAW;QACP,IAAInB,QAAQ,GAAGD,KAAK,CAACC,QAAN,EAAf;;QACA,IAAID,KAAK,CAACsB,SAAN,EAAJ,EAAuB;UACnB,IAAIrB,QAAJ,EAAc;YACVP,OAAO,CAACC,GAAR,CAAY,gBAAZ,EADU,CAEV;;YACAhB,MAAM,CAAC4C,MAAP,CAAcC,WAAd,CAA0BC,IAAI,CAACC,SAAL,CAAezC,MAAf,CAA1B,EAAkD,GAAlD;UACH,CAJD,MAIO;YACH,IAAIkC,QAAJ,EAAc;cACV,IAAInB,KAAK,CAAC2B,OAAN,EAAJ,EAAqB;gBACjBR,QAAQ,CAACS,IAAT,CAAcjD,MAAM,CAACkD,KAArB,EAA4BJ,IAAI,CAACC,SAAL,CAAezC,MAAf,CAA5B;cACH,CAFD,MAEO;gBACHkC,QAAQ,CAACS,IAAT,CAAcjD,MAAM,CAACmD,YAArB,EAAmCL,IAAI,CAACC,SAAL,CAAezC,MAAf,CAAnC;cACH;YACJ;UACJ;QACJ,CAdD,MAcO,IAAIe,KAAK,CAAC+B,KAAN,EAAJ,EAAmB;UACtB,IAAI9B,QAAJ,EAAc;YACVP,OAAO,CAACC,GAAR,CAAY,YAAZ,EADU,CAEV;;YACAhB,MAAM,CAAC4C,MAAP,CAAcC,WAAd,CAA0BC,IAAI,CAACC,SAAL,CAAezC,MAAf,CAA1B,EAAkD,GAAlD;UACH,CAJD,MAIO;YACH,IAAIkC,QAAJ,EAAc;cACVA,QAAQ,CAACK,WAAT,CAAqBC,IAAI,CAACC,SAAL,CAAezC,MAAf,CAArB;YACH;UACJ;QACJ,CAVM,MAUA;UACH,IAAIgB,QAAJ,EAAc;YACVP,OAAO,CAACC,GAAR,CAAY,WAAZ,EADU,CAEV;;YACAhB,MAAM,CAAC4C,MAAP,CAAcC,WAAd,CAA0BC,IAAI,CAACC,SAAL,CAAezC,MAAf,CAA1B,EAAkD,GAAlD;UACH,CAJD,MAIO;YACHS,OAAO,CAACsC,KAAR,CAAc,UAAd,EAA0B,SAA1B,EAAqC,qBAArC;UACH;QACJ;MACJ,CAnCD,MAmCO;QACHtC,OAAO,CAACsC,KAAR,CAAc,UAAd,EAA0B,SAA1B,EAAqC,gCAArC;MACH;IACJ,CA7CD;;IA8CAf,MAAM,CAACI,cAAP,GAAwB,UAAUtC,UAAV,EAAsBE,MAAtB,EAA8BC,UAA9B,EAA0CC,SAA1C,EAAqDC,OAArD,EAA8D;MAClF,IAAI6C,KAAK,GAAG,KAAKC,cAAL,CAAoBnD,UAApB,KAAmC,CAA/C;MACAkD,KAAK,IAAI,CAAT;MACA,KAAKC,cAAL,CAAoBnD,UAApB,IAAkCkD,KAAlC;MACA,OAAO,IAAInD,QAAJ,CAAaC,UAAb,EAAyBkD,KAAzB,EAAgChD,MAAhC,EAAwCC,UAAxC,EAAoDC,SAApD,EAA+DC,OAA/D,CAAP;IACH,CALD;;IAMA6B,MAAM,CAACkB,WAAP,GAAqB,EAArB;IACAlB,MAAM,CAACmB,SAAP,GAAmB,CAAC,CAApB;IACAnB,MAAM,CAACiB,cAAP,GAAwB,EAAxB;IACA,OAAOjB,MAAP;EACH,CA5Da,EAAd;;EA6DA,IAAIK,SAAS,GAAG,SAAZA,SAAY,GAAY;IACxB,IAAIe,CAAC,GAAGC,SAAS,CAACC,SAAlB;IACA,OAAOF,CAAC,CAACG,OAAF,CAAU,SAAV,IAAuB,CAAC,CAAxB,IAA6BH,CAAC,CAACG,OAAF,CAAU,KAAV,IAAmB,CAAC,CAAxD;EACH,CAHD;;EAIA,IAAIT,KAAK,GAAG,SAARA,KAAQ,GAAY;IACpB,IAAIM,CAAC,GAAGC,SAAS,CAACC,SAAlB;IACA,OAAO,CAAC,CAACF,CAAC,CAACI,KAAF,CAAQ,+BAAR,CAAT;EACH,CAHD;;EAIA,IAAIxC,QAAQ,GAAG,SAAXA,QAAW,GAAY;IACvB,OAAOtB,MAAM,CAAC+D,GAAP,KAAe/D,MAAtB;EACH,CAFD;;EAGA,IAAIgD,OAAO,GAAG,SAAVA,OAAU,GAAY;IACtB,OAAOhD,MAAM,CAAC,SAAD,CAAb;EACH,CAFD;;EAGA,IAAIgE,UAAU,GAAG1B,MAAM,CAACC,MAAP,CAAc0B,IAAd,CAAmB3B,MAAnB,CAAjB;EACA,IAAIjB,KAAK,GAAG6C,MAAM,CAACC,MAAP,CAAc;IACtBH,UAAU,EAAEA,UADU;IAEtBrB,SAAS,EAAEA,SAFW;IAGtBS,KAAK,EAAEA,KAHe;IAItB9B,QAAQ,EAAEA,QAJY;IAKtB0B,OAAO,EAAEA;EALa,CAAd,CAAZ;;EAOA,IAAI9C,OAAO,GAAI,YAAY;IACvB,SAASA,OAAT,GAAmB;MACf;MACA,KAAKkE,oBAAL,GAA4B;QACxB;QACA,oBAAoB,0BAAUC,OAAV,EAAmBpD,MAAnB,EAA2B;UAC3C,IAAIqD,EAAE,GAAGD,OAAT;UAAA,IAAkB5C,IAAI,GAAGR,MAAzB;;UACA,IAAIqD,EAAJ,EAAQ;YACJA,EAAE,CAAC7C,IAAD,CAAF;UACH;QACJ,CAPuB;QAQxB;QACA,gBAAgB,sBAAU4C,OAAV,EAAmBpD,MAAnB,EAA2B;UACvC,IAAIqD,EAAE,GAAGD,OAAT;UAAA,IAAkB5C,IAAI,GAAGR,MAAzB;;UACA,IAAIqD,EAAJ,EAAQ;YACJA,EAAE,CAAC7C,IAAD,CAAF;UACH;QACJ,CAduB;;QAexB;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QAEoB,oBAAoB,0BAAU4C,OAAV,EAAmBpD,MAAnB,EAA2B;UAC3C,IAAIqD,EAAE,GAAGD,OAAT;UAAA,IAAkB5C,IAAI,GAAGR,MAAzB;;UACA,IAAIqD,EAAJ,EAAQ;YACJA,EAAE,CAAC7C,IAAD,CAAF;UACH;QACJ,CA5JuB;;QA6JxB;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;QACoB,eAAe,qBAAU4C,OAAV,EAAmBpD,MAAnB,EAA2B;UACtC,IAAIqD,EAAE,GAAGD,OAAT;UAAA,IAAkB5C,IAAI,GAAGR,MAAzB;;UACA,IAAIqD,EAAJ,EAAQ;YACJA,EAAE,CAAC7C,IAAD,CAAF;UACH;QACJ,CA1KuB;;QA2KxB;AACpB;AACA;AACA;QACoB,gBAAgB,sBAAU4C,OAAV,EAAmBpD,MAAnB,EAA2B;UACvC,IAAIqD,EAAE,GAAGD,OAAT;UAAA,IAAkB5C,IAAI,GAAGR,MAAzB;;UACA,IAAIqD,EAAJ,EAAQ;YACJA,EAAE,CAAC7C,IAAD,CAAF;UACH;QACJ;MApLuB,CAA5B;IAsLH;IAED;AACZ;AACA;AACA;;;IACYvB,OAAO,CAAC0B,SAAR,CAAkB2C,cAAlB,GAAmC,UAAUC,MAAV,EAAkB;MACjD,IAAIlD,QAAQ,GAAGD,KAAK,CAACC,QAAN,EAAf;;MACA,IAAID,KAAK,CAACsB,SAAN,EAAJ,EAAuB;QACnB,IAAIrB,QAAJ,EAAc;UACVP,OAAO,CAACC,GAAR,CAAY,gBAAZ;QACH,CAFD,MAEO,IAAIK,KAAK,CAAC2B,OAAN,EAAJ,EAAqB;UACxB,IAAIhD,MAAM,CAACkD,KAAP,IAAgBlD,MAAM,CAACkD,KAAP,CAAa,MAAb,CAApB,EAA0C;YACtC,OAAOlD,MAAM,CAACkD,KAAP,CAAa,MAAb,CAAP;UACH,CAFD,MAEO;YACH,OAAO,IAAP;UACH;QACJ,CANM,MAMA;UACH,IAAIlD,MAAM,CAACmD,YAAP,IAAuBnD,MAAM,CAACmD,YAAP,CAAoBqB,MAApB,CAA3B,EAAwD;YACpD,OAAOxE,MAAM,CAACmD,YAAP,CAAoBqB,MAApB,CAAP;UACH,CAFD,MAEO;YACH,OAAO,IAAP;UACH;QACJ;MACJ,CAhBD,MAgBO,IAAInD,KAAK,CAAC+B,KAAN,EAAJ,EAAmB;QACtB,IAAI9B,QAAJ,EAAc;UACVP,OAAO,CAACC,GAAR,CAAY,YAAZ;QACH,CAFD,MAEO,IAAIK,KAAK,CAAC2B,OAAN,EAAJ,EAAqB;UACxB,IAAIhD,MAAM,CAACyE,MAAP,IAAiBzE,MAAM,CAACyE,MAAP,CAAcC,eAA/B,IAAkD1E,MAAM,CAACyE,MAAP,CAAcC,eAAd,CAA8B,cAA9B,CAAtD,EAAqG;YACjG,OAAO1E,MAAM,CAACyE,MAAP,CAAcC,eAAd,CAA8B,cAA9B,CAAP;UACH,CAFD,MAEO;YACH,OAAO,IAAP;UACH;QACJ,CANM,MAMA;UACH,IAAI1E,MAAM,CAACyE,MAAP,IAAiBzE,MAAM,CAACyE,MAAP,CAAcC,eAA/B,IAAkD1E,MAAM,CAACyE,MAAP,CAAcC,eAAd,CAA8BF,MAA9B,CAAtD,EAA6F;YACzF,OAAOxE,MAAM,CAACyE,MAAP,CAAcC,eAAd,CAA8BF,MAA9B,CAAP;UACH,CAFD,MAEO;YACH,OAAO,IAAP;UACH;QACJ;MACJ,CAhBM,MAgBA;QACH,IAAIlD,QAAJ,EAAc;UACVP,OAAO,CAACC,GAAR,CAAY,WAAZ;QACH,CAFD,MAEO;UACHD,OAAO,CAACsC,KAAR,CAAc,eAAd,EAA+B,kCAA/B;QACH;MACJ;IACJ,CAzCD;IA0CA;AACZ;AACA;AACA;AACA;AACA;;;IACYnD,OAAO,CAAC0B,SAAR,CAAkBC,WAAlB,GAAgC,UAAU8C,GAAV,EAAe1D,MAAf,EAAuB;MACnD,IAAI0D,GAAJ,EACI,MAAMA,GAAN,CADJ,KAEK,IAAI1D,MAAM,IAAIa,SAAV,IAAuBb,MAAM,IAAI,IAArC,EACD,MAAM,cAAN,CADC,KAEA;QACD,IAAI2D,MAAM,GAAG,KAAK,CAAlB;;QACA,IAAI,OAAO3D,MAAP,IAAiB,QAArB,EAA+B;UAC3B2D,MAAM,GAAG9B,IAAI,CAAC+B,KAAL,CAAW5D,MAAX,CAAT;QACH,CAFD,MAEO,IAAI,OAAOA,MAAP,IAAiB,QAArB,EAA+B;UAClC2D,MAAM,GAAG3D,MAAT;QACH;;QACD,IAAI,CAAC2D,MAAL,EACI,MAAM,iBAAN;QACJ,OAAOA,MAAP;MACH;IACJ,CAhBD;IAiBA;AACZ;AACA;AACA;AACA;;;IACY1E,OAAO,CAAC0B,SAAR,CAAkBkD,gBAAlB,GAAqC,UAAUC,OAAV,EAAmBC,MAAnB,EAA2B;MAC5D,IAAItE,KAAK,GAAG,IAAZ;;MACA,OAAO,UAAUiE,GAAV,EAAe1D,MAAf,EAAuB;QAC1B,IAAI;UACA,IAAI2D,MAAM,GAAGlE,KAAK,CAACmB,WAAN,CAAkB8C,GAAlB,EAAuB1D,MAAvB,CAAb;;UACA,IAAI2D,MAAJ,EAAY;YACRG,OAAO,CAACH,MAAD,CAAP;UACH;QACJ,CALD,CAKE,OAAOpD,CAAP,EAAU;UACRwD,MAAM,CAACxD,CAAD,CAAN;QACH;MACJ,CATD;IAUH,CAZD;IAaA;AACZ;AACA;AACA;AACA;AACA;AACA;;;IACYtB,OAAO,CAAC0B,SAAR,CAAkBqD,IAAlB,GAAyB,UAAU3E,MAAV,EAAkBC,UAAlB,EAA8BC,SAA9B,EAAyC;MAC9D,IAAI,CAACF,MAAL,EACI;MACJe,KAAK,CAAC2C,UAAN,CAAiB1D,MAAM,CAACF,UAAxB,EAAoCE,MAAM,CAAC4E,QAA3C,EAAqD5E,MAAM,CAACA,MAA5D,EAAoEC,UAApE,EAAgFC,SAAhF,EAA2FF,MAAM,CAACG,OAAlG;IACH,CAJD;IAKA;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;;;IACYP,OAAO,CAAC0B,SAAR,CAAkBuD,OAAlB,GAA4B,UAAU/E,UAAV,EAAsBE,MAAtB,EAA8BC,UAA9B,EAA0C6E,UAA1C,EAAsD3E,OAAtD,EAA+D;MACvF,IAAIA,OAAO,KAAK,KAAK,CAArB,EAAwB;QACpBA,OAAO,GAAG,CAAV;MACH;;MACD,KAAKwE,IAAL,CAAU;QACN7E,UAAU,EAAEA,UADN;QAEN8E,QAAQ,EAAE,KAAKX,cAAL,CAAoBnE,UAApB,CAFJ;QAGNE,MAAM,EAAEA,MAHF;QAING,OAAO,EAAEA;MAJH,CAAV,EAKG,KAAKqE,gBAAL,CAAsBvE,UAAtB,EAAkC,UAAU8E,MAAV,EAAkB;QACnD,OAAOtE,OAAO,CAACsC,KAAR,CAAc,eAAd,EAA+B,gBAAgBjD,UAAhB,GAA6B,UAA7B,GAA0CiF,MAAzE,CAAP;MACH,CAFE,CALH,EAOID,UAPJ;IAQH,CAZD;;IAaAlF,OAAO,CAAC0B,SAAR,CAAkB0D,iBAAlB,GAAsC,UAAUhF,MAAV,EAAkB;MACpD,IAAII,KAAK,GAAG,IAAZ;;MACA,IAAI6E,OAAO,GAAG,EAAd;;MACA,KAAK,IAAI5E,GAAT,IAAgBL,MAAhB,EAAwB;QACpB,IAAI,CAAC,CAACI,KAAK,CAAC0D,oBAAN,CAA2BzD,GAA3B,CAAN,EAAuC;UACnC4E,OAAO,CAAC5E,GAAD,CAAP,GAAeA,GAAf;QACH;MACJ;;MACD,IAAIU,KAAK,CAACC,QAAN,EAAJ,EAAsB;QAClBtB,MAAM,CAACuB,gBAAP,CAAwB,SAAxB,EAAmC,UAAAC,CAAC,EAAI;UACpC,IAAIA,CAAC,CAACC,IAAN,EAAY;YACR,IAAIA,IAAI,GAAGD,CAAC,CAACC,IAAb;YACA,IAAIC,SAAS,GAAGD,IAAI,CAACC,SAArB;YACA,IAAI,CAACpB,MAAM,CAACoB,SAAD,CAAX,EACI;YACJ,IAAIC,MAAM,GAAGF,IAAI,CAACE,MAAlB;YACA,IAAI,CAACA,MAAL,EAAa;YACb,IAAIV,MAAM,GAAGf,OAAO,CAAC0B,SAAR,CAAkBC,WAAlB,CAA8BC,SAA9B,EAAyCH,MAAzC,CAAb;YACA,IAAI,CAACV,MAAL,EACI;;YACJ,IAAI,OAAOX,MAAM,CAACoB,SAAD,CAAb,KAA6B,UAA7B,IAA2C,CAAC,CAAChB,KAAK,CAAC0D,oBAAN,CAA2B1C,SAA3B,CAAjD,EAAwF;cACpFhB,KAAK,CAAC0D,oBAAN,CAA2B1C,SAA3B,EAAsCpB,MAAM,CAACoB,SAAD,CAA5C,EAAyDT,MAAzD;YACH;UACJ;QACJ,CAfD;MAgBH,CAjBD,MAiBO;QACH;QACAiD,MAAM,CAACsB,IAAP,CAAYD,OAAZ,EAAqBE,OAArB,CAA6B,UAAU9E,GAAV,EAAe;UACxC,IAAI+E,IAAI,GAAGH,OAAO,CAAC5E,GAAD,CAAlB;;UACA,IAAI+E,IAAJ,EAAU;YACN,IAAIA,IAAI,IAAI,cAAZ,EAA4B;cACxB1F,MAAM,CAAC0F,IAAD,CAAN,GAAe,UAAUC,GAAV,EAAe;gBAC1B,IAAI,OAAOrF,MAAM,CAACK,GAAD,CAAb,KAAuB,UAAvB,IAAqC,CAAC,CAACD,KAAK,CAAC0D,oBAAN,CAA2BzD,GAA3B,CAA3C,EAA4E;kBACxED,KAAK,CAAC0D,oBAAN,CAA2BzD,GAA3B,EAAgCL,MAAM,CAACK,GAAD,CAAtC,EAA6C,EAA7C;gBACH;cACJ,CAJD;YAKH,CAND,MAMO;cACHX,MAAM,CAAC0F,IAAD,CAAN,GAAe,UAAUC,GAAV,EAAe;gBAC1B,IAAI1E,MAAM,GAAGP,KAAK,CAACmB,WAAN,CAAkBC,SAAlB,EAA6B6D,GAA7B,CAAb;;gBACA,IAAI,CAAC1E,MAAL,EACI;gBACJ,IAAI,CAACX,MAAM,CAACK,GAAD,CAAX,EACI;;gBACJ,IAAI,OAAOL,MAAM,CAACK,GAAD,CAAb,KAAuB,UAAvB,IAAqC,CAAC,CAACD,KAAK,CAAC0D,oBAAN,CAA2BzD,GAA3B,CAA3C,EAA4E;kBACxED,KAAK,CAAC0D,oBAAN,CAA2BzD,GAA3B,EAAgCL,MAAM,CAACK,GAAD,CAAtC,EAA6CM,MAA7C;gBACH;cACJ,CATD;YAUH;UACJ;QACJ,CAtBD;MAuBH;;MACD,OAAOsE,OAAP;IACH,CApDD;IAqDA;AACZ;AACA;AACA;;;IACYrF,OAAO,CAAC0B,SAAR,CAAkBgE,UAAlB,GAA+B,YAAY;MACvC,OAAO,IAAIC,OAAJ,CAAY,UAAUd,OAAV,EAAmB;QAClC,IAAItD,IAAI,GAAG,OAAX;QACAsD,OAAO,CAACtD,IAAD,CAAP;MACH,CAHM,CAAP;IAIH,CALD;IAMA;AACZ;AACA;AACA;;;IACYvB,OAAO,CAAC0B,SAAR,CAAkBkE,SAAlB,GAA8B,YAAY;MACtC,IAAIpF,KAAK,GAAG,IAAZ;;MACA,OAAO,IAAImF,OAAJ,CAAY,UAAUd,OAAV,EAAmBC,MAAnB,EAA2B;QAC1CtE,KAAK,CAACyE,OAAN,CAAc,WAAd,EAA2B,EAA3B,EAA+B,UAAU1D,IAAV,EAAgB;UAC3CsD,OAAO,CAACtD,IAAD,CAAP;QACH,CAFD,EAEGuD,MAFH;MAGH,CAJM,CAAP;IAKH,CAPD;IAQA;AACZ;AACA;AACA;;;IACY9E,OAAO,CAAC0B,SAAR,CAAkBmE,OAAlB,GAA4B,YAAY;MACpC,IAAIrF,KAAK,GAAG,IAAZ;;MACA,OAAO,IAAImF,OAAJ,CAAY,UAAUd,OAAV,EAAmBC,MAAnB,EAA2B;QAC1CtE,KAAK,CAACyE,OAAN,CAAc,SAAd,EAAyB,EAAzB,EAA6B,UAAU1D,IAAV,EAAgB;UACzCsD,OAAO,CAACtD,IAAD,CAAP;QACH,CAFD,EAEGuD,MAFH;MAGH,CAJM,CAAP;IAKH,CAPD;IAQA;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;IACY9E,OAAO,CAAC0B,SAAR,CAAkBoE,YAAlB,GAAiC,UAAU1F,MAAV,EAAkB;MAC/C,IAAII,KAAK,GAAG,IAAZ;;MACA,OAAO,IAAImF,OAAJ,CAAY,UAAUd,OAAV,EAAmBC,MAAnB,EAA2B;QAC1CtE,KAAK,CAACyE,OAAN,CAAc,cAAd,EAA8B7E,MAAM,GAAGA,MAAH,GAAY,EAAhD,EAAoD,UAAUmB,IAAV,EAAgB;UAChEsD,OAAO,CAACtD,IAAD,CAAP;QACH,CAFD,EAEGuD,MAFH;MAGH,CAJM,CAAP;IAKH,CAPD;IAQA;AACZ;AACA;AACA;;;IACY9E,OAAO,CAAC0B,SAAR,CAAkBqE,UAAlB,GAA+B,YAAY;MACvC,IAAIvF,KAAK,GAAG,IAAZ;;MACA,OAAO,IAAImF,OAAJ,CAAY,UAAUd,OAAV,EAAmBC,MAAnB,EAA2B;QAC1CtE,KAAK,CAACyE,OAAN,CAAc,YAAd,EAA4B,EAA5B,EAAgC,UAAU1D,IAAV,EAAgB;UAC5CsD,OAAO,CAACtD,IAAD,CAAP;QACH,CAFD,EAEGuD,MAFH;MAGH,CAJM,CAAP;IAKH,CAPD;IAQA;AACZ;AACA;AACA;AACA;AACA;;;IACY9E,OAAO,CAAC0B,SAAR,CAAkBsE,QAAlB,GAA6B,YAAY;MACrC,IAAIxF,KAAK,GAAG,IAAZ;;MACA,OAAO,IAAImF,OAAJ,CAAY,UAAUd,OAAV,EAAmBC,MAAnB,EAA2B;QAC1CtE,KAAK,CAACyE,OAAN,CAAc,UAAd,EAA0B,EAA1B,EAA8B,UAAU1D,IAAV,EAAgB;UAC1CsD,OAAO,CAACtD,IAAD,CAAP;QACH,CAFD,EAEGuD,MAFH;MAGH,CAJM,CAAP;IAKH,CAPD;IASA;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;IACY9E,OAAO,CAAC0B,SAAR,CAAkBuE,cAAlB,GAAmC,UAAU7F,MAAV,EAAkB;MACjD,IAAII,KAAK,GAAG,IAAZ;;MACA,OAAO,IAAImF,OAAJ,CAAY,UAAUd,OAAV,EAAmBC,MAAnB,EAA2B;QAC1CtE,KAAK,CAACyE,OAAN,CAAc,gBAAd,EAAgC7E,MAAhC,EAAwC,UAAUmB,IAAV,EAAgB;UACpDsD,OAAO,CAACtD,IAAD,CAAP;QACH,CAFD,EAEGuD,MAFH;MAGH,CAJM,CAAP;IAKH,CAPD;IASA;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;;;IACY9E,OAAO,CAAC0B,SAAR,CAAkBwE,mBAAlB,GAAwC,YAAY;MAChD,IAAI1F,KAAK,GAAG,IAAZ;;MACA,OAAO,IAAImF,OAAJ,CAAY,UAAUd,OAAV,EAAmB;QAClC,IAAIsB,SAAS,GAAG,KAAhB;;QACA,IAAIhF,KAAK,CAACsB,SAAN,EAAJ,EAAuB;UACnB,IAAI3C,MAAM,CAACmD,YAAP,CAAoB,oBAApB,CAAJ,EAA+C;YAC3CzC,KAAK,CAACyE,OAAN,CAAc,oBAAd,EAAoC,EAApC,EAAwC,UAAU1D,IAAV,EAAgB;cACpD,IAAIA,IAAI,CAAC6E,IAAL,IAAa7E,IAAI,CAAC6E,IAAL,CAAUC,QAAV,CAAmB,sBAAnB,CAAjB,EAA6D;gBACzDF,SAAS,GAAG,IAAZ;cACH;;cACDtB,OAAO,CAAC;gBAAC,aAAasB;cAAd,CAAD,CAAP;YACH,CALD;UAMH,CAPD,MAOO;YACHtB,OAAO,CAAC;cAAC,aAAasB;YAAd,CAAD,CAAP;UACH;QACJ,CAXD,MAWO,IAAIhF,KAAK,CAAC+B,KAAN,EAAJ,EAAmB;UACtB,IAAIpD,MAAM,CAACyE,MAAP,CAAcC,eAAd,CAA8B,oBAA9B,CAAJ,EAAyD;YACrDhE,KAAK,CAACyE,OAAN,CAAc,oBAAd,EAAoC,EAApC,EAAwC,UAAU1D,IAAV,EAAgB;cACpD,IAAIA,IAAI,CAAC6E,IAAL,IAAa7E,IAAI,CAAC6E,IAAL,CAAUC,QAAV,CAAmB,sBAAnB,CAAjB,EAA6D;gBACzDF,SAAS,GAAG,IAAZ;cACH;;cACDtB,OAAO,CAAC;gBAAC,aAAasB;cAAd,CAAD,CAAP;YACH,CALD;UAMH,CAPD,MAOO;YACHtB,OAAO,CAAC;cAAC,aAAasB;YAAd,CAAD,CAAP;UACH;QACJ,CAXM,MAWA;UACHA,SAAS,GAAG,IAAZ;UACAtB,OAAO,CAAC;YAAC,aAAasB;UAAd,CAAD,CAAP;QACH;MACJ,CA5BM,CAAP;IA6BH,CA/BD;;IAiCA,OAAOnG,OAAP;EACH,CApmBc,EAAf;;EAqmBA,IAAIsG,OAAO,GAAG,IAAItG,OAAJ,EAAd;EACAR,OAAO,CAAC8G,OAAR,GAAkBA,OAAlB;EACAtC,MAAM,CAACuC,cAAP,CAAsB/G,OAAtB,EAA+B,YAA/B,EAA6C;IAACgH,KAAK,EAAE;EAAR,CAA7C;AACH,CA7xBJ,CAAD", "sourceRoot": "/", "sourcesContent": ["(function (root, factory) {\r\n    typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :\r\n        typeof define === 'function' && define.amd ? define(['exports'], factory) :\r\n            (global = typeof globalThis !== 'undefined' ? globalThis : window.global = window || self, factory(global.MeshSDK = {}));\r\n}(this,\r\n    function (exports) {\r\n        'use strict';\r\n        var Protocal = (function () {\r\n            function Protocal(methodName, id, params, onComplete, onReceive, timeout) {\r\n                var _this = this;\r\n                var key = methodName + \"_\" + id;\r\n                this._receiveName = key + \"_receive\";\r\n                this._completeName = key + \"_complete\";\r\n                params = params || {};\r\n                params['receive'] = this._receiveName;\r\n                params['jsCallback'] = this._completeName;\r\n                if (methodName !== 'sendGameAction' && params.type !== 5) {\r\n                    console.log('[MeshH5]', 'params', params);\r\n                }\r\n                window[this._receiveName] = function (result) {\r\n                    console.log('[MeshH5]', 'native:', \"methodName \" + key + \" received\");\r\n                    window[_this._receiveName] = null;\r\n                    _this.done();\r\n                    if (onReceive) {\r\n                        onReceive(null, result);\r\n                    }\r\n                };\r\n                var flag = false;\r\n                window[this._completeName] = function (result) {\r\n                    console.log('[MeshH5]', 'native:', \"methodName \" + key + \" completed\", result);\r\n                    flag = true;\r\n                    _this.dispose();\r\n                    if (onComplete) {\r\n                        onComplete(null, result);\r\n                    }\r\n                };\r\n                if (Utils.isIframe()) {\r\n                    if (methodName == 'sendGameAction') {\r\n                        if (params.type == 1) {\r\n                            flag = true;\r\n                            _this.dispose();\r\n                            return;\r\n                        }\r\n                    }\r\n                    window.addEventListener('message', e => {\r\n                        if (e.data && window[this._completeName]) {\r\n                            var data = e.data;\r\n                            var jsMethods = data.jsMethods;\r\n                            if (!jsMethods) return;\r\n                            if (jsMethods !== this._completeName) return;\r\n                            var jsData = data.jsData;\r\n                            if (!jsData) return;\r\n                            var result = MeshSDK.prototype.parseResult(undefined, jsData);\r\n                            if (!result)\r\n                                return;\r\n                            console.log('[MeshH5]', 'iFrame:', \"methodName \" + jsMethods + \" completed\", result);\r\n                            flag = true;\r\n                            _this.dispose();\r\n                            if (onComplete) {\r\n                                onComplete(null, result);\r\n                            }\r\n                        }\r\n                    }, false);\r\n                }\r\n                if (timeout > 0) {\r\n                    this._completeTimeoutId = setTimeout(function () {\r\n                        clearTimeout(_this._completeTimeoutId);\r\n                        if (onComplete && !flag) {\r\n                            _this.dispose();\r\n                            onComplete(\"methodName \" + key + \" not responding\", null);\r\n                        }\r\n                    }, timeout);\r\n                }\r\n            }\r\n\r\n            Protocal.prototype.onDone = function (callback) {\r\n                if (this._receiveName && this._completeName)\r\n                    this._onDone = callback;\r\n            };\r\n            Protocal.prototype.done = function () {\r\n                if (this._onDone) {\r\n                    this._onDone();\r\n                    this._onDone = undefined;\r\n                }\r\n            };\r\n            Protocal.prototype.dispose = function () {\r\n                this.done();\r\n                window[this._receiveName] = null;\r\n                delete window[this._receiveName];\r\n                this._receiveName = null;\r\n                window[this._completeName] = null;\r\n                delete window[this._completeName];\r\n                this._completeName = null;\r\n                clearTimeout(this._completeTimeoutId);\r\n            };\r\n            Protocal.TIME_OUT_IN_MS = 1500;\r\n            return Protocal;\r\n        }());\r\n        var Native = (function () {\r\n            function Native() {\r\n            }\r\n\r\n            Native.invoke = function (methodName, protocal, params, onComplete, onReceive, timeout\r\n            ) {\r\n                var _this = this;\r\n                if (timeout === void 0) {\r\n                    timeout = 5000;\r\n                }\r\n                var proto = this.createProtocal(methodName, params, onComplete, onReceive, timeout);\r\n                if (proto) {\r\n                    var isIframe = Utils.isIframe();\r\n                    if (Utils.isAndroid()) {\r\n                        if (isIframe) {\r\n                            console.log('android iframe');\r\n                            //向iframe发送消息\r\n                            window.parent.postMessage(JSON.stringify(params), \"*\");\r\n                        } else {\r\n                            if (protocal) {\r\n                                if (Utils.isUnity()) {\r\n                                    protocal.call(window.Unity, JSON.stringify(params));\r\n                                } else {\r\n                                    protocal.call(window.NativeBridge, JSON.stringify(params));\r\n                                }\r\n                            }\r\n                        }\r\n                    } else if (Utils.isIOS()) {\r\n                        if (isIframe) {\r\n                            console.log('ios iframe');\r\n                            //向iframe发送消息\r\n                            window.parent.postMessage(JSON.stringify(params), \"*\");\r\n                        } else {\r\n                            if (protocal) {\r\n                                protocal.postMessage(JSON.stringify(params));\r\n                            }\r\n                        }\r\n                    } else {\r\n                        if (isIframe) {\r\n                            console.log('pc iframe');\r\n                            //向iframe发送消息\r\n                            window.parent.postMessage(JSON.stringify(params), \"*\");\r\n                        } else {\r\n                            console.error('[MeshH5]', 'native:', 'invalid Android&Ios');\r\n                        }\r\n                    }\r\n                } else {\r\n                    console.error('[MeshH5]', 'native:', 'invalid protocal initliazation');\r\n                }\r\n            };\r\n            Native.createProtocal = function (methodName, params, onComplete, onReceive, timeout) {\r\n                var total = this.PROTOCAL_CACHE[methodName] || 0;\r\n                total += 1;\r\n                this.PROTOCAL_CACHE[methodName] = total;\r\n                return new Protocal(methodName, total, params, onComplete, onReceive, timeout);\r\n            };\r\n            Native._allIFrames = [];\r\n            Native._iframeId = -1;\r\n            Native.PROTOCAL_CACHE = {};\r\n            return Native;\r\n        }());\r\n        var isAndroid = function () {\r\n            var u = navigator.userAgent;\r\n            return u.indexOf('Android') > -1 || u.indexOf('Adr') > -1;\r\n        };\r\n        var isIOS = function () {\r\n            var u = navigator.userAgent;\r\n            return !!u.match(/\\(i[^;]+;( U;)? CPU.+Mac OS X/);\r\n        };\r\n        var isIframe = function () {\r\n            return window.top !== window;\r\n        };\r\n        var isUnity = function () {\r\n            return window['isUnity'];\r\n        }\r\n        var nativeCall = Native.invoke.bind(Native);\r\n        var Utils = Object.freeze({\r\n            nativeCall: nativeCall,\r\n            isAndroid: isAndroid,\r\n            isIOS: isIOS,\r\n            isIframe: isIframe,\r\n            isUnity: isUnity,\r\n        });\r\n        var MeshSDK = (function () {\r\n            function MeshSDK() {\r\n                /*注册H5接受消息*/\r\n                this._wrappedConfigParams = {\r\n                    //用户token发生变化\r\n                    'serverCodeUpdate': function (handler, result) {\r\n                        var cb = handler, data = result;\r\n                        if (cb) {\r\n                            cb(data);\r\n                        }\r\n                    },\r\n                    //用户货币发生变化\r\n                    'walletUpdate': function (handler, result) {\r\n                        var cb = handler, data = result;\r\n                        if (cb) {\r\n                            cb(data);\r\n                        }\r\n                    },\r\n                    /**\r\n                     * 游戏行为发生变化\r\n                     * 物理返回键:{\r\n                     *     type:1\r\n                     * }\r\n                     * 返回按钮显示状态:{\r\n                     *     type:2,\r\n                     *     showStatus:0 隐藏 1 显示\r\n                     * }\r\n                     * 观众人数变化:{\r\n                     *     type:3,\r\n                     *     audienceCount:1//最新观众人数\r\n                     * }\r\n                     * APP操作游戏座位\r\n                     *      1.收到sendGameAction 上/下游戏座位 type:15\r\n                     *      2.通过APP麦位上座游戏座位(按需调用)\r\n                     *      3.收到sendGameAction 同步座位信息 type:16 时，发现座位信息同麦位信息不一致时，\r\n                     *        以APP麦位信息为准，进而保证两者的一致性。(按需调用)\r\n                     * :{\r\n                     *      type:4,\r\n                     *      params:{\r\n                     *          optType:0,//0上座,1下座\r\n                     *          userId:xxxA,\r\n                     *          seat:0 // 座位位置\r\n                     *      }\r\n                     * }\r\n                     * 变更用户身份\r\n                     * {\r\n                     *     type:5,\r\n                     *     params:{\r\n                     *         role:0,//0.正常用户 1.游客  2.主持人(拥有开始游戏权限)\r\n                     *         userId:xxxA\r\n                     *     }\r\n                     * }\r\n                     * APP返回踢人操作结果\r\n                     * {\r\n                     *     type:6,\r\n                     *     params:{\r\n                     *         optResult:0,//0 踢人成功 1 踢人失败\r\n                     *         optUserId:0,//执行踢人操作用户id\r\n                     *         userId:xxxA,//被踢用户id\r\n                     *         reason:\"success\" //optResult 为1时，需要注明原因\r\n                     *     }\r\n                     * }\r\n                     * @param handler\r\n                     * @param result\r\n                     * 活动入口ICON状态 (psd):\r\n                     * {\r\n                     *     type:2001,\r\n                     *     showStatus:0 隐藏 1 显示\r\n                     *     activityIcon:'' 活动icon 外网访问地址 (120*120 px)\r\n                     * }\r\n                     *\r\n                     * 打开帮助\r\n                     * {\r\n                     *     type:2002\r\n                     * }\r\n                     *\r\n                     * 控制游戏背景音乐 0-1\r\n                     * {\r\n                     *     type:2003,\r\n                     *     bgVolume:0.5\r\n                     * }\r\n                     *\r\n                     * 控制游戏音效 0-1\r\n                     * {\r\n                     *     type:2004,\r\n                     *     soundVolume:0.5\r\n                     * }\r\n                     *\r\n                     * 麦位图标底部到屏幕顶部距离/屏幕高度\r\n                     * 数值范围 0-1\r\n                     * {\r\n                     *     type:2005,\r\n                     *     value:0.1\r\n                     * }\r\n                     *\r\n                     * 帮助按钮显示状态:{\r\n                     *     type:2006,\r\n                     *     showStatus:0 隐藏 1 显示\r\n                     * }\r\n                     *\r\n                     * musee 最小化按钮显示状态:{\r\n                     *     type:2007,\r\n                     *     showStatus:0 隐藏 1 显示\r\n                     * }\r\n                     * 客户端通知游戏最小化状态\r\n                     * {\r\n                     *     type:2008,\r\n                     *     minimizeStatus:0 取消最小化 1 启用最小化\r\n                     * }\r\n                     * tietie 控制游戏内的按钮点击逻辑\r\n                     * {\r\n                     *   type:2009,\r\n                     *   params:{\r\n                     *       \"subAction\":2009,\r\n                     *       \"subMsg\":{}\r\n                     *   }\r\n                     * }\r\n                     * 切换货币\r\n                     * {\r\n                     *     type:2010,\r\n                     *     params:{\r\n                     *          appId:xxx,\r\n                     *          coinType:1\r\n                     *      }\r\n                     * }\r\n                     * 隐藏设置按钮和商城按钮\r\n                     * {\r\n                     *     type:2011,\r\n                     *     params:{\r\n                     *         \"settingButton\": false,  //设置按钮是否隐藏（true显示，false隐藏，默认显示）\r\n                     *         \"mallButton\": false, //商城按钮是否隐藏（true显示，false隐藏，默认显示）\r\n                     *         \"subMsg\": {} //保留字段可不传\r\n                     *     }\r\n                     * }\r\n                     * 查询当前音乐和音效的状态\r\n                     * {\r\n                     *     type:2012,\r\n                     *     params:{}\r\n                     * }\r\n                     * 直接触发游戏内的事件（拉起商城、拉起规则页）\r\n                     * {\r\n                     *     type:2013,\r\n                     *     params:{\r\n                     *         //1001 拉起规则页面  //1002拉起商城页面 //1003设置音效和音乐相关的\r\n                     *         \"subAction\": 1001,\r\n                     *         \"subMsg\": {\r\n                     *                     //在subAction为 1003 的时候传bgmStatus、seStatus字段\r\n                     *                     \"bgmStatus\": 1, //背景音乐 bgmStatus 0 关闭 1 打开\r\n                     *                     \"seStatus\": 1 //音效 seStatus 0 关闭 1 打开\r\n                     *                   }\r\n                     *     }\r\n                     * }\r\n                     */\r\n\r\n                    'gameActionUpdate': function (handler, result) {\r\n                        var cb = handler, data = result;\r\n                        if (cb) {\r\n                            cb(data);\r\n                        }\r\n                    },\r\n                    /**\r\n                     * 声音发生变化\r\n                     * 背景音乐 bgmStatus 0 关闭 1 打开\r\n                     * 音效 seStatus 0 关闭 1 打开\r\n                     * {bgmStatus:1,seStatus:1}\r\n                     * @param handler\r\n                     * @param result\r\n                     */\r\n                    'soundUpdate': function (handler, result) {\r\n                        var cb = handler, data = result;\r\n                        if (cb) {\r\n                            cb(data);\r\n                        }\r\n                    },\r\n                    /**\r\n                     * musee 恢复游戏面板\r\n                     * 游戏 恢复音量\r\n                     */\r\n                    'openFoldGame': function (handler, result) {\r\n                        var cb = handler, data = result;\r\n                        if (cb) {\r\n                            cb(data);\r\n                        }\r\n                    },\r\n                }\r\n            }\r\n\r\n            /**\r\n             * 创建协议\r\n             * @param fnName 方法名\r\n             */\r\n            MeshSDK.prototype.createProtocol = function (fnName) {\r\n                var isIframe = Utils.isIframe();\r\n                if (Utils.isAndroid()) {\r\n                    if (isIframe) {\r\n                        console.log('android iframe');\r\n                    } else if (Utils.isUnity()) {\r\n                        if (window.Unity && window.Unity['call']) {\r\n                            return window.Unity['call'];\r\n                        } else {\r\n                            return null;\r\n                        }\r\n                    } else {\r\n                        if (window.NativeBridge && window.NativeBridge[fnName]) {\r\n                            return window.NativeBridge[fnName];\r\n                        } else {\r\n                            return null;\r\n                        }\r\n                    }\r\n                } else if (Utils.isIOS()) {\r\n                    if (isIframe) {\r\n                        console.log('ios iframe');\r\n                    } else if (Utils.isUnity()) {\r\n                        if (window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers['unityControl']) {\r\n                            return window.webkit.messageHandlers['unityControl'];\r\n                        } else {\r\n                            return null;\r\n                        }\r\n                    } else {\r\n                        if (window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers[fnName]) {\r\n                            return window.webkit.messageHandlers[fnName];\r\n                        } else {\r\n                            return null;\r\n                        }\r\n                    }\r\n                } else {\r\n                    if (isIframe) {\r\n                        console.log('pc iframe');\r\n                    } else {\r\n                        console.error('[Mesh-H5-sdk]', \"createProtocol 既不是Andoroid也不是Ios\");\r\n                    }\r\n                }\r\n            };\r\n            /**\r\n             * 通用的处理结果的方法\r\n             * @param err 错误信息\r\n             * @param result 成功的结果，一般是字符串，需要JSON.parse处理\r\n             * @returns {*}\r\n             */\r\n            MeshSDK.prototype.parseResult = function (err, result) {\r\n                if (err)\r\n                    throw err;\r\n                else if (result == undefined || result == null)\r\n                    throw 'empty result';\r\n                else {\r\n                    var parsed = void 0;\r\n                    if (typeof result == 'string') {\r\n                        parsed = JSON.parse(result);\r\n                    } else if (typeof result == 'object') {\r\n                        parsed = result;\r\n                    }\r\n                    if (!parsed)\r\n                        throw 'failed to parse';\r\n                    return parsed;\r\n                }\r\n            };\r\n            /**\r\n             * 辅助方法，用来方便创建统一的协议回调处理逻辑\r\n             * @param resolve 通过 promise 传入的 resolve 回调\r\n             * @param reject 通过 promise 传入的 reject 回调\r\n             */\r\n            MeshSDK.prototype.usePromiseResult = function (resolve, reject) {\r\n                var _this = this;\r\n                return function (err, result) {\r\n                    try {\r\n                        var parsed = _this.parseResult(err, result);\r\n                        if (parsed) {\r\n                            resolve(parsed);\r\n                        }\r\n                    } catch (e) {\r\n                        reject(e);\r\n                    }\r\n                };\r\n            };\r\n            /**\r\n             * 发送协议的接口。\r\n             * 一般情况下无需主动调用\r\n             * @param params 构造出来的协议参数，包括协议名和参数等\r\n             * @param onComplete 完成回调\r\n             * @param onReceive 接收到回调\r\n             */\r\n            MeshSDK.prototype.send = function (params, onComplete, onReceive) {\r\n                if (!params)\r\n                    return;\r\n                Utils.nativeCall(params.methodName, params.protocol, params.params, onComplete, onReceive, params.timeout);\r\n            };\r\n            /**\r\n             * 通用的请求协议的方法。可以参照协议文档进行调用。\r\n             * @param methodName 方法函数名\r\n             * @param params 协议参数，JSON Object\r\n             * @param onComplete 协议完成回调方法\r\n             * @param onReceived 协议收到回调方法（ACK）\r\n             * @param timeout 超时，单位毫秒。默认 0 为不超时。超时设置的用处是清空回调等资源。\r\n             */\r\n            MeshSDK.prototype.request = function (methodName, params, onComplete, onReceived, timeout) {\r\n                if (timeout === void 0) {\r\n                    timeout = 0;\r\n                }\r\n                this.send({\r\n                    methodName: methodName,\r\n                    protocol: this.createProtocol(methodName),\r\n                    params: params,\r\n                    timeout: timeout,\r\n                }, this.usePromiseResult(onComplete, function (reason) {\r\n                    return console.error('[Mesh-H5-sdk]', \"methodName \" + methodName + \" error: \" + reason);\r\n                }), onReceived);\r\n            };\r\n            MeshSDK.prototype.regReceiveMessage = function (params) {\r\n                var _this = this;\r\n                var wrapped = {};\r\n                for (var key in params) {\r\n                    if (!!_this._wrappedConfigParams[key]) {\r\n                        wrapped[key] = key;\r\n                    }\r\n                }\r\n                if (Utils.isIframe()) {\r\n                    window.addEventListener('message', e => {\r\n                        if (e.data) {\r\n                            var data = e.data;\r\n                            var jsMethods = data.jsMethods;\r\n                            if (!params[jsMethods])\r\n                                return;\r\n                            var jsData = data.jsData;\r\n                            if (!jsData) return;\r\n                            var result = MeshSDK.prototype.parseResult(undefined, jsData);\r\n                            if (!result)\r\n                                return;\r\n                            if (typeof params[jsMethods] === 'function' || !!_this._wrappedConfigParams[jsMethods]) {\r\n                                _this._wrappedConfigParams[jsMethods](params[jsMethods], result);\r\n                            }\r\n                        }\r\n                    });\r\n                } else {\r\n                    // 把包裹后的方法注册到 window 上\r\n                    Object.keys(wrapped).forEach(function (key) {\r\n                        var name = wrapped[key];\r\n                        if (name) {\r\n                            if (name == 'openFoldGame') {\r\n                                window[name] = function (str) {\r\n                                    if (typeof params[key] === 'function' || !!_this._wrappedConfigParams[key]) {\r\n                                        _this._wrappedConfigParams[key](params[key], {});\r\n                                    }\r\n                                }\r\n                            } else {\r\n                                window[name] = function (str) {\r\n                                    var result = _this.parseResult(undefined, str);\r\n                                    if (!result)\r\n                                        return;\r\n                                    if (!params[key])\r\n                                        return;\r\n                                    if (typeof params[key] === 'function' || !!_this._wrappedConfigParams[key]) {\r\n                                        _this._wrappedConfigParams[key](params[key], result);\r\n                                    }\r\n                                };\r\n                            }\r\n                        }\r\n                    });\r\n                }\r\n                return wrapped;\r\n            };\r\n            /**\r\n             * 获取版本号(H5SDK 协议)\r\n             * @returns {Promise<unknown>}\r\n             */\r\n            MeshSDK.prototype.getVersion = function () {\r\n                return new Promise(function (resolve) {\r\n                    var data = \"1.0.0\";\r\n                    resolve(data);\r\n                });\r\n            };\r\n            /**\r\n             * 获取用户信息配置\r\n             * @returns {Promise<unknown>}\r\n             */\r\n            MeshSDK.prototype.getConfig = function () {\r\n                var _this = this;\r\n                return new Promise(function (resolve, reject) {\r\n                    _this.request(\"getConfig\", {}, function (data) {\r\n                        resolve(data);\r\n                    }, reject);\r\n                });\r\n            };\r\n            /**\r\n             * 销毁游戏\r\n             * @returns {Promise<unknown>}\r\n             */\r\n            MeshSDK.prototype.destroy = function () {\r\n                var _this = this;\r\n                return new Promise(function (resolve, reject) {\r\n                    _this.request(\"destroy\", {}, function (data) {\r\n                        resolve(data);\r\n                    }, reject);\r\n                });\r\n            };\r\n            /**\r\n             * 提示余额不足，拉起充值商城\r\n             * type 0：余额不足 1：余额充足，主动调用\r\n             * gameId：游戏Id\r\n             * paymentType\r\n             *   trovo: 1: mana; 2: game coin\r\n             *   musee: 1:黄钻 2:蓝钻\r\n             * inThisRound: 本轮是否参与 0:未参与  1:参与\r\n             * @returns {Promise<unknown>}\r\n             */\r\n            MeshSDK.prototype.gameRecharge = function (params) {\r\n                var _this = this;\r\n                return new Promise(function (resolve, reject) {\r\n                    _this.request(\"gameRecharge\", params ? params : {}, function (data) {\r\n                        resolve(data);\r\n                    }, reject);\r\n                });\r\n            };\r\n            /**\r\n             * 游戏加载完毕\r\n             * @returns {Promise<unknown>}\r\n             */\r\n            MeshSDK.prototype.gameLoaded = function () {\r\n                var _this = this;\r\n                return new Promise(function (resolve, reject) {\r\n                    _this.request(\"gameLoaded\", {}, function (data) {\r\n                        resolve(data);\r\n                    }, reject);\r\n                });\r\n            };\r\n            /**\r\n             * musee\r\n             * 最小化按钮\r\n             * 调用时游戏执行静音操作(不再执行，只是通知到最小化操作)\r\n             * @returns {Promise<unknown>}\r\n             */\r\n            MeshSDK.prototype.foldGame = function () {\r\n                var _this = this;\r\n                return new Promise(function (resolve, reject) {\r\n                    _this.request(\"foldGame\", {}, function (data) {\r\n                        resolve(data);\r\n                    }, reject);\r\n                });\r\n            };\r\n\r\n            /**\r\n             * 上报游戏行为\r\n             * 游戏加载进度  type:1, params: {progress: 10}\r\n             * 进入/打开游戏主页 type:2\r\n             * 游戏房间号 type:3, params: {gameRoomId: 'xxxx'}\r\n             * 网络延迟程度\r\n             *   type:4,\r\n             *   params:{\r\n             *       status:1,延迟程度(1:良好 0~100ms,2:普通 100~200ms,3:较差 200~300ms,4:很差 300~600ms,5:极差 >600ms,6:断开网络 9999ms)\r\n             *       latency:100,延迟时间，单位ms\r\n             *   }\r\n             * 帧率\r\n             *   type:5,\r\n             *   params:{\r\n             *       status:1,帧率评级(1:超高 >60fps,2:高 50~60fps,3:中 30~50fps,4:较低 20~30fps,5:低 <20fps)\r\n             *       fps:55,帧率值\r\n             *   }\r\n             * 拉起观众列表 type:6\r\n             * 拉起玩家资料卡 type:7,params:{userId:xxx}\r\n             * 获取用户是否已关注\r\n             *    type:8,params:{userIds:[userId1xx,userId2xx]}\r\n             *    返回数据：{\r\n             *              userIds:{userId1xx:true,userId2xx:false}//true为已关注\r\n             *            }\r\n             * 关注用户\r\n             *    type:9,params:{userId:xxx}\r\n             *    返回数据：{\r\n             *        status:0,//0 为关注成功,1 为关注失败\r\n             *        userId:xxx,\r\n             *    }\r\n             * 拉起分享面板 type:10\r\n             * 回到APP准备页(组队页) type:11\r\n             * 点击右上角关闭按钮 type:12\r\n             * 游戏开始 type:13\r\n             * 游戏结束 type:14\r\n             * 上/下游戏座位\r\n             *    type:15\r\n             *    params:{\r\n             *        optType:0,//0上座,1下座\r\n             *        userId:xxxA,\r\n             *        seat:0 // 座位位置\r\n             *    }\r\n             * 同步座位信息\r\n             *    type:16,\r\n             *    params:{\r\n             *      seats:[\r\n             *          {\r\n             *              userId:xxxA,\r\n             *              seat:0, //座位位置\r\n             *              prepareStatus:0 //0未准备,1已准备\r\n             *          },\r\n             *          {\r\n             *              userId:xxxB,\r\n             *              seat:1,\r\n             *              prepareStatus:1,\r\n             *          }\r\n             *      ]\r\n             *    }\r\n             * 游戏发起踢人请求\r\n             *    type:17,\r\n             *    params:{\r\n             *      userId:xxxA,\r\n             *      seat:0 //座位位置\r\n             *    }\r\n             * 游戏上座失败\r\n             *    type:18,\r\n             *    params:{\r\n             *        userId:xxxA,\r\n             *    }\r\n             * 是否可切换货币，游戏打开时状态为不可以切换\r\n             *    type:19,\r\n             *    params:{\r\n             *        isCanChange: 0 不可切换 1 可切换\r\n             *        currencyCoinType:1,//当前货币类型\r\n             *    }\r\n             * 语聊房游戏准备完成，通知 app 可以发起自动上座 type:20\r\n             * 返回游戏内的音乐和音效状态（一般是收到gameActionUpdate中的 type ==2012 的时候发起回调）\r\n             *    type: 21,\r\n             *    params: {\r\n             *         \"bgmStatus\": 1, //背景音乐 bgmStatus 0 关闭 1 打开\r\n             *         \"seStatus\": 1   //音效 seStatus 0 关闭 1 打开\r\n             *    }\r\n             * 触发活动事件 psd专享()\r\n             *    type:2001\r\n             * 帮助页准备完毕\r\n             *    type:2002\r\n             *    params:{\r\n             *        url:\"\"\r\n             *    }\r\n             * 通知miya H5商城事件\r\n             *  type:2003\r\n             *  params:{\r\n             *        env: 1 //1测试,2正式\r\n             *        gameId:xxx\r\n             *    }\r\n             * 同步APP 麦克风和扬声器开关 游戏内通知 (比如拆弹猫、谁是卧底等）\r\n             * {\r\n             *     \"type\":3001,\r\n             *     \"params\": {\r\n             *         \"users\": [\r\n             *                 {\r\n             *                     \"userId:\": \"xxxxx\",   //用户 id\r\n             *                     \"microphone\": false, //是否开启麦克风  false关闭  true开启\r\n             *                     \"useSpeaker\": false //是否开启扬声器\r\n             *                 },\r\n             *                 {\r\n             *                     \"userId:\": \"xxxxx\",\r\n             *                     \"microphone\": true,\r\n             *                     \"useSpeaker\": true\r\n             *                 }\r\n             *             ]\r\n             *     }\r\n             * }\r\n             *\r\n             * {\r\n             *   \"type\": 3002,\r\n             *   \"params\": {\r\n             *     \"state\": 0 //0 代表点击的结算页面的关闭按钮   1 代表点击结算页面的再来一局按钮\r\n             *   }\r\n             * }\r\n             * @returns {Promise<unknown>}\r\n             */\r\n            MeshSDK.prototype.sendGameAction = function (params) {\r\n                var _this = this;\r\n                return new Promise(function (resolve, reject) {\r\n                    _this.request(\"sendGameAction\", params, function (data) {\r\n                        resolve(data);\r\n                    }, reject);\r\n                });\r\n            };\r\n\r\n            /**\r\n             * 是否支持isSupportRechargeV2\r\n             * trovo 使用\r\n             * @returns\r\n             * isSupport boolean\r\n             * true 去掉之前的Toast提示，调用gameRecharge paymentType =1\r\n             * false 保留Toast\r\n             */\r\n            MeshSDK.prototype.isSupportRechargeV2 = function () {\r\n                var _this = this;\r\n                return new Promise(function (resolve) {\r\n                    var isSupport = false;\r\n                    if (Utils.isAndroid()) {\r\n                        if (window.NativeBridge['versionSupportApis']) {\r\n                            _this.request(\"versionSupportApis\", {}, function (data) {\r\n                                if (data.apis && data.apis.includes('gameRechargeV2_trovo')) {\r\n                                    isSupport = true;\r\n                                }\r\n                                resolve({'isSupport': isSupport});\r\n                            });\r\n                        } else {\r\n                            resolve({'isSupport': isSupport});\r\n                        }\r\n                    } else if (Utils.isIOS()) {\r\n                        if (window.webkit.messageHandlers['versionSupportApis']) {\r\n                            _this.request(\"versionSupportApis\", {}, function (data) {\r\n                                if (data.apis && data.apis.includes('gameRechargeV2_trovo')) {\r\n                                    isSupport = true;\r\n                                }\r\n                                resolve({'isSupport': isSupport});\r\n                            });\r\n                        } else {\r\n                            resolve({'isSupport': isSupport});\r\n                        }\r\n                    } else {\r\n                        isSupport = true;\r\n                        resolve({'isSupport': isSupport});\r\n                    }\r\n                });\r\n            };\r\n\r\n            return MeshSDK;\r\n        }());\r\n        var meshSDK = new MeshSDK();\r\n        exports.meshSDK = meshSDK;\r\n        Object.defineProperty(exports, '__esModule', {value: true});\r\n    }));"]}