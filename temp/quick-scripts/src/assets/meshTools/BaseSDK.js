"use strict";
cc._RF.push(module, 'b65e79RMudAKablFCxs6O0q', 'BaseSDK');
// meshTools/BaseSDK.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseSDK = void 0;
var BaseSDK = /** @class */ (function () {
    function BaseSDK() {
    }
    BaseSDK.prototype.AddAPPEvent = function () { };
    BaseSDK.prototype.HideLoading = function () { };
    BaseSDK.prototype.CloseWebView = function () { };
    BaseSDK.prototype.ShowAppShop = function (type) { };
    BaseSDK.prototype.GetAppVersion = function (callback, errCallback) {
        callback && callback("0.0.0");
    };
    BaseSDK.prototype.GetConfig = function (callback) {
        callback && callback(null);
    };
    return BaseSDK;
}());
exports.BaseSDK = BaseSDK;

cc._RF.pop();