{"version": 3, "sources": ["assets/meshTools/Singleton.ts"], "names": [], "mappings": ";;;;;;;AACA;IAAA;IAWA,CAAC;IATiB,qBAAW,GAAzB;QAEI,IAAU,IAAK,CAAC,KAAK,IAAI,IAAI,EAC7B;YACU,IAAK,CAAC,KAAK,GAAG,IAAI,IAAI,EAAE,CAAC;SAClC;QAED,OAAa,IAAK,CAAC,KAAK,CAAC;IAC7B,CAAC;IACL,gBAAC;AAAD,CAXA,AAWC,IAAA;AAXY,8BAAS", "file": "", "sourceRoot": "/", "sourcesContent": ["\nexport class Singleton\n{\n    public static GetInstance<T>(this: new() => T): T\n    {\n        if ((<any>this)._inst == null)\n        {\n            (<any>this)._inst = new this();\n        }\n\n        return (<any>this)._inst;\n    }\n}\n"]}