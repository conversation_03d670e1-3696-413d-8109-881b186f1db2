{"version": 3, "sources": ["assets/meshTools/MeshTools.ts"], "names": [], "mappings": ";;;;;;;AAAA,2CAA0C;AAE1C;IAAA;IAUA,CAAC;IAJG,sBAAkB,oBAAO;QAJzB;;;WAGG;aACH;YAEI,OAAO,iBAAO,CAAC,WAAW,EAAE,CAAC;QACjC,CAAC;;;OAAA;IACL,gBAAC;AAAD,CAVA,AAUC,IAAA;AAVY,8BAAS", "file": "", "sourceRoot": "/", "sourcesContent": ["import { Publish } from \"./tools/Publish\";\r\n\r\nexport class MeshTools\r\n{\r\n    /**\r\n     * 游戏发布配置\r\n     * @type {Publish}\r\n     */\r\n    public static get Publish (): Publish\r\n    {\r\n        return Publish.GetInstance();\r\n    }\r\n}"]}