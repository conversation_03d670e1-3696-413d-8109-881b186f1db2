# 游戏开始和回合开始动画功能

## 功能概述

根据需求，我们实现了两个动画节点：

1. **游戏开始节点** - 在收到GameStart消息后显示，收到NoticeRoundStart时隐藏
2. **回合开始节点** - 在NoticeActionDisplay倒计时还剩2秒时从屏幕左边移动到中间，然后移出

## 实现细节

### 1. 游戏开始动画

**触发时机：** 收到GameStart消息后
**隐藏时机：** 收到NoticeRoundStart消息时
**动画效果：** 放大展示 → 缩小隐藏

**动画流程：**
- 节点从scale=0开始
- 0.3秒内放大到scale=1.2（使用backOut缓动）
- 0.2秒内缩小到scale=1.0（使用backOut缓动）
- 隐藏时：0.3秒内缩小到scale=0并透明度变为0（使用backIn缓动）

### 2. 回合开始动画

**触发时机：** NoticeActionDisplay倒计时还剩2秒时
**动画效果：** 从屏幕左边移入 → 中间展示 → 移出到右边
**展示位置：** (0, -1)

**动画流程：**
- 节点初始位置在屏幕左边外：(-screenWidth, -1)
- 0.5秒移动到中间位置：(0, -1)（使用quartOut缓动）
- 停留1秒展示
- 0.5秒移动到屏幕右边外：(screenWidth, -1)（使用quartIn缓动）
- 总时长：2秒，确保在NoticeRoundStart之前完成

## 代码修改

### GamePageController.ts 主要修改：

1. **添加节点属性：**
   ```typescript
   @property(cc.Node)
   gameStartNode: cc.Node = null // 游戏开始节点

   @property(cc.Node)
   roundStartNode: cc.Node = null // 回合开始节点
   ```

2. **在onGameStart中调用游戏开始动画：**
   ```typescript
   // 显示游戏开始节点动画
   this.showGameStartAnimation();
   ```

3. **在onNoticeRoundStart中隐藏游戏开始动画：**
   ```typescript
   // 隐藏游戏开始节点
   this.hideGameStartAnimation();
   ```

4. **在handleCountdownBasedDisplay中添加回合开始动画：**
   ```typescript
   } else if (remainingSeconds === 2) {
       // 2s时：显示回合开始节点动画
       this.showRoundStartAnimation();
   }
   ```

5. **添加动画方法：**
   - `initializeAnimationNodes()` - 初始化节点
   - `createGameStartNode()` - 创建游戏开始节点
   - `createRoundStartNode()` - 创建回合开始节点
   - `showGameStartAnimation()` - 显示游戏开始动画
   - `hideGameStartAnimation()` - 隐藏游戏开始动画
   - `showRoundStartAnimation()` - 显示回合开始动画

## 资源文件

动画使用的图片资源：
- 游戏开始：`开始游戏@2x-2.png`
- 回合开始：`<EMAIL>`

## 测试功能

在NoticeRoundStartTest.ts中添加了测试按钮：
- `testGameStartAnimationBtn` - 测试游戏开始动画
- `testRoundStartAnimationBtn` - 测试回合开始动画

## 使用方法

1. 在Cocos Creator编辑器中打开游戏场景
2. 在GamePageController组件中设置gameStartNode和roundStartNode属性（可选，代码会自动创建）
3. 运行游戏，动画会在相应的消息触发时自动播放

## 注意事项

1. 如果在编辑器中没有设置节点，代码会自动创建节点并加载相应的图片资源
2. 节点的zIndex设置为1000，确保在最上层显示
3. 回合开始动画的总时长为2秒，确保在NoticeRoundStart消息到达前完成
4. 所有动画都使用了适当的缓动函数，提供流畅的视觉效果
